(self.webpackChunknew_charge = self.webpackChunknew_charge || []).push([
    [547],
    {
        68580: function (e, t, n) {
            var i = n(2880);
            (i = 'string' == typeof (i = i.__esModule ? i.default : i) ? [[e.id, i, '']] : i)
                .locals && (e.exports = i.locals),
                (0, n(69333).A)('1ef52776', i, !0, { sourceMap: !1, shadowMode: !1 });
        },
        11556: function (e, t, n) {
            n.r(t),
                n.d(t, {
                    default: function () {
                        return f;
                    },
                }),
                n(79432);
            var i = n(23074),
                o = n(31554),
                r = n(81940),
                a = (n(28706), n(26099), n(94084)),
                u = n(14116),
                s = n(19406),
                c = n(62090),
                d = ((t = n(55373)), n.n(t)),
                l = n(59489),
                f =
                    ((t = n(45013)),
                    (t = {
                        props: {
                            standType: { type: String, default: '' },
                            hideBg: { type: Boolean, default: !1 },
                            sourceInfo: { type: Object },
                        },
                        data: function () {
                            return {
                                VIP_GUIDE_ASSEMBLY_TYPES: l.S$,
                                IMG_URL: c.sE,
                                guideInfo: null,
                            };
                        },
                        created: function () {
                            this.sourceInfo && (this.guideInfo = this.sourceInfo);
                        },
                        computed: {
                            guideTitle: function () {
                                return this.guideInfo ? (0, a._Z)(this.guideInfo.mainContent) : [];
                            },
                            paymemberBtnTitle: function () {
                                return (this.guideInfo && this.guideInfo.btnContent) || '';
                            },
                            guideSubTitle: function () {
                                return this.guideInfo ? (0, a._Z)(this.guideInfo.subContent) : [];
                            },
                        },
                        methods: (0, r.A)(
                            (0, r.A)({}, (0, t.mapActions)('common', ['getLocation'])),
                            {},
                            {
                                initData: function () {
                                    var e = arguments,
                                        t = this;
                                    return (0, o.A)(
                                        (0, i.A)().mark(function n() {
                                            var o, c, d, l;
                                            return (0, i.A)().wrap(
                                                function (n) {
                                                    for (;;)
                                                        switch ((n.prev = n.next)) {
                                                            case 0:
                                                                if (
                                                                    ((o =
                                                                        0 < e.length &&
                                                                        void 0 !== e[0]
                                                                            ? e[0]
                                                                            : {}),
                                                                    (n.prev = 1),
                                                                    t.isShowVipChannelFlag)
                                                                ) {
                                                                    n.next = 4;
                                                                    break;
                                                                }
                                                                return n.abrupt('return');
                                                            case 4:
                                                                return (
                                                                    (n.next = 6),
                                                                    t.getLocation({ city: !0 })
                                                                );
                                                            case 6:
                                                                return (
                                                                    (c = n.sent),
                                                                    (c = (0, r.A)(
                                                                        {
                                                                            standType: t.standType,
                                                                            city: c.city,
                                                                            lat: c.latitude,
                                                                            lon: c.longitude,
                                                                        },
                                                                        o,
                                                                    )),
                                                                    (n.next = 10),
                                                                    (0, s.KX)(c)
                                                                );
                                                            case 10:
                                                                return (
                                                                    (c = n.sent),
                                                                    (d = c.data),
                                                                    (l = d.bdResponse),
                                                                    (t.guideInfo = d),
                                                                    l &&
                                                                        ((0, u.Ed)(t.$biz, !0)(
                                                                            l,
                                                                            {},
                                                                        ),
                                                                        (0, a.uk)('StandExpo', l)),
                                                                    n.abrupt('return', d)
                                                                );
                                                            case 18:
                                                                return (
                                                                    (n.prev = 18),
                                                                    (n.t0 = n.catch(1)),
                                                                    n.abrupt(
                                                                        'return',
                                                                        Promise.reject(n.t0),
                                                                    )
                                                                );
                                                            case 21:
                                                            case 'end':
                                                                return n.stop();
                                                        }
                                                },
                                                n,
                                                null,
                                                [[1, 18]],
                                            );
                                        }),
                                    )();
                                },
                                getJumpVipPath: function () {
                                    var e =
                                            0 < arguments.length && void 0 !== arguments[0]
                                                ? arguments[0]
                                                : {},
                                        t = '',
                                        n = (e.actSubType, e.btnPageType),
                                        i = e.policyId,
                                        o = ((i = void 0 === i ? '' : i), '');
                                    switch (this.standType) {
                                        case l.S$.ORDER_DETAIL:
                                            o = '订单详情页';
                                            break;
                                        case l.S$.CHARGE:
                                            o = '充电页';
                                            break;
                                        case l.S$.TRANSACTION:
                                            o = '下单页';
                                    }
                                    return (
                                        (i = {
                                            sourceType: 'vipGuideBar',
                                            policyId: i,
                                            sourceEvent: '{VipActPos_button_Click}|{'
                                                .concat(
                                                    o,
                                                    '会员导购组件点击}|{{策略ID,activ_type},{',
                                                )
                                                .concat(i, ',会员导购组件}}'),
                                            CrowdPortraitId: e.crowdInfo || '',
                                        }),
                                        (e = d().stringify(i)),
                                        '01' === n
                                            ? (t = '/pagesPaymember/paymember/pay?'.concat(e))
                                            : '02' === n &&
                                              (t = '/pagesPaymember/paymember/details?'.concat(e)),
                                        t
                                    );
                                },
                                gotoPaymember: function () {
                                    var e = this;
                                    return (0, o.A)(
                                        (0, i.A)().mark(function t() {
                                            var n;
                                            return (0, i.A)().wrap(
                                                function (t) {
                                                    for (;;)
                                                        switch ((t.prev = t.next)) {
                                                            case 0:
                                                                if (((t.prev = 0), e.guideInfo))
                                                                    return (
                                                                        (n = (0, r.A)(
                                                                            {},
                                                                            e.guideInfo,
                                                                        )),
                                                                        (n = e.getJumpVipPath(n)),
                                                                        (0, a.uk)(
                                                                            'PutClick',
                                                                            (0, r.A)(
                                                                                (0, r.A)(
                                                                                    {},
                                                                                    e.guideInfo,
                                                                                ),
                                                                                e.guideInfo
                                                                                    .bdResponse ||
                                                                                    {},
                                                                            ),
                                                                        ),
                                                                        (t.next = 7),
                                                                        (0, a.SK)(200)
                                                                    );
                                                                t.next = 8;
                                                                break;
                                                            case 7:
                                                                n && (0, a.ZK)(n);
                                                            case 8:
                                                                return t.abrupt('return');
                                                            case 11:
                                                                return (
                                                                    (t.prev = 11),
                                                                    (t.t0 = t.catch(0)),
                                                                    t.abrupt(
                                                                        'return',
                                                                        Promise.reject(t.t0),
                                                                    )
                                                                );
                                                            case 14:
                                                            case 'end':
                                                                return t.stop();
                                                        }
                                                },
                                                t,
                                                null,
                                                [[0, 11]],
                                            );
                                        }),
                                    )();
                                },
                            },
                        ),
                        watch: {
                            sourceInfo: function (e) {
                                (0, a.LK)(e) || (this.guideInfo = e);
                            },
                        },
                    }),
                    (n = (n(68580), n(18535))),
                    (t = (0, n.A)(
                        t,
                        function () {
                            var e = this,
                                t = e.$createElement,
                                n = e._self._c || t;
                            return e.isShowVipChannelFlag && e.guideInfo
                                ? n(
                                      'v-uni-view',
                                      {
                                          class: ['paymember-info-bar'],
                                          style: {
                                              backgroundImage:
                                                  !e.hideBg && e.guideInfo && e.guideInfo.bgImg
                                                      ? 'url(' + e.guideInfo.bgImg + ')'
                                                      : '',
                                          },
                                      },
                                      [
                                          e.guideInfo && e.guideInfo.littleIcon
                                              ? n(
                                                    'v-uni-view',
                                                    { staticClass: 'paymember-info-logo' },
                                                    [
                                                        n('v-uni-image', {
                                                            staticClass: 'logo-img',
                                                            attrs: { src: e.guideInfo.littleIcon },
                                                        }),
                                                    ],
                                                    1,
                                                )
                                              : e._e(),
                                          n(
                                              'v-uni-view',
                                              {
                                                  staticClass: 'paymember-info-title',
                                                  style: {
                                                      color:
                                                          e.guideInfo &&
                                                          e.guideInfo.contentDefaultColor,
                                                  },
                                              },
                                              [
                                                  0 < e.guideTitle.length
                                                      ? n(
                                                            'v-uni-view',
                                                            { class: ['info-title'] },
                                                            [
                                                                e._l(e.guideTitle, function (t, i) {
                                                                    return [
                                                                        n(
                                                                            'v-uni-text',
                                                                            {
                                                                                key: i,
                                                                                style: {
                                                                                    color: t.light
                                                                                        ? e.guideInfo &&
                                                                                          e
                                                                                              .guideInfo
                                                                                              .contentHighColor
                                                                                        : '',
                                                                                },
                                                                            },
                                                                            [
                                                                                e._v(
                                                                                    e._s(
                                                                                        t.result ||
                                                                                            '',
                                                                                    ),
                                                                                ),
                                                                            ],
                                                                        ),
                                                                    ];
                                                                }),
                                                            ],
                                                            2,
                                                        )
                                                      : e._e(),
                                                  0 < e.guideSubTitle.length
                                                      ? n(
                                                            'v-uni-view',
                                                            {
                                                                staticClass: 'info-remark',
                                                                style: {
                                                                    color:
                                                                        e.guideInfo &&
                                                                        e.guideInfo
                                                                            .subContentDefaultColor,
                                                                },
                                                            },
                                                            [
                                                                e._l(
                                                                    e.guideSubTitle,
                                                                    function (t, i) {
                                                                        return [
                                                                            n(
                                                                                'v-uni-text',
                                                                                {
                                                                                    key: i,
                                                                                    style: {
                                                                                        color: t.light
                                                                                            ? e.guideInfo &&
                                                                                              e
                                                                                                  .guideInfo
                                                                                                  .subContentHighColor
                                                                                            : '',
                                                                                    },
                                                                                },
                                                                                [
                                                                                    e._v(
                                                                                        e._s(
                                                                                            t.label ||
                                                                                                '',
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                            ),
                                                                        ];
                                                                    },
                                                                ),
                                                            ],
                                                            2,
                                                        )
                                                      : e._e(),
                                              ],
                                              1,
                                          ),
                                          n(
                                              'v-uni-view',
                                              {
                                                  class: ['paymember-info-btn'],
                                                  style: {
                                                      color:
                                                          e.guideInfo &&
                                                          e.guideInfo.btnContentColor,
                                                      backgroundImage:
                                                          e.guideInfo && e.guideInfo.btnImg
                                                              ? 'url(' + e.guideInfo.btnImg + ')'
                                                              : '',
                                                  },
                                                  on: {
                                                      click: function (t) {
                                                          t.stopPropagation(),
                                                              (arguments[0] = t =
                                                                  e.$handleEvent(t)),
                                                              e.gotoPaymember.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                  },
                                              },
                                              [e._v(e._s(e.paymemberBtnTitle))],
                                          ),
                                      ],
                                      1,
                                  )
                                : e._e();
                        },
                        [],
                        !1,
                        null,
                        '06d48b72',
                        null,
                        !1,
                        void 0,
                        void 0,
                    ).exports),
                    (0, n.A)(
                        {
                            components: { VipGuideBar: t },
                            data: function () {
                                return { guideList: null };
                            },
                            onLoad: function (e) {
                                e = e.guideList;
                                e && (this.guideList = JSON.parse(e)),
                                    'public' === c.Kn &&
                                        ((e = window.sessionStorage.getItem('xdt_vipGuide')),
                                        (0, a.g9)(e) || (this.guideList = JSON.parse(e)));
                            },
                        },
                        function () {
                            var e = this,
                                t = e.$createElement,
                                n = e._self._c || t;
                            return n(
                                'v-uni-view',
                                e._l(e.guideList, function (e, t) {
                                    return n(
                                        'v-uni-view',
                                        { key: t, staticClass: 'mg-t-20' },
                                        [n('VipGuideBar', { attrs: { sourceInfo: e } })],
                                        1,
                                    );
                                }),
                                1,
                            );
                        },
                        [],
                        !1,
                        null,
                        '28ab306e',
                        null,
                        !1,
                        void 0,
                        void 0,
                    ).exports);
        },
        2880: function (e, t, n) {
            n.r(t);
            var i = n(45522),
                o = ((i = n.n(i)), n(67643));
            n = n.n(o)()(i());
            n.push([
                e.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.paymember-info-bar[data-v-06d48b72]{padding:%?15?% %?18?%;display:flex;align-items:center;border-radius:%?22?%;background-size:cover;background-position:50%;background-repeat:no-repeat}.paymember-info-bar .paymember-info-logo[data-v-06d48b72]{flex-shrink:0;display:flex;justify-content:center;align-items:center;width:%?40?%;height:%?40?%}.paymember-info-bar .paymember-info-logo .logo-img[data-v-06d48b72]{display:block;width:100%;height:100%}.paymember-info-bar .paymember-info-title[data-v-06d48b72]{margin:0 %?15?%;flex:1}.paymember-info-bar .paymember-info-title .info-title[data-v-06d48b72]{font-size:%?28?%;line-height:1.2;max-width:%?450?%;font-weight:700}.paymember-info-bar .paymember-info-title .info-remark[data-v-06d48b72]{margin-top:%?10?%;font-size:%?24?%;line-height:1.2;color:#555d81;max-width:%?450?%}.paymember-info-bar .paymember-info-btn[data-v-06d48b72]{position:relative;flex-shrink:0;display:flex;justify-content:center;align-items:center;padding:0 %?15?%;min-width:%?140?%;height:%?58?%;background-size:cover;background-position:50%;background-repeat:no-repeat;border-radius:%?30?%;font-size:%?24?%;font-weight:700}.paymember-info-bar .paymember-info-btn .btn-tips[data-v-06d48b72]{position:absolute;padding:%?8?% %?15?%;left:50%;top:0;-webkit-transform:translate(-50%,-90%);transform:translate(-50%,-90%);background-color:#ff2700;color:#f9f000;color:%?24?%;border-radius:%?20?%;border:1px solid #ff2700;white-space:nowrap}.paymember-info-bar .paymember-info-btn .btn-tips .iconfont[data-v-06d48b72]{color:#ff2700;position:absolute;bottom:0;left:50%;-webkit-transform:translate(-50%,60%);transform:translate(-50%,60%);z-index:-1}',
                '',
            ]),
                (t.default = n);
        },
        59489: function (e, t, n) {
            n.d(t, {
                $J: function () {
                    return u;
                },
                CC: function () {
                    return i;
                },
                M5: function () {
                    return r;
                },
                Rr: function () {
                    return o;
                },
                S$: function () {
                    return s;
                },
                jr: function () {
                    return a;
                },
            });
            var i = { SINGLE: '01', MERGE: '02' },
                o = {
                    UNSTART: 'unstart',
                    UNRECEIVE: 'unreceive',
                    RECEIVE: 'receive',
                    COMPLETE: 'complete',
                    CLAIM: 'claim',
                },
                r = {
                    TASK: '60',
                    SIGN: '27',
                    AWARD: '14',
                    EQUITY: '61',
                    RECHARGE: '60',
                    STATION_REMEMBER: '66',
                },
                a = { TASK: null, SIGN: null, AWARD: '1410', EQUITY: null, RECHARGE: '6002' },
                u = { DEFAULT: 'default', ACTIVE: 'act' },
                s = { TRANSACTION: '25', CHARGE: '26', ORDER_DETAIL: '27' };
        },
    },
]);
