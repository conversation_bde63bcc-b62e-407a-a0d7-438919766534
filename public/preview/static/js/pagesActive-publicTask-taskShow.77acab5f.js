(self.webpackChunknew_charge = self.webpackChunknew_charge || []).push([
    [629],
    {
        32195: function (t, e, i) {
            var n = i(12719);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('5f159bfd', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        8270: function (t, e, i) {
            var n = i(13930);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('50fb63b3', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        90516: function (t, e, i) {
            var n = i(4256);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('4c264d42', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        40269: function (t, e, i) {
            var n = i(91753);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('4beadc6b', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        90299: function (t, e, i) {
            var n = i(55455);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('6cb00981', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        79314: function (t, e, i) {
            var n = i(40342);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('068e0fd5', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        55213: function (t, e, i) {
            var n = i(15713);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('25138e72', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        27233: function (t, e, i) {
            var n = i(18533);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('034bd3dc', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        48295: function (t, e, i) {
            var n = i(67795);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('da092558', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        10077: function (t, e, i) {
            var n = i(91441);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('63de2032', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        7291: function (t, e, i) {
            var n = i(99999);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('e3144976', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        15375: function (t, e, i) {
            var n = i(3331);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('73eefad9', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        30333: function (t, e, i) {
            var n = i(10441);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('30d71d1d', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        31238: function (t, e, i) {
            var n = i(72434);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('fe8f47a4', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        63059: function (t, e, i) {
            var n = i(2631);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('5d6b4fe6', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        78805: function (t, e, i) {
            var n = i(29385);
            (n = 'string' == typeof (n = n.__esModule ? n.default : n) ? [[t.id, n, '']] : n)
                .locals && (t.exports = n.locals),
                (0, i(69333).A)('a3cadbf6', n, !0, { sourceMap: !1, shadowMode: !1 });
        },
        18594: function (t, e, i) {
            i.r(e),
                i.d(e, {
                    default: function () {
                        return E;
                    },
                }),
                i(44114),
                i(79432);
            var n = i(62090),
                a = i(94084),
                o = i(81940),
                s =
                    ((e = {
                        props: {
                            assemblyInfo: { type: Object, required: !0 },
                            hideHeader: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                            assemblyStyle: { type: String },
                            assemblyStyleInfo: { type: Object },
                        },
                        data: function () {
                            return { IMG_URL: n.sE };
                        },
                        mounted: function () {},
                        methods: {
                            touchMoreEvent: function () {
                                this.$emit('more');
                            },
                        },
                    }),
                    i(90516),
                    i(18535)),
                r =
                    ((e = (0, s.A)(
                        e,
                        function () {
                            var t = this,
                                e = t.$createElement;
                            e = t._self._c || e;
                            return e(
                                'v-uni-view',
                                { staticClass: 'assembly-layout' },
                                [
                                    t.hideHeader
                                        ? t._e()
                                        : e('v-uni-image', {
                                              staticClass: 'layout-bg',
                                              attrs: {
                                                  mode: 'widthFix',
                                                  src: t.assemblyStyleInfo.headerImg,
                                              },
                                          }),
                                    t.hideHeader
                                        ? t._e()
                                        : e(
                                              'v-uni-view',
                                              { staticClass: 'layout-header' },
                                              [
                                                  e(
                                                      'v-uni-view',
                                                      { staticClass: 'header-title' },
                                                      [
                                                          e(
                                                              'v-uni-view',
                                                              { staticClass: 'title' },
                                                              [
                                                                  e(
                                                                      'v-uni-view',
                                                                      {
                                                                          class: [
                                                                              'title-text',
                                                                              'text-line',
                                                                              t.assemblyStyle,
                                                                          ],
                                                                      },
                                                                      [
                                                                          t._v(
                                                                              t._s(
                                                                                  t.assemblyInfo
                                                                                      .assemblyTitle,
                                                                              ),
                                                                          ),
                                                                      ],
                                                                  ),
                                                              ],
                                                              1,
                                                          ),
                                                      ],
                                                      1,
                                                  ),
                                                  e(
                                                      'v-uni-view',
                                                      { class: ['header-more', t.assemblyStyle] },
                                                      [
                                                          t._t('more', [
                                                              t.assemblyInfo.moreName
                                                                  ? e(
                                                                        'v-uni-text',
                                                                        {
                                                                            on: {
                                                                                click: function (
                                                                                    e,
                                                                                ) {
                                                                                    (arguments[0] =
                                                                                        e =
                                                                                            t.$handleEvent(
                                                                                                e,
                                                                                            )),
                                                                                        t.touchMoreEvent.apply(
                                                                                            void 0,
                                                                                            arguments,
                                                                                        );
                                                                                },
                                                                            },
                                                                        },
                                                                        [
                                                                            t._v(
                                                                                t._s(
                                                                                    t.assemblyInfo
                                                                                        .moreName ||
                                                                                        '',
                                                                                ),
                                                                            ),
                                                                            t.assemblyInfo.morePath
                                                                                ? e('v-uni-text', {
                                                                                      staticClass:
                                                                                          'iconfont iconjiantou',
                                                                                  })
                                                                                : t._e(),
                                                                        ],
                                                                        1,
                                                                    )
                                                                  : t._e(),
                                                          ]),
                                                      ],
                                                      2,
                                                  ),
                                              ],
                                              1,
                                          ),
                                    e(
                                        'v-uni-view',
                                        {
                                            class: ['layout-main'],
                                            style: { background: t.assemblyStyleInfo.background },
                                        },
                                        [t._t('default')],
                                        2,
                                    ),
                                ],
                                1,
                            );
                        },
                        [],
                        !1,
                        null,
                        '6a32fecc',
                        null,
                        !1,
                        void 0,
                        void 0,
                    ).exports),
                    i(3814)),
                l = (i(50113), i(34782), i(2892), i(26099), i(98992), i(72577), i(28706), i(59489)),
                c = i(83320),
                u = i(74353),
                d = i.n(u),
                f =
                    ((u = i(54786)),
                    {
                        components: { Countdown: u.A },
                        props: { info: { type: Object }, progressInfo: { type: Object } },
                        inject: ['assemblyInfo'],
                        data: function () {
                            return { IMG_URL: n.sE, TASK_STATUS: l.Rr, REWARD_TYPES: l.CC };
                        },
                        computed: {
                            relaActType: function () {
                                return this.info && this.info.relaActType
                                    ? this.info.relaActType
                                    : null;
                            },
                            relaActSubType: function () {
                                return this.info && this.info.relaActSubType
                                    ? this.info.relaActSubType
                                    : null;
                            },
                            isRechargeAct: function () {
                                return (
                                    this.relaActType === l.M5.RECHARGE &&
                                    this.relaActSubType === l.jr.RECHARGE
                                );
                            },
                            actInfoStartDays: function () {
                                if (this.moduleActInfo) {
                                    var t = d()(this.moduleActInfo.effTime),
                                        e = d()(),
                                        i = t.diff(e, 'day');
                                    t = t.diff(e, 'hour');
                                    if (0 < i) return ''.concat(i, '天后开始');
                                    if (0 < t && t < 24) return ''.concat(t, '小时后开始');
                                }
                                return '';
                            },
                            showStartCountdown: function () {
                                var t,
                                    e,
                                    i = 0;
                                return this.moduleActInfo &&
                                    ((e = d()(this.moduleActInfo.effTime)),
                                    (t = d()()),
                                    0 < (e = e.diff(t, 'millisecond'))) &&
                                    e < 36e5
                                    ? e
                                    : i;
                            },
                            surplusExpDays: function () {
                                if (this.moduleActInfo) {
                                    var t = d()(this.moduleActInfo.expTime),
                                        e = d()(),
                                        i = t.diff(e, 'day');
                                    t = t.diff(e, 'hour');
                                    if (0 < i) return '限时'.concat(i, '天');
                                    if (0 < t && t < 24) return '限时'.concat(t, '小时');
                                }
                                return '';
                            },
                            showExpCountdown: function () {
                                var t,
                                    e,
                                    i = 0;
                                return this.moduleActInfo &&
                                    ((e = d()(this.moduleActInfo.expTime)),
                                    (t = d()()),
                                    0 < (e = e.diff(t)))
                                    ? e
                                    : i;
                            },
                            moduleActInfoEffTime: function () {
                                var t;
                                return this.moduleActInfo
                                    ? ((t = d()(this.moduleActInfo.effTime)),
                                      d()(t).format('YYYY/MM/DD HH:mm'))
                                    : '';
                            },
                            moduleActInfoExpTime: function () {
                                var t;
                                return this.moduleActInfo
                                    ? ((t = d()(this.moduleActInfo.expTime)),
                                      d()(t).format('YYYY/MM/DD HH:mm'))
                                    : '';
                            },
                            mainModuleConfQueryVo: function () {
                                return this.info && this.info.mainModuleConfQueryVo
                                    ? this.info.mainModuleConfQueryVo
                                    : null;
                            },
                            currModuleBtnConfQueryVo: function () {
                                return this.mainModuleConfQueryVo &&
                                    this.mainModuleConfQueryVo.currModuleBtnConfQueryVo
                                    ? this.mainModuleConfQueryVo.currModuleBtnConfQueryVo
                                    : null;
                            },
                            moduleActInfo: function () {
                                return this.info && this.info.moduleActInfo
                                    ? this.info.moduleActInfo
                                    : null;
                            },
                            moduleActTaskInfoList: function () {
                                return this.moduleActInfo &&
                                    this.moduleActInfo.moduleActTaskInfoList instanceof Array
                                    ? this.moduleActInfo.moduleActTaskInfoList
                                    : [];
                            },
                            joinLimitMessage: function () {
                                var t,
                                    e = '';
                                return (
                                    this.moduleActInfo &&
                                        ((t = -1),
                                        !(0, a.g9)(this.moduleActInfo.joinLimit) &&
                                            0 < this.moduleActInfo.joinLimit &&
                                            (t = Number(this.moduleActInfo.joinLimit)),
                                        this.moduleActInfo.rewardType === l.CC.MERGE
                                            ? (t < 0 && (t = this.moduleActTaskInfoList.length),
                                              (e = '完成'
                                                  .concat(t, '个任务获得')
                                                  .concat(
                                                      (this.mainModuleConfQueryVo &&
                                                          this.mainModuleConfQueryVo.prizeName) ||
                                                          '奖励',
                                                  )))
                                            : this.moduleActInfo.rewardType === l.CC.SINGLE &&
                                              0 < t &&
                                              (e = '限完成任意'.concat(t, '个任务'))),
                                    e
                                );
                            },
                        },
                        methods: {
                            touchEvent: (0, c.n)(function () {
                                this.$emit('btnEvent', this.info);
                            }, 1e3),
                            reloadEvent: function () {
                                this.$emit('reloadEvent');
                            },
                        },
                    }),
                m =
                    ((f =
                        (i(27233),
                        (0, s.A)(
                            f,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return t.mainModuleConfQueryVo && t.moduleActInfo
                                    ? e(
                                          'v-uni-view',
                                          { staticClass: 'task-info-item' },
                                          [
                                              t.isRechargeAct
                                                  ? [
                                                        '02' == t.mainModuleConfQueryVo.styleType
                                                            ? e('v-uni-image', {
                                                                  staticClass: 'task-logo',
                                                                  attrs: {
                                                                      src:
                                                                          t.IMG_URL +
                                                                          '/static/images/task/new_people_icon.png',
                                                                  },
                                                              })
                                                            : e('v-uni-image', {
                                                                  staticClass: 'task-logo',
                                                                  attrs: {
                                                                      src:
                                                                          t.IMG_URL +
                                                                          '/static/images/task/task_prize_icon.png',
                                                                  },
                                                              }),
                                                    ]
                                                  : e('v-uni-image', {
                                                        staticClass: 'task-logo',
                                                        attrs: {
                                                            src: t.mainModuleConfQueryVo
                                                                .prizeImagePath,
                                                        },
                                                    }),
                                              e(
                                                  'v-uni-view',
                                                  {
                                                      class: [
                                                          'task-content',
                                                          {
                                                              small:
                                                                  t.currModuleBtnConfQueryVo &&
                                                                  1 ==
                                                                      t.currModuleBtnConfQueryVo
                                                                          .btnShowFlag,
                                                          },
                                                      ],
                                                  },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'content-title' },
                                                          [
                                                              e(
                                                                  'v-uni-text',
                                                                  {
                                                                      staticClass:
                                                                          'title text-line',
                                                                  },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              t
                                                                                  .mainModuleConfQueryVo
                                                                                  .title,
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                          ],
                                                          1,
                                                      ),
                                                      t.actInfoStartDays || 0 < t.showStartCountdown
                                                          ? e(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'content-remark text-wrap mg-t-15',
                                                                },
                                                                [
                                                                    t.actInfoStartDays
                                                                        ? e(
                                                                              'v-uni-text',
                                                                              {
                                                                                  staticClass:
                                                                                      'date-tip',
                                                                              },
                                                                              [
                                                                                  t._v(
                                                                                      t._s(
                                                                                          t.actInfoStartDays,
                                                                                      ),
                                                                                  ),
                                                                              ],
                                                                          )
                                                                        : 0 < t.showStartCountdown
                                                                        ? e(
                                                                              'v-uni-view',
                                                                              {
                                                                                  staticClass:
                                                                                      'date-tip',
                                                                              },
                                                                              [
                                                                                  e('Countdown', {
                                                                                      attrs: {
                                                                                          expTime:
                                                                                              t.showStartCountdown,
                                                                                          showUnit:
                                                                                              !0,
                                                                                          completion:
                                                                                              !1,
                                                                                      },
                                                                                      on: {
                                                                                          timeout:
                                                                                              function (
                                                                                                  e,
                                                                                              ) {
                                                                                                  (arguments[0] =
                                                                                                      e =
                                                                                                          t.$handleEvent(
                                                                                                              e,
                                                                                                          )),
                                                                                                      t.reloadEvent.apply(
                                                                                                          void 0,
                                                                                                          arguments,
                                                                                                      );
                                                                                              },
                                                                                      },
                                                                                  }),
                                                                                  t._v('后开始'),
                                                                              ],
                                                                              1,
                                                                          )
                                                                        : t._e(),
                                                                    t.isRechargeAct
                                                                        ? t._e()
                                                                        : [
                                                                              t._v(
                                                                                  '开始时间：' +
                                                                                      t._s(
                                                                                          t.moduleActInfoEffTime,
                                                                                      ),
                                                                              ),
                                                                          ],
                                                                ],
                                                                2,
                                                            )
                                                          : e(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'content-remark text-wrap mg-t-15',
                                                                },
                                                                [
                                                                    0 < t.showExpCountdown
                                                                        ? e(
                                                                              'v-uni-view',
                                                                              {
                                                                                  staticClass:
                                                                                      'date-exp-tip',
                                                                              },
                                                                              [
                                                                                  e('v-uni-text', {
                                                                                      staticClass:
                                                                                          'iconfont iconshichang',
                                                                                  }),
                                                                                  t._v('限时'),
                                                                                  e('Countdown', {
                                                                                      attrs: {
                                                                                          expTime:
                                                                                              t.showExpCountdown,
                                                                                          showUnit:
                                                                                              !1,
                                                                                          completion:
                                                                                              !0,
                                                                                          formatDay:
                                                                                              !0,
                                                                                      },
                                                                                      on: {
                                                                                          timeout:
                                                                                              function (
                                                                                                  e,
                                                                                              ) {
                                                                                                  (arguments[0] =
                                                                                                      e =
                                                                                                          t.$handleEvent(
                                                                                                              e,
                                                                                                          )),
                                                                                                      t.reloadEvent.apply(
                                                                                                          void 0,
                                                                                                          arguments,
                                                                                                      );
                                                                                              },
                                                                                      },
                                                                                  }),
                                                                              ],
                                                                              1,
                                                                          )
                                                                        : t._e(),
                                                                ],
                                                                1,
                                                            ),
                                                      t.joinLimitMessage
                                                          ? e(
                                                                'v-uni-view',
                                                                {
                                                                    class: [
                                                                        'content-remark',
                                                                        {
                                                                            light:
                                                                                t.actInfoStartDays ||
                                                                                0 <
                                                                                    t.showStartCountdown,
                                                                        },
                                                                        'mg-t-15',
                                                                    ],
                                                                },
                                                                [t._v(t._s(t.joinLimitMessage))],
                                                            )
                                                          : t._e(),
                                                  ],
                                                  1,
                                              ),
                                              t.currModuleBtnConfQueryVo
                                                  ? e(
                                                        'v-uni-view',
                                                        { staticClass: 'task-touch' },
                                                        [
                                                            t.moduleActInfo &&
                                                            t.moduleActInfo.actProgressStatus ===
                                                                t.TASK_STATUS.CLAIM
                                                                ? e('v-uni-image', {
                                                                      staticClass: 'done-view',
                                                                      attrs: {
                                                                          src:
                                                                              t.IMG_URL +
                                                                              '/static/images/common/task_done.png',
                                                                      },
                                                                  })
                                                                : t.moduleActInfo &&
                                                                  t.moduleActInfo
                                                                      .actProgressStatus ===
                                                                      t.TASK_STATUS.RECEIVE &&
                                                                  t.progressInfo
                                                                ? [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'progress-remark',
                                                                          },
                                                                          [
                                                                              t._v(
                                                                                  t._s(
                                                                                      t.progressInfo
                                                                                          .showContent ||
                                                                                          '',
                                                                                  ),
                                                                              ),
                                                                          ],
                                                                      ),
                                                                  ]
                                                                : 1 ==
                                                                  t.currModuleBtnConfQueryVo
                                                                      .btnShowFlag
                                                                ? [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'touch-btn',
                                                                              on: {
                                                                                  click: function (
                                                                                      e,
                                                                                  ) {
                                                                                      e.stopPropagation(),
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                          t.touchEvent.apply(
                                                                                              void 0,
                                                                                              arguments,
                                                                                          );
                                                                                  },
                                                                              },
                                                                          },
                                                                          [
                                                                              t._v(
                                                                                  t._s(
                                                                                      t
                                                                                          .currModuleBtnConfQueryVo
                                                                                          .btnDesc,
                                                                                  ),
                                                                              ),
                                                                          ],
                                                                      ),
                                                                  ]
                                                                : t._e(),
                                                        ],
                                                        2,
                                                    )
                                                  : t._e(),
                                          ],
                                          2,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '575bb83a',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    {
                        props: { info: { type: Object }, getSigninInfo: { type: Object } },
                        inject: ['assemblyInfo'],
                        data: function () {
                            return { IMG_URL: n.sE };
                        },
                        computed: {
                            hasSigned: function () {
                                return !!(
                                    this.getSigninInfo &&
                                    !(0, a.g9)(this.getSigninInfo.signDayNum) &&
                                    0 < this.getSigninInfo.signDayNum
                                );
                            },
                        },
                        methods: {
                            touchEvent: (0, c.n)(function () {
                                this.$emit('signEvent', this.getSigninInfo);
                            }, 1e3),
                        },
                    }),
                p =
                    ((m =
                        (i(10077),
                        (0, s.A)(
                            m,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return t.getSigninInfo
                                    ? e(
                                          'v-uni-view',
                                          { staticClass: 'signin-info-item' },
                                          [
                                              e('v-uni-image', {
                                                  staticClass: 'signin-logo',
                                                  attrs: {
                                                      src:
                                                          t.IMG_URL +
                                                          '/static/images/task/sign_logo.png',
                                                  },
                                              }),
                                              e(
                                                  'v-uni-view',
                                                  { staticClass: 'signin-content' },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'content-title' },
                                                          [
                                                              t.hasSigned
                                                                  ? [
                                                                        t._v(
                                                                            '已累积签到' +
                                                                                t._s(
                                                                                    t.getSigninInfo
                                                                                        .signDayNum ||
                                                                                        '0',
                                                                                ) +
                                                                                '天',
                                                                        ),
                                                                    ]
                                                                  : [
                                                                        t._v(
                                                                            t._s(
                                                                                t.getSigninInfo
                                                                                    .equityName ||
                                                                                    '',
                                                                            ),
                                                                        ),
                                                                    ],
                                                              t.getSigninInfo.todaySignFlag
                                                                  ? e(
                                                                        'v-uni-view',
                                                                        {
                                                                            staticClass:
                                                                                'signin-result',
                                                                        },
                                                                        [t._v('已签到明天再来噢')],
                                                                    )
                                                                  : t._e(),
                                                          ],
                                                          2,
                                                      ),
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'content-remark mg-t-10' },
                                                          [
                                                              t._v(
                                                                  '完成' +
                                                                      t._s(
                                                                          t.getSigninInfo
                                                                              .totalDayNum || '',
                                                                      ) +
                                                                      '天签到后可进行下一轮签到',
                                                              ),
                                                          ],
                                                      ),
                                                  ],
                                                  1,
                                              ),
                                              t.getSigninInfo
                                                  ? e(
                                                        'v-uni-view',
                                                        { staticClass: 'signin-touch' },
                                                        [
                                                            t.getSigninInfo.todaySignFlag
                                                                ? t._e()
                                                                : [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'touch-btn',
                                                                              on: {
                                                                                  click: function (
                                                                                      e,
                                                                                  ) {
                                                                                      (arguments[0] =
                                                                                          e =
                                                                                              t.$handleEvent(
                                                                                                  e,
                                                                                              )),
                                                                                          t.touchEvent.apply(
                                                                                              void 0,
                                                                                              arguments,
                                                                                          );
                                                                                  },
                                                                              },
                                                                          },
                                                                          [t._v('签到')],
                                                                      ),
                                                                  ],
                                                        ],
                                                        2,
                                                    )
                                                  : t._e(),
                                          ],
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '7bf82291',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    {
                        props: {
                            taskInfo: { type: Object, require: !0 },
                            disabled: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                            moduleActInfo: { type: Object },
                        },
                        data: function () {
                            return { TASK_STATUS: l.Rr, REWARD_TYPES: l.CC };
                        },
                        mounted: function () {
                            this.currTaskConfQueryVo &&
                                this.$emit('taskShowEvent', { taskInfo: this.taskInfo });
                        },
                        computed: {
                            currTaskConfQueryVo: function () {
                                return this.taskInfo ? this.taskInfo.currTaskConfQueryVo : null;
                            },
                            currTaskBtnConf: function () {
                                return this.currTaskConfQueryVo
                                    ? this.currTaskConfQueryVo.currTaskBtnConf
                                    : null;
                            },
                        },
                        methods: {
                            touchDone: (0, c.n)(function () {
                                this.currTaskBtnConf &&
                                    1 == this.currTaskBtnConf.btnClickFlag &&
                                    this.$emit('doEvent', { taskInfo: this.taskInfo });
                            }, 1e3),
                        },
                    }),
                g =
                    ((p =
                        (i(30333),
                        (0, s.A)(
                            p,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return t.currTaskConfQueryVo
                                    ? e(
                                          'v-uni-view',
                                          { staticClass: 'step-item' },
                                          [
                                              e(
                                                  'v-uni-view',
                                                  { staticClass: 'step-name' },
                                                  [
                                                      e(
                                                          'v-uni-text',
                                                          {
                                                              class: [
                                                                  'step-title',
                                                                  { disabled: t.disabled },
                                                              ],
                                                          },
                                                          [
                                                              t._v(
                                                                  t._s(
                                                                      t.currTaskConfQueryVo.title ||
                                                                          '',
                                                                  ),
                                                              ),
                                                          ],
                                                      ),
                                                      t.moduleActInfo &&
                                                      t.moduleActInfo.rewardType ===
                                                          t.REWARD_TYPES.SINGLE &&
                                                      t.currTaskConfQueryVo.prizeName
                                                          ? e(
                                                                'v-uni-text',
                                                                { staticClass: 'light text-line' },
                                                                [
                                                                    t._v(
                                                                        '得' +
                                                                            t._s(
                                                                                t
                                                                                    .currTaskConfQueryVo
                                                                                    .prizeName ||
                                                                                    '',
                                                                            ),
                                                                    ),
                                                                ],
                                                            )
                                                          : t._e(),
                                                  ],
                                                  1,
                                              ),
                                              !t.disabled &&
                                              t.currTaskBtnConf &&
                                              1 == t.currTaskBtnConf.btnShowFlag
                                                  ? e(
                                                        'v-uni-view',
                                                        { staticClass: 'step-status' },
                                                        [
                                                            t.taskInfo.taskProgressStatus ===
                                                            t.TASK_STATUS.CLAIM
                                                                ? e(
                                                                      'v-uni-view',
                                                                      {
                                                                          staticClass:
                                                                              'status-done',
                                                                      },
                                                                      [
                                                                          e('v-uni-text', {
                                                                              staticClass:
                                                                                  'iconfont iconxuanzhe',
                                                                          }),
                                                                          t._v(
                                                                              t._s(
                                                                                  t.currTaskBtnConf
                                                                                      .btnDesc ||
                                                                                      '',
                                                                              ),
                                                                          ),
                                                                      ],
                                                                      1,
                                                                  )
                                                                : t.taskInfo.taskProgressStatus ===
                                                                  t.TASK_STATUS.COMPLETE
                                                                ? [
                                                                      t.moduleActInfo &&
                                                                      t.moduleActInfo.rewardType ===
                                                                          t.REWARD_TYPES.SINGLE
                                                                          ? e(
                                                                                'v-uni-view',
                                                                                {
                                                                                    staticClass:
                                                                                        'status-get',
                                                                                    on: {
                                                                                        click: function (
                                                                                            e,
                                                                                        ) {
                                                                                            (arguments[0] =
                                                                                                e =
                                                                                                    t.$handleEvent(
                                                                                                        e,
                                                                                                    )),
                                                                                                t.touchDone.apply(
                                                                                                    void 0,
                                                                                                    arguments,
                                                                                                );
                                                                                        },
                                                                                    },
                                                                                },
                                                                                [
                                                                                    t._v(
                                                                                        t._s(
                                                                                            t
                                                                                                .currTaskBtnConf
                                                                                                .btnDesc ||
                                                                                                '',
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                            )
                                                                          : e(
                                                                                'v-uni-view',
                                                                                {
                                                                                    staticClass:
                                                                                        'status-done',
                                                                                },
                                                                                [
                                                                                    e(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            staticClass:
                                                                                                'iconfont iconxuanzhe',
                                                                                        },
                                                                                    ),
                                                                                    t._v(
                                                                                        t._s(
                                                                                            t
                                                                                                .currTaskBtnConf
                                                                                                .btnDesc ||
                                                                                                '',
                                                                                        ),
                                                                                    ),
                                                                                ],
                                                                                1,
                                                                            ),
                                                                  ]
                                                                : t.taskInfo.taskProgressStatus ===
                                                                  t.TASK_STATUS.RECEIVE
                                                                ? e(
                                                                      'v-uni-view',
                                                                      {
                                                                          staticClass:
                                                                              'status-todo',
                                                                          on: {
                                                                              click: function (e) {
                                                                                  (arguments[0] =
                                                                                      e =
                                                                                          t.$handleEvent(
                                                                                              e,
                                                                                          )),
                                                                                      t.touchDone.apply(
                                                                                          void 0,
                                                                                          arguments,
                                                                                      );
                                                                              },
                                                                          },
                                                                      },
                                                                      [
                                                                          t._v(
                                                                              t._s(
                                                                                  t.currTaskBtnConf
                                                                                      .btnDesc ||
                                                                                      '',
                                                                              ),
                                                                          ),
                                                                          e('v-uni-text', {
                                                                              staticClass:
                                                                                  'iconfont iconjiantou',
                                                                          }),
                                                                      ],
                                                                      1,
                                                                  )
                                                                : t._e(),
                                                        ],
                                                        2,
                                                    )
                                                  : t._e(),
                                          ],
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '6c9ed35c',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    {
                        props: {
                            relaSubType: { type: String },
                            info: { required: !0, type: Object },
                        },
                        computed: {
                            couponAmt: function () {
                                return this.info && !(0, a.LK)(this.info.cpnAmt)
                                    ? Number(this.info.cpnAmt)
                                    : 0;
                            },
                            hasCoupon: function () {
                                return 0 < this.couponAmt;
                            },
                            isSignined: function () {
                                return !(!this.info || !this.info.signFlag);
                            },
                            itemLabel: function () {
                                return this.isSignined
                                    ? this.hasCoupon
                                        ? '已领取'
                                        : '已签到'
                                    : this.info.remark || '';
                            },
                        },
                    }),
                h =
                    ((g =
                        (i(15375),
                        (0, s.A)(
                            g,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return e(
                                    'v-uni-view',
                                    { staticClass: 'signin-item-view' },
                                    [
                                        e(
                                            'v-uni-view',
                                            { staticClass: 'signin-item-content' },
                                            [
                                                t.hasCoupon
                                                    ? e(
                                                          'v-uni-view',
                                                          { staticClass: 'signin-item-coupon' },
                                                          [
                                                              t.info.actGiftbagIcon
                                                                  ? e('v-uni-image', {
                                                                        staticClass: 'coupon-bg',
                                                                        attrs: {
                                                                            src: t.info
                                                                                .actGiftbagIcon,
                                                                        },
                                                                    })
                                                                  : [
                                                                        e('v-uni-image', {
                                                                            staticClass:
                                                                                'coupon-bg',
                                                                            attrs: {
                                                                                src:
                                                                                    t.IMG_URL +
                                                                                    '/static/images/paymember/details3.0/signin_item_bg.png',
                                                                            },
                                                                        }),
                                                                        e(
                                                                            'v-uni-view',
                                                                            {
                                                                                staticClass:
                                                                                    'coupon-price',
                                                                            },
                                                                            [
                                                                                e(
                                                                                    'v-uni-text',
                                                                                    {
                                                                                        staticClass:
                                                                                            'price',
                                                                                    },
                                                                                    [
                                                                                        t._v(
                                                                                            t._s(
                                                                                                t.couponAmt,
                                                                                            ),
                                                                                        ),
                                                                                    ],
                                                                                ),
                                                                                e(
                                                                                    'v-uni-text',
                                                                                    {
                                                                                        staticClass:
                                                                                            'unit',
                                                                                    },
                                                                                    [t._v('元')],
                                                                                ),
                                                                            ],
                                                                            1,
                                                                        ),
                                                                    ],
                                                          ],
                                                          2,
                                                      )
                                                    : e(
                                                          'v-uni-view',
                                                          {
                                                              class: [
                                                                  'signin-item-dot',
                                                                  { done: t.isSignined },
                                                              ],
                                                          },
                                                          [
                                                              e('v-uni-text', {
                                                                  staticClass:
                                                                      'iconfont icondagou dot-arrow',
                                                              }),
                                                          ],
                                                          1,
                                                      ),
                                            ],
                                            1,
                                        ),
                                        e(
                                            'v-uni-view',
                                            {
                                                class: [
                                                    'signin-item-label',
                                                    { light: t.isSignined },
                                                ],
                                            },
                                            [t._v(t._s(t.itemLabel))],
                                        ),
                                        t.info && t.info.breakFlag
                                            ? e('v-uni-view', { class: ['patch-label'] }, [
                                                  t._v('已断签'),
                                              ])
                                            : t._e(),
                                    ],
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '6b96a6f8',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (g = {
                        props: {
                            relaSubType: { type: String },
                            signinList: {
                                type: Array,
                                required: !0,
                                default: function () {
                                    return [];
                                },
                            },
                        },
                        components: { SigninItem: g },
                        computed: {
                            formatSigninList: function () {
                                for (var t = [], e = 0; e < this.signinList.length; e += 7) {
                                    var i = this.signinList.slice(e, e + 7);
                                    t.push(i);
                                }
                                return t;
                            },
                        },
                    }),
                    (g =
                        (i(7291),
                        (0, s.A)(
                            g,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return i(
                                    'v-uni-view',
                                    { staticClass: 'signin-space' },
                                    t._l(t.formatSigninList, function (e, n) {
                                        return i(
                                            'v-uni-view',
                                            { key: n, staticClass: 'signin-bar pd-tb-20' },
                                            t._l(e, function (e, n) {
                                                return i(
                                                    'v-uni-view',
                                                    { key: n, staticClass: 'signin-item' },
                                                    [
                                                        i('SigninItem', {
                                                            attrs: {
                                                                relaSubType: t.relaSubType,
                                                                info: e,
                                                            },
                                                        }),
                                                    ],
                                                    1,
                                                );
                                            }),
                                            1,
                                        );
                                    }),
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '7745f4c0',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    {
                        props: {
                            taskInfo: { type: Object, required: !0 },
                            isFirst: { type: Boolean },
                            isEnd: { type: Boolean },
                        },
                        data: function () {
                            return { IMG_URL: n.sE, TASK_STATUS: l.Rr };
                        },
                        computed: {
                            currTaskConfQueryVo: function () {
                                return this.taskInfo ? this.taskInfo.currTaskConfQueryVo : null;
                            },
                            currTaskBtnConf: function () {
                                return this.currTaskConfQueryVo &&
                                    this.currTaskConfQueryVo.currTaskBtnConf
                                    ? this.currTaskConfQueryVo.currTaskBtnConf
                                    : null;
                            },
                        },
                        methods: {
                            touchDone: (0, c.n)(function () {
                                this.currTaskBtnConf &&
                                    1 == this.currTaskBtnConf.btnClickFlag &&
                                    this.$emit('doEvent', { taskInfo: this.taskInfo });
                            }, 1e3),
                        },
                    }),
                v =
                    ((h =
                        (i(79314),
                        (0, s.A)(
                            h,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return e(
                                    'v-uni-view',
                                    { staticClass: 'task-main' },
                                    [
                                        e(
                                            'v-uni-view',
                                            {
                                                class: [
                                                    'task-prize',
                                                    { first: t.isFirst, end: t.isEnd },
                                                ],
                                            },
                                            [
                                                t.taskInfo.taskProgressStatus ===
                                                t.TASK_STATUS.CLAIM
                                                    ? e('v-uni-text', {
                                                          staticClass:
                                                              'iconfont iconxuanze task-prize-done',
                                                      })
                                                    : [
                                                          t.taskInfo.taskProgressStatus ==
                                                          t.TASK_STATUS.COMPLETE
                                                              ? e('v-uni-image', {
                                                                    class: [
                                                                        'task-prize-img',
                                                                        'complete',
                                                                    ],
                                                                    attrs: {
                                                                        src:
                                                                            t.IMG_URL +
                                                                            '/static/images/task/claim_prize.png',
                                                                    },
                                                                })
                                                              : e('v-uni-image', {
                                                                    class: ['task-prize-img'],
                                                                    attrs: {
                                                                        src:
                                                                            t.IMG_URL +
                                                                            '/static/images/task/charge_prize.png',
                                                                    },
                                                                }),
                                                      ],
                                            ],
                                            2,
                                        ),
                                        e('v-uni-view', { staticClass: 'task-prize-name' }, [
                                            t._v(t._s(t.currTaskConfQueryVo.prizeName)),
                                        ]),
                                        e('v-uni-view', { staticClass: 'task-title' }, [
                                            t._v(t._s(t.currTaskConfQueryVo.title)),
                                        ]),
                                        t.currTaskBtnConf &&
                                        1 == t.currTaskBtnConf.btnClickFlag &&
                                        t.currTaskBtnConf.status == t.TASK_STATUS.COMPLETE
                                            ? e(
                                                  'v-uni-view',
                                                  {
                                                      staticClass: 'task-interactive',
                                                      on: {
                                                          click: function (e) {
                                                              e.stopPropagation(),
                                                                  (arguments[0] = e =
                                                                      t.$handleEvent(e)),
                                                                  t.touchDone.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                      },
                                                  },
                                                  [
                                                      t._v(
                                                          t._s(
                                                              t.currTaskBtnConf.btnDesc || '领奖励',
                                                          ),
                                                      ),
                                                  ],
                                              )
                                            : t._e(),
                                    ],
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '848c1080',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (h = {
                        props: {
                            taskList: {
                                type: Array,
                                required: !0,
                                default: function () {
                                    return [];
                                },
                            },
                        },
                        components: { TaskItem: h },
                        methods: {
                            touchTaskPrizeEvent: function (t) {
                                this.$emit('doEvent', t);
                            },
                        },
                    }),
                    (h =
                        (i(55213),
                        (0, s.A)(
                            h,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return 0 < t.taskList.length
                                    ? i(
                                          'v-uni-view',
                                          { staticClass: 'charge-task-list' },
                                          t._l(t.taskList, function (e, n) {
                                              return i(
                                                  'v-uni-view',
                                                  { key: n, staticClass: 'charge-task-item' },
                                                  [
                                                      i('TaskItem', {
                                                          attrs: {
                                                              taskInfo: e,
                                                              isFirst: 0 == n,
                                                              isEnd: n == t.taskList.length - 1,
                                                          },
                                                          on: {
                                                              doEvent: function (e) {
                                                                  (arguments[0] = e =
                                                                      t.$handleEvent(e)),
                                                                      t.touchTaskPrizeEvent.apply(
                                                                          void 0,
                                                                          arguments,
                                                                      );
                                                              },
                                                          },
                                                      }),
                                                  ],
                                                  1,
                                              );
                                          }),
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '0dd4fed1',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    i(23074)),
                b = i(31554),
                y = i(45013),
                w = i(55373),
                k = i.n(w),
                I =
                    ((w = {
                        components: { Countdown: u.A },
                        props: { info: { type: Object }, assemblyClaimInfo: { type: Object } },
                        inject: ['assemblyInfo', 'assemblySourceType', 'queryParams'],
                        data: function () {
                            return { IMG_URL: n.sE, TASK_STATUS: l.Rr, REWARD_TYPES: l.CC };
                        },
                        computed: {
                            actInfoStartDays: function () {
                                if (this.moduleActInfo) {
                                    var t = d()(this.moduleActInfo.effTime),
                                        e = d()(),
                                        i = t.diff(e, 'day');
                                    t = t.diff(e, 'hour');
                                    if (0 < i) return ''.concat(i, '天后开始');
                                    if (0 < t && t < 24) return ''.concat(t, '小时后开始');
                                }
                                return '';
                            },
                            showStartCountdown: function () {
                                var t = 0;
                                return this.moduleActInfo ? this.moduleActInfo.effTimeMs : t;
                            },
                            surplusExpDays: function () {
                                if (this.moduleActInfo) {
                                    var t = d()(this.moduleActInfo.expTime),
                                        e = d()(),
                                        i = t.diff(e, 'day');
                                    t = t.diff(e, 'hour');
                                    if (0 < i) return '限时'.concat(i, '天');
                                    if (0 < t && t < 24) return '限时'.concat(t, '小时');
                                }
                                return '';
                            },
                            showExpCountdown: function () {
                                var t = 0;
                                return this.moduleActInfo ? this.moduleActInfo.expTimeMs : t;
                            },
                            moduleActInfoEffTime: function () {
                                var t;
                                return this.moduleActInfo
                                    ? ((t = d()(this.moduleActInfo.effTime)),
                                      d()(t).format('YYYY/MM/DD HH:mm'))
                                    : '';
                            },
                            moduleActInfoExpTime: function () {
                                var t;
                                return this.moduleActInfo
                                    ? ((t = d()(this.moduleActInfo.expTime)),
                                      d()(t).format('YYYY/MM/DD HH:mm'))
                                    : '';
                            },
                            mainModuleConfQueryVo: function () {
                                return this.info && this.info.mainModuleConfQueryVo
                                    ? this.info.mainModuleConfQueryVo
                                    : null;
                            },
                            currModuleBtnConfQueryVo: function () {
                                return this.mainModuleConfQueryVo &&
                                    this.mainModuleConfQueryVo.currModuleBtnConfQueryVo
                                    ? this.mainModuleConfQueryVo.currModuleBtnConfQueryVo
                                    : null;
                            },
                            moduleActInfo: function () {
                                return this.info && this.info.moduleActInfo
                                    ? this.info.moduleActInfo
                                    : null;
                            },
                            moduleActTaskInfoList: function () {
                                return this.moduleActInfo &&
                                    this.moduleActInfo.moduleActTaskInfoList instanceof Array
                                    ? this.moduleActInfo.moduleActTaskInfoList
                                    : [];
                            },
                            joinLimitMessage: function () {
                                var t,
                                    e = '';
                                return (
                                    this.moduleActInfo &&
                                        ((t = -1),
                                        !(0, a.g9)(this.moduleActInfo.joinLimit) &&
                                            0 < this.moduleActInfo.joinLimit &&
                                            (t = Number(this.moduleActInfo.joinLimit)),
                                        this.moduleActInfo.rewardType === l.CC.MERGE
                                            ? (t < 0 && (t = this.moduleActTaskInfoList.length),
                                              (e = '完成'
                                                  .concat(t, '个任务获得')
                                                  .concat(
                                                      (this.mainModuleConfQueryVo &&
                                                          this.mainModuleConfQueryVo.prizeName) ||
                                                          '奖励',
                                                  )))
                                            : this.moduleActInfo.rewardType === l.CC.SINGLE &&
                                              0 < t &&
                                              (e = '限完成任意'.concat(t, '个任务'))),
                                    e
                                );
                            },
                        },
                        methods: (0, o.A)(
                            (0, o.A)({}, (0, y.mapActions)('active', ['updateAwardPrizeInfo'])),
                            {},
                            {
                                touchEvent: (0, c.n)(function () {
                                    this.$emit('btnEvent', this.info);
                                }, 1e3),
                                reloadEvent: function () {
                                    this.$emit('reloadEvent');
                                },
                                awardDetail: function () {
                                    this.updateAwardPrizeInfo({
                                        assemblyInfo: this.assemblyClaimInfo,
                                        moduleInfo: this.info,
                                    });
                                    var t = (0, o.A)(
                                        { assemblySourceType: this.assemblySourceType },
                                        this.queryParams,
                                    );
                                    this.jumpPage(
                                        '/pagesActive/publicTask/awardPrize?'.concat(
                                            k().stringify(t),
                                        ),
                                    );
                                },
                                jumpPage: (0, c.n)(
                                    (() => {
                                        var t = (0, b.A)(
                                            (0, v.A)().mark(function t(e, i) {
                                                return (0, v.A)().wrap(function (t) {
                                                    for (;;)
                                                        switch ((t.prev = t.next)) {
                                                            case 0:
                                                                e && (0, a.ZK)(e);
                                                            case 1:
                                                            case 'end':
                                                                return t.stop();
                                                        }
                                                }, t);
                                            }),
                                        );
                                        return function (e, i) {
                                            return t.apply(this, arguments);
                                        };
                                    })(),
                                    2e3,
                                ),
                            },
                        ),
                    }),
                    (u =
                        (i(90299),
                        (0, s.A)(
                            w,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return t.mainModuleConfQueryVo && t.moduleActInfo
                                    ? e(
                                          'v-uni-view',
                                          {
                                              staticClass: 'task-info-item',
                                              on: {
                                                  click: function (e) {
                                                      (arguments[0] = e = t.$handleEvent(e)),
                                                          t.awardDetail();
                                                  },
                                              },
                                          },
                                          [
                                              e('v-uni-image', {
                                                  staticClass: 'task-logo',
                                                  attrs: {
                                                      src: t.mainModuleConfQueryVo.prizeImagePath,
                                                  },
                                              }),
                                              e(
                                                  'v-uni-view',
                                                  {
                                                      class: [
                                                          'task-content',
                                                          {
                                                              small:
                                                                  t.currModuleBtnConfQueryVo &&
                                                                  1 ==
                                                                      t.currModuleBtnConfQueryVo
                                                                          .btnShowFlag,
                                                          },
                                                      ],
                                                  },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'content-title' },
                                                          [
                                                              e(
                                                                  'v-uni-text',
                                                                  {
                                                                      staticClass:
                                                                          'title text-line',
                                                                  },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              t
                                                                                  .mainModuleConfQueryVo
                                                                                  .title,
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                          ],
                                                          1,
                                                      ),
                                                      t.moduleActInfo.actProgressStatus ===
                                                      t.TASK_STATUS.UNSTART
                                                          ? e(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'content-remark text-wrap mg-t-15',
                                                                },
                                                                [
                                                                    0 < t.showExpCountdown
                                                                        ? e(
                                                                              'v-uni-view',
                                                                              {
                                                                                  staticClass:
                                                                                      'date-exp-tip',
                                                                              },
                                                                              [
                                                                                  e('Countdown', {
                                                                                      attrs: {
                                                                                          expTime:
                                                                                              t.showStartCountdown,
                                                                                          showUnit:
                                                                                              !1,
                                                                                          completion:
                                                                                              !0,
                                                                                          formatDay:
                                                                                              !0,
                                                                                      },
                                                                                      on: {
                                                                                          timeout:
                                                                                              function (
                                                                                                  e,
                                                                                              ) {
                                                                                                  (arguments[0] =
                                                                                                      e =
                                                                                                          t.$handleEvent(
                                                                                                              e,
                                                                                                          )),
                                                                                                      t.reloadEvent.apply(
                                                                                                          void 0,
                                                                                                          arguments,
                                                                                                      );
                                                                                              },
                                                                                      },
                                                                                  }),
                                                                                  t._v('后开抢'),
                                                                              ],
                                                                              1,
                                                                          )
                                                                        : t._e(),
                                                                ],
                                                                1,
                                                            )
                                                          : t._e(),
                                                      t.moduleActInfo.actProgressStatus ===
                                                      t.TASK_STATUS.UNRECEIVE
                                                          ? e(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'content-remark text-wrap mg-t-15',
                                                                },
                                                                [
                                                                    0 < t.showExpCountdown
                                                                        ? e(
                                                                              'v-uni-view',
                                                                              {
                                                                                  staticClass:
                                                                                      'date-exp-tip',
                                                                              },
                                                                              [
                                                                                  e('v-uni-text', {
                                                                                      staticClass:
                                                                                          'iconfont iconshichang',
                                                                                  }),
                                                                                  t._v('限时'),
                                                                                  e('Countdown', {
                                                                                      attrs: {
                                                                                          expTime:
                                                                                              t.showExpCountdown,
                                                                                          showUnit:
                                                                                              !1,
                                                                                          completion:
                                                                                              !0,
                                                                                          formatDay:
                                                                                              !0,
                                                                                      },
                                                                                      on: {
                                                                                          timeout:
                                                                                              function (
                                                                                                  e,
                                                                                              ) {
                                                                                                  (arguments[0] =
                                                                                                      e =
                                                                                                          t.$handleEvent(
                                                                                                              e,
                                                                                                          )),
                                                                                                      t.reloadEvent.apply(
                                                                                                          void 0,
                                                                                                          arguments,
                                                                                                      );
                                                                                              },
                                                                                      },
                                                                                  }),
                                                                              ],
                                                                              1,
                                                                          )
                                                                        : t._e(),
                                                                ],
                                                                1,
                                                            )
                                                          : t._e(),
                                                      t.mainModuleConfQueryVo.subTitle
                                                          ? e(
                                                                'v-uni-view',
                                                                {
                                                                    class: [
                                                                        'content-remark',
                                                                        'mg-t-15',
                                                                    ],
                                                                },
                                                                [
                                                                    t._v(
                                                                        t._s(
                                                                            t.mainModuleConfQueryVo
                                                                                .subTitle,
                                                                        ),
                                                                    ),
                                                                ],
                                                            )
                                                          : t._e(),
                                                  ],
                                                  1,
                                              ),
                                              t.moduleActInfo
                                                  ? e(
                                                        'v-uni-view',
                                                        { staticClass: 'task-touch' },
                                                        [
                                                            t.moduleActInfo.actProgressStatus ===
                                                            t.TASK_STATUS.UNSTART
                                                                ? [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'touch-btn',
                                                                          },
                                                                          [t._v('即将开抢')],
                                                                      ),
                                                                  ]
                                                                : t.moduleActInfo
                                                                      .actProgressStatus ===
                                                                  t.TASK_STATUS.UNRECEIVE
                                                                ? [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'touch-btn',
                                                                              on: {
                                                                                  click: function (
                                                                                      e,
                                                                                  ) {
                                                                                      e.stopPropagation(),
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                          t.touchEvent.apply(
                                                                                              void 0,
                                                                                              arguments,
                                                                                          );
                                                                                  },
                                                                              },
                                                                          },
                                                                          [t._v('立即领取')],
                                                                      ),
                                                                  ]
                                                                : t.moduleActInfo
                                                                      .actProgressStatus ===
                                                                  t.TASK_STATUS.COMPLETE
                                                                ? [
                                                                      e(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'touch-btn complete',
                                                                          },
                                                                          [t._v('已抢完')],
                                                                      ),
                                                                  ]
                                                                : t.moduleActInfo
                                                                      .actProgressStatus ===
                                                                  t.TASK_STATUS.CLAIM
                                                                ? e('v-uni-image', {
                                                                      staticClass: 'done-view',
                                                                      attrs: {
                                                                          src:
                                                                              t.IMG_URL +
                                                                              '/static/images/common/task_done.png',
                                                                      },
                                                                  })
                                                                : t._e(),
                                                        ],
                                                        2,
                                                    )
                                                  : t._e(),
                                          ],
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            'aa485f4e',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    i(82525)),
                A =
                    (i(2008),
                    i(74423),
                    i(25276),
                    i(62062),
                    i(60739),
                    i(33110),
                    i(27495),
                    i(21699),
                    i(25440),
                    i(54520),
                    i(81454),
                    i(32135)),
                C =
                    ((w = {
                        props: {
                            label: { type: String, default: '会员价' },
                            position: { type: String, default: 'topRight' },
                            round: { type: Boolean, default: !1 },
                            backgroundColor: { type: String, default: '#f2f2f2' },
                            frontColor: { type: String, default: '#0C0E0C' },
                            dark: { type: Boolean, default: !1 },
                        },
                        computed: {
                            hasOption: function () {
                                return ['left', 'right', 'top', 'bottom'].includes(this.position);
                            },
                            darkColor: function () {
                                return this.dark ? '#0C0E0C' : '';
                            },
                            getBackgroundColor: function () {
                                return this.darkColor || this.backgroundColor;
                            },
                            getFrontColor: function () {
                                return this.darkColor ? '#fff' : this.frontColor;
                            },
                        },
                    }),
                    (w =
                        (i(63059),
                        (0, s.A)(
                            w,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return e(
                                    'v-uni-text',
                                    {
                                        class: [
                                            'newpeople-discount-bar',
                                            { round: t.round, dark: t.dark },
                                            t.position,
                                        ],
                                        style: {
                                            backgroundColor: t.getBackgroundColor,
                                            color: t.getFrontColor,
                                        },
                                    },
                                    [
                                        t.hasOption
                                            ? e('v-uni-text', {
                                                  class: ['iconfont', 'iconxiala1', t.position],
                                                  style: { color: t.getBackgroundColor },
                                              })
                                            : t._e(),
                                        e(
                                            'v-uni-text',
                                            { staticClass: 'discount-bar-main' },
                                            [t._t('default', [t._v(t._s(t.label))])],
                                            2,
                                        ),
                                    ],
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '7a2b8e01',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    { props: { info: { type: Object, require: !0 } } }),
                x =
                    ((C =
                        (i(32195),
                        (0, s.A)(
                            C,
                            function () {
                                var t = this,
                                    e = t.$createElement;
                                e = t._self._c || e;
                                return e(
                                    'v-uni-view',
                                    { staticClass: 'pie-space' },
                                    [
                                        0 < t.info.superPileNum
                                            ? e(
                                                  'v-uni-view',
                                                  { staticClass: 'pie-bar cell-bar' },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'pie-item' },
                                                          [
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-icon super' },
                                                                  [t._v('超')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-type' },
                                                                  [t._v('闲')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-num' },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              0 <
                                                                                  t.info
                                                                                      .superPileNum
                                                                                  ? t.info
                                                                                        .superPileFreeNum
                                                                                  : '-',
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                              t._v(
                                                                  '/' +
                                                                      t._s(
                                                                          (0 <
                                                                              t.info.superPileNum &&
                                                                              t.info
                                                                                  .superPileNum) ||
                                                                              '-',
                                                                      ),
                                                              ),
                                                          ],
                                                          1,
                                                      ),
                                                  ],
                                                  1,
                                              )
                                            : t._e(),
                                        0 < t.info.dcPileNum
                                            ? e(
                                                  'v-uni-view',
                                                  { staticClass: 'pie-bar cell-bar' },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'pie-item' },
                                                          [
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-icon dc' },
                                                                  [t._v('快')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-type' },
                                                                  [t._v('闲')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-num' },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              0 < t.info.dcPileNum
                                                                                  ? t.info
                                                                                        .dcPileFreeNum
                                                                                  : '-',
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                              t._v(
                                                                  '/' +
                                                                      t._s(
                                                                          (0 < t.info.dcPileNum &&
                                                                              t.info.dcPileNum) ||
                                                                              '-',
                                                                      ),
                                                              ),
                                                          ],
                                                          1,
                                                      ),
                                                  ],
                                                  1,
                                              )
                                            : t._e(),
                                        0 < t.info.acPileNum
                                            ? e(
                                                  'v-uni-view',
                                                  { staticClass: 'pie-bar cell-bar' },
                                                  [
                                                      e(
                                                          'v-uni-view',
                                                          { staticClass: 'pie-item' },
                                                          [
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-icon ac' },
                                                                  [t._v('慢')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-type' },
                                                                  [t._v('闲')],
                                                              ),
                                                              e(
                                                                  'v-uni-text',
                                                                  { staticClass: 'pie-num' },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              0 < t.info.acPileNum
                                                                                  ? t.info
                                                                                        .acPileFreeNum
                                                                                  : '-',
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                              t._v(
                                                                  '/' +
                                                                      t._s(
                                                                          (0 < t.info.acPileNum &&
                                                                              t.info.acPileNum) ||
                                                                              '-',
                                                                      ),
                                                              ),
                                                          ],
                                                          1,
                                                      ),
                                                  ],
                                                  1,
                                              )
                                            : t._e(),
                                    ],
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '32d7f2ce',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (w = {
                        props: {
                            info: { type: Object, required: !0 },
                            showClose: { type: Boolean, default: !1 },
                            showPosition: { type: Boolean, default: !0 },
                            pageName: { type: String },
                            isShadow: { type: Boolean, default: !1 },
                            sortRule: { type: String, default: '' },
                            itemBgColor: { type: String, default: 'fff' },
                            pieType: { type: String, default: '' },
                            chargingType: {
                                type: Array,
                                default: function () {
                                    return [];
                                },
                            },
                            hideDest: { type: Boolean, default: !1 },
                            rank: { type: String },
                            isCollect: { type: Boolean, default: !1 },
                            isLocation: { type: Boolean, default: !1 },
                        },
                        components: { StationPriceTag: w, PieBar: C },
                        data: function () {
                            return {
                                IMG_URL: n.sE,
                                CHANNEL_TYPES: A.s8,
                                LOGIN_CHANNEL: n.Kn,
                                showMore: !0,
                                canIShowMore: !0,
                            };
                        },
                        onUnload: function () {},
                        mounted: function () {
                            uni.createSelectorQuery().in(this);
                        },
                        computed: (0, o.A)(
                            (0, o.A)(
                                (0, o.A)(
                                    {},
                                    (0, y.mapGetters)('common', [
                                        'getSaveLocationInfo',
                                        'getLastLocation',
                                        'getSystemParams',
                                    ]),
                                ),
                                (0, y.mapGetters)('filter', ['getAlgorithmInfo']),
                            ),
                            {},
                            {
                                chargingTypeCount: function () {
                                    var t = 0;
                                    return (
                                        0 < this.info.acPileNum && t++,
                                        0 < this.info.dcPileNum && t++,
                                        0 < this.info.superPileNum && t++,
                                        t
                                    );
                                },
                                moreDiscountActTagNum: function () {
                                    return this.actTagList.length + this.getCouponList.length;
                                },
                                moreActNum: function () {
                                    return (
                                        this.getAllActTagList.length +
                                        this.getCouponList.length -
                                        this.getSliceActTagList.length
                                    );
                                },
                                getSliceActTagList: function () {
                                    var t = this.getAllActTagList || [],
                                        e = 4;
                                    return (
                                        0 < this.getCouponList.length && (e -= 1),
                                        t.length > e && (t = t.slice(0, e)),
                                        this.getCouponList instanceof Array
                                            ? t.concat(
                                                  this.getCouponList.map(function (t) {
                                                      return { actName: t.cpnName };
                                                  }),
                                              )
                                            : t
                                    );
                                },
                                getAllActTagList: function () {
                                    var t,
                                        e = [];
                                    if (
                                        this.info &&
                                        (this.isShowVipChannelFlag &&
                                            !(0, a._X)(A.s8.SHUNFENG) &&
                                            0 < this.getVipResultPrice &&
                                            ((this.info.vipFlag &&
                                                (this.info.proVipFlag ||
                                                    (0, a.g9)(this.getProVipPrice) ||
                                                    (0, a.g9)(this.getVipPrice) ||
                                                    this.getProVipPrice == this.getVipPrice)) ||
                                                ((t = ''),
                                                (t =
                                                    this.info.hideSlowVipPrice ||
                                                    this.info.hideVipPrice ||
                                                    this.info.hideSuperVipPrice
                                                        ? '****'
                                                        : ''.concat(this.getVipResultPrice)),
                                                e.push({
                                                    actName: t,
                                                    vip: !0,
                                                    link: '/pagesPaymember/paymember/pay?sourceName='
                                                        .concat(
                                                            this.info.stationId,
                                                            '&sourceType=station&sourceEvent={StationListClick}|{点击站点卡片}|{{站点ID},{',
                                                        )
                                                        .concat(this.info.stationId, '}}'),
                                                }))),
                                        this.actTagList instanceof Array)
                                    ) {
                                        var i,
                                            n = (0, r.A)(this.actTagList);
                                        try {
                                            for (n.s(); !(i = n.n()).done; ) {
                                                var o = i.value;
                                                o.showFlag &&
                                                    !(0, a.g9)(o.tagName) &&
                                                    e.push({
                                                        actName: o.tagName,
                                                        light: '1' === o.tagLevel,
                                                    });
                                            }
                                        } catch (t) {
                                            n.e(t);
                                        } finally {
                                            n.f();
                                        }
                                    }
                                    return e;
                                },
                                getStationTag: function () {
                                    return this.info &&
                                        this.info.stationTag instanceof Array &&
                                        0 < this.info.stationTag.length
                                        ? this.info.stationTag.slice(0, 5)
                                        : [];
                                },
                                isGreenTag: function () {
                                    return (
                                        this.getStationTag.find(function (t) {
                                            return t.tagName && -1 !== t.tagName.indexOf('绿电');
                                        }) || ''
                                    );
                                },
                                filterChargeTypes: function () {
                                    var t = this,
                                        e = (0, I.A)(A.yq);
                                    return (0, a.LK)(this.chargingType)
                                        ? e
                                        : (0, I.A)(A.yq).filter(function (e) {
                                              return t.chargingType.includes(e);
                                          });
                                },
                                getDiscountPrice: function () {
                                    var t = '';
                                    return (
                                        !(0, a.g9)(this.info.slowDiscountIdentityAmount) &&
                                            this.filterChargeTypes.includes(A.X.SLOW) &&
                                            (t = this.info.slowDiscountIdentityAmount),
                                        !(0, a.g9)(this.info.superDiscountIdentityAmount) &&
                                            this.filterChargeTypes.includes(A.X.SUPER) &&
                                            (t = this.info.superDiscountIdentityAmount),
                                        !(0, a.g9)(this.info.discountIdentityAmount) &&
                                        this.filterChargeTypes.includes(A.X.FAST)
                                            ? this.info.discountIdentityAmount
                                            : t
                                    );
                                },
                                formatDiscountPrice: function () {
                                    var t, e;
                                    return this.getDiscountPrice
                                        ? ((t = ''),
                                          this.chargingTypeCount < 3
                                              ? ((t = this.getDiscountPrice),
                                                (e = this.formatDiscountPriceResult('会员')).flag &&
                                                    (t = e.result),
                                                (e = this.formatDiscountPriceResult('Pro会员'))
                                                    .flag && (t = e.result),
                                                (e = this.formatDiscountPriceResult('新客')).flag &&
                                                    (t = e.result),
                                                (e = this.formatDiscountPriceResult('V3')).flag &&
                                                    (t = e.result))
                                              : ((e = this.formatDiscountPriceResult('会员').flag),
                                                e && (t = '会员价'),
                                                (e =
                                                    this.formatDiscountPriceResult('Pro会员').flag),
                                                e && (t = 'Pro会员价'),
                                                (e = this.formatDiscountPriceResult('新客').flag),
                                                e && (t = '新客价'),
                                                (e = this.formatDiscountPriceResult('V3').flag),
                                                e && (t = 'V3价')),
                                          t)
                                        : '';
                                },
                                showStationPrice: function () {
                                    return this.info && this.info.vipFlag && this.showVipPriceFlag
                                        ? [A.s8.ALI, A.s8.AMAP].includes(n.Kn) &&
                                          0 < this.getProVipPrice &&
                                          this.info.proVipFlag
                                            ? this.getProVipPrice
                                            : 0 < this.getVipPrice
                                            ? this.getVipPrice
                                            : this.getBasicPrice
                                        : this.getBasicPrice;
                                },
                                getBasicPrice: function () {
                                    var t = 0;
                                    return (
                                        !(0, a.g9)(this.info.slowPrice) &&
                                            this.filterChargeTypes.includes(A.X.SLOW) &&
                                            (t = this.info.slowPrice),
                                        !(0, a.g9)(this.info.superPrice) &&
                                            this.filterChargeTypes.includes(A.X.SUPER) &&
                                            (t = this.info.superPrice),
                                        !(0, a.g9)(this.info.price) &&
                                        this.filterChargeTypes.includes(A.X.FAST)
                                            ? this.info.price
                                            : t
                                    );
                                },
                                getVipResultPrice: function () {
                                    var t = 0;
                                    return (
                                        this.filterChargeTypes.includes(A.X.SLOW) &&
                                            ((0, a.g9)(this.info.slowVipPrice) ||
                                                (t = this.info.slowVipPrice),
                                            (0, a.g9)(this.info.slowProVipPrice) ||
                                                (t = this.info.slowProVipPrice)),
                                        this.filterChargeTypes.includes(A.X.SUPER) &&
                                            ((0, a.g9)(this.info.superVipPrice) ||
                                                (t = this.info.superVipPrice),
                                            (0, a.g9)(this.info.superProVipPrice) ||
                                                (t = this.info.superProVipPrice)),
                                        this.filterChargeTypes.includes(A.X.FAST) &&
                                            ((0, a.g9)(this.info.vipPrice) ||
                                                (t = this.info.vipPrice),
                                            (0, a.g9)(this.info.proVipPrice) ||
                                                (t = this.info.proVipPrice)),
                                        t
                                    );
                                },
                                getVipPrice: function () {
                                    var t = 0;
                                    return (
                                        this.filterChargeTypes.includes(A.X.SLOW) &&
                                            !(0, a.g9)(this.info.slowVipPrice) &&
                                            (t = this.info.slowVipPrice),
                                        this.filterChargeTypes.includes(A.X.SUPER) &&
                                            !(0, a.g9)(this.info.superVipPrice) &&
                                            (t = this.info.superVipPrice),
                                        this.filterChargeTypes.includes(A.X.FAST) &&
                                        !(0, a.g9)(this.info.vipPrice)
                                            ? this.info.vipPrice
                                            : t
                                    );
                                },
                                getProVipPrice: function () {
                                    var t = 0;
                                    return (
                                        this.filterChargeTypes.includes(A.X.SLOW) &&
                                            !(0, a.g9)(this.info.slowProVipPrice) &&
                                            (t = this.info.slowProVipPrice),
                                        this.filterChargeTypes.includes(A.X.SUPER) &&
                                            !(0, a.g9)(this.info.superProVipPrice) &&
                                            (t = this.info.superProVipPrice),
                                        this.filterChargeTypes.includes(A.X.FAST) &&
                                        !(0, a.g9)(this.info.proVipPrice)
                                            ? this.info.proVipPrice
                                            : t
                                    );
                                },
                                isProVipPrice: function () {
                                    return (
                                        0 < this.getProVipPrice &&
                                        this.getProVipPrice == this.getVipPrice
                                    );
                                },
                                showVipPriceFlag: function () {
                                    return !(
                                        !this.info ||
                                        '1' === this.info.isVipLimit ||
                                        n.Kn === A.s8.CZH ||
                                        n.Kn === A.s8.CZHRB
                                    );
                                },
                                actTagList: function () {
                                    var t = [];
                                    return this.info && this.info.stationActTagList instanceof Array
                                        ? this.info.stationActTagList
                                        : t;
                                },
                                getDistance: function () {
                                    return this.info && this.info.distance
                                        ? (0, a.KJ)(this.info.distance, !0)
                                        : '';
                                },
                                formatDistance: function () {
                                    var t = this.getDistance;
                                    return (
                                        this.isLocation ||
                                            (this.getDistance &&
                                                this.getSaveLocationInfo &&
                                                this.getLastLocation &&
                                                this.getSaveLocationInfo.city !=
                                                    this.getLastLocation.city &&
                                                (t = '距市中心点'.concat(this.getDistance))),
                                        t
                                    );
                                },
                                formatName: function () {
                                    return this.info && this.info.stationName
                                        ? this.info.stationName || ''
                                        : '站点名称';
                                },
                                itemPromotionTag: function () {
                                    return this.info && this.info.promotionTag
                                        ? this.info.promotionTag
                                        : '';
                                },
                                isPromotionFlag: function () {
                                    return !(!this.info || '1' !== this.info.promotionFlag);
                                },
                                centerClassNames: function () {
                                    return this.info &&
                                        this.info.stationTag instanceof Array &&
                                        0 < this.info.stationTag.length
                                        ? 'mg-t-5'
                                        : 'mg-t-10';
                                },
                                getCouponList: function () {
                                    return this.info && this.info.cpnList instanceof Array
                                        ? this.info.cpnList.slice(0, 1)
                                        : [];
                                },
                                isEffStationFlag: function () {
                                    var t = !1;
                                    return (
                                        !(
                                            (0, a.g9)(this.info.stationStatus) ||
                                            ('1' !== this.info.hiddenFlag &&
                                                ['02', '03'].includes(this.info.stationStatus) &&
                                                !this.info.offLineFlag &&
                                                1 == this.info.stationType)
                                        ) || t
                                    );
                                },
                                getErrorStationStatusName: function () {
                                    var t = '';
                                    return this.info &&
                                        ('03' === this.info.stationStatus && (t = '已停运'),
                                        (0, a.g9)(t) && this.info.offLineFlag && (t = '暂停使用'),
                                        this.isEffStationFlag)
                                        ? '已失效'
                                        : t;
                                },
                                parkingDesc: function () {
                                    return this.info && !(0, a.g9)(this.info.parkingDesc)
                                        ? this.info.parkingDesc.replace(/[\r\n\s]/g, '')
                                        : '';
                                },
                            },
                        ),
                        methods: {
                            isEmptyTextCommon: a.g9,
                            imageLoad: function (t) {
                                this.$emit(
                                    'imageLoad',
                                    (0, o.A)(
                                        (0, o.A)({}, this.info),
                                        {},
                                        { distance: this.getDistance },
                                    ),
                                );
                                var e = {
                                    stationId: this.info.stationId,
                                    default_sorting: (0, a.L3)(this.sortRule),
                                    SortTab: (0, a.L3)(this.sortRule),
                                    distance: this.getDistance,
                                    dcPileFreeNum: this.info.dcPileFreeNum,
                                    dcPileNum: this.info.dcPileNum,
                                    acPileFreeNum: this.info.acPileFreeNum,
                                    acPileNum: this.info.acPileNum,
                                    superPileFreeNum: this.info.superPileFreeNum,
                                    superPileNum: this.info.superPileNum,
                                    price: this.info.price,
                                    originalPrice: this.info.originalPrice,
                                    slowPrice: this.info.slowPrice,
                                    slowOriginalPrice: this.info.slowOriginalPrice,
                                    superPrice: this.info.superPrice,
                                    superOriginalPrice: this.info.superOriginalPrice,
                                    vipPrice: this.info.vipPrice,
                                    slowVipPrice: this.info.slowVipPrice,
                                    superVipPrice: this.info.superVipPrice,
                                    proVipPrice: this.info.proVipPrice,
                                    slowProVipPrice: this.info.slowProVipPrice,
                                    superProVipPrice: this.info.superProVipPrice,
                                    stationTag:
                                        this.info.stationTag &&
                                        JSON.stringify(this.info.stationTag),
                                    tag_name: this.info.tagCopywriting,
                                };
                                (0, a.g9)(this.rank) || (e.rank = this.rank),
                                    this.sortRule == A.Df.INTELLECT &&
                                        this.getAlgorithmInfo &&
                                        ((e.flowid = this.getAlgorithmInfo.groupId || ''),
                                        (e.strategyid =
                                            this.getAlgorithmInfo.recommendStrategy || ''),
                                        this.info.top) &&
                                        (e.is_top = '是'),
                                    (0, a.H_)('StationListShow', e);
                            },
                            openMap: function () {
                                (0, a.xF)({
                                    latitude: this.info.lat,
                                    longitude: this.info.lon,
                                    name: this.info.stationName,
                                    address: this.info.stationAddr,
                                    success: function () {},
                                });
                            },
                            gotoDetailEvent: function () {
                                var t = this;
                                return (0, b.A)(
                                    (0, v.A)().mark(function e() {
                                        var i, n;
                                        return (0, v.A)().wrap(
                                            function (e) {
                                                for (;;)
                                                    switch ((e.prev = e.next)) {
                                                        case 0:
                                                            return (
                                                                (e.prev = 0),
                                                                t.$emit(
                                                                    'click',
                                                                    (0, o.A)(
                                                                        (0, o.A)({}, t.info),
                                                                        {},
                                                                        { distance: t.getDistance },
                                                                    ),
                                                                ),
                                                                (i = {
                                                                    stationId: t.info.stationId,
                                                                    default_sorting: (0, a.L3)(
                                                                        t.sortRule,
                                                                    ),
                                                                    distance: t.getDistance,
                                                                    tag_name: t.info.tagCopywriting,
                                                                }),
                                                                (0, a.g9)(t.rank) ||
                                                                    (i.rank = t.rank),
                                                                t.sortRule == A.Df.INTELLECT &&
                                                                    t.getAlgorithmInfo &&
                                                                    ((i.flowid =
                                                                        t.getAlgorithmInfo
                                                                            .groupId || ''),
                                                                    (i.strategyid =
                                                                        t.getAlgorithmInfo
                                                                            .recommendStrategy ||
                                                                        ''),
                                                                    t.info.top) &&
                                                                    (i.is_top = '是'),
                                                                (0, a.H_)('StationListClick', i),
                                                                (e.next = 9),
                                                                (0, a.SK)(200)
                                                            );
                                                        case 9:
                                                            if (
                                                                ((i = {
                                                                    stationId: t.info.stationId,
                                                                    tenantId: t.info.tenantId,
                                                                    sortRule: t.sortRule,
                                                                }),
                                                                t.pieType &&
                                                                    (i.pieType = t.pieType),
                                                                t.chargingType &&
                                                                    (i.chargingType =
                                                                        t.chargingType.join(',')),
                                                                (n = k().stringify(i)),
                                                                t.isEffStationFlag && t.isCollect)
                                                            )
                                                                return (
                                                                    t.$showToast('场站已失效'),
                                                                    e.abrupt('return')
                                                                );
                                                            e.next = 16;
                                                            break;
                                                        case 16:
                                                            return (
                                                                (e.next = 18),
                                                                (0, a.ZK)(
                                                                    '/pagesStation/station/details?'.concat(
                                                                        n,
                                                                    ),
                                                                    'push',
                                                                    {
                                                                        referrer_event:
                                                                            'StationListClick',
                                                                    },
                                                                )
                                                            );
                                                        case 18:
                                                            return e.abrupt('return');
                                                        case 21:
                                                            return (
                                                                (e.prev = 21),
                                                                (e.t0 = e.catch(0)),
                                                                e.abrupt(
                                                                    'return',
                                                                    Promise.reject(e.t0),
                                                                )
                                                            );
                                                        case 24:
                                                        case 'end':
                                                            return e.stop();
                                                    }
                                            },
                                            e,
                                            null,
                                            [[0, 21]],
                                        );
                                    }),
                                )();
                            },
                            toggleShowMore: function () {
                                this.canIShowMore &&
                                    (this.showMore
                                        ? (0, a.uk)('specialBusClick', {
                                              pageName: this.pageName || '',
                                              keyFactor: '场站活动信息关闭',
                                          })
                                        : (0, a.uk)('specialBusClick', {
                                              pageName: this.pageName || '',
                                              keyFactor: '场站活动信息展开',
                                          }),
                                    (this.showMore = !this.showMore));
                            },
                            formatNumber: function (t) {
                                return t ? t.split(',') : [0, 0];
                            },
                            goPaymemberEvent: function () {
                                var t = '/pagesPaymember/paymember/pay?sourceName='
                                    .concat(
                                        this.info.stationId,
                                        '&sourceType=station&sourceEvent={StationListClick}|{点击站点卡片}|{{站点ID},{',
                                    )
                                    .concat(this.info.stationId, '}}');
                                (0, a.ZK)(t);
                            },
                            touchDiscountEvent: function (t) {
                                t.link && (0, a.ZK)(t.link);
                            },
                            closeEvent: function () {
                                this.$emit('close', this.info);
                            },
                            jumpGreenIntegral: function () {
                                var t;
                                this.getSystemParams.greenIntegralUrl &&
                                    ((t = {
                                        pageName: '场站item',
                                        linkType: '02',
                                        contentUrl: this.getSystemParams.greenIntegralUrl,
                                        loginFlag: '1',
                                        jumpOrign: '场站item绿电氛围',
                                    }),
                                    (0, a.Kr)(t));
                            },
                            formatDiscountPriceResult: function (t) {
                                var e = !1;
                                return {
                                    flag: (e =
                                        !!(
                                            this.getDiscountPrice &&
                                            0 <= this.getDiscountPrice.indexOf(t)
                                        ) || e),
                                    result: ''
                                        .concat(t, ' | ')
                                        .concat(this.getDiscountPrice.replace(t, '')),
                                };
                            },
                        },
                    }),
                    (C =
                        (i(31238),
                        (0, s.A)(
                            w,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return i(
                                    'v-uni-view',
                                    { staticClass: 'station-item-box' },
                                    [
                                        !t.isCollect && t.isGreenTag
                                            ? i(
                                                  'v-uni-view',
                                                  {
                                                      staticClass: 'green-tips-bar',
                                                      on: {
                                                          click: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.jumpGreenIntegral.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                      },
                                                  },
                                                  [
                                                      i('v-uni-image', {
                                                          staticClass: 'green-station-bg',
                                                          attrs: {
                                                              src:
                                                                  t.IMG_URL +
                                                                  '/static/images/active/green/green-station.png',
                                                          },
                                                      }),
                                                  ],
                                                  1,
                                              )
                                            : t._e(),
                                        i(
                                            'v-uni-view',
                                            {
                                                class: ['station-item', { shadow: t.isShadow }],
                                                style: [{ backgroundColor: t.itemBgColor }],
                                                on: {
                                                    click: function (e) {
                                                        (arguments[0] = e = t.$handleEvent(e)),
                                                            t.gotoDetailEvent.apply(
                                                                void 0,
                                                                arguments,
                                                            );
                                                    },
                                                },
                                            },
                                            [
                                                !t.isCollect &&
                                                t.isPromotionFlag &&
                                                t.itemPromotionTag
                                                    ? i(
                                                          'v-uni-view',
                                                          { staticClass: 'promotion-tips' },
                                                          [
                                                              i('v-uni-image', {
                                                                  staticClass: 'promotion-tips-bg',
                                                                  attrs: {
                                                                      src:
                                                                          t.IMG_URL +
                                                                          '/static/images/active/promo/tip_img.png',
                                                                  },
                                                              }),
                                                              i(
                                                                  'div',
                                                                  {
                                                                      staticClass:
                                                                          'promotion-tips-content',
                                                                  },
                                                                  [t._v(t._s(t.itemPromotionTag))],
                                                              ),
                                                          ],
                                                          1,
                                                      )
                                                    : t.info.tagName
                                                    ? i(
                                                          'v-uni-view',
                                                          { staticClass: 'promotion-tips' },
                                                          [
                                                              i('v-uni-image', {
                                                                  staticClass: 'promotion-tips-bg',
                                                                  attrs: {
                                                                      src:
                                                                          t.IMG_URL +
                                                                          '/static/images/station/' +
                                                                          (t.info.tagStyle ||
                                                                              'style5') +
                                                                          '.png',
                                                                  },
                                                              }),
                                                              i(
                                                                  'div',
                                                                  {
                                                                      staticClass:
                                                                          'promotion-tips-content',
                                                                  },
                                                                  [
                                                                      t._v(
                                                                          t._s(
                                                                              t.info.tagCopywriting,
                                                                          ),
                                                                      ),
                                                                  ],
                                                              ),
                                                          ],
                                                          1,
                                                      )
                                                    : t._e(),
                                                i(
                                                    'v-uni-view',
                                                    {
                                                        class: [
                                                            'main-view',
                                                            {
                                                                large:
                                                                    (t.isPromotionFlag &&
                                                                        t.itemPromotionTag) ||
                                                                    t.info.tagName,
                                                            },
                                                        ],
                                                    },
                                                    [
                                                        i(
                                                            'v-uni-view',
                                                            { staticClass: 'name-bar' },
                                                            [
                                                                i(
                                                                    'v-uni-view',
                                                                    {
                                                                        staticClass:
                                                                            'name-info text-line',
                                                                    },
                                                                    [
                                                                        t.getErrorStationStatusName
                                                                            ? i(
                                                                                  'v-uni-view',
                                                                                  {
                                                                                      staticClass:
                                                                                          'offline-tag',
                                                                                  },
                                                                                  [
                                                                                      t._v(
                                                                                          t._s(
                                                                                              t.getErrorStationStatusName,
                                                                                          ),
                                                                                      ),
                                                                                  ],
                                                                              )
                                                                            : '1' == t.info.goodFlag
                                                                            ? i(
                                                                                  'v-uni-view',
                                                                                  {
                                                                                      staticClass:
                                                                                          'top-tag',
                                                                                  },
                                                                                  [
                                                                                      i(
                                                                                          'v-uni-image',
                                                                                          {
                                                                                              staticClass:
                                                                                                  'top-tag-icon',
                                                                                              attrs: {
                                                                                                  src:
                                                                                                      t.IMG_URL +
                                                                                                      '/static/images/common/thumbs.png',
                                                                                                  'lazy-load':
                                                                                                      !0,
                                                                                              },
                                                                                          },
                                                                                      ),
                                                                                      i(
                                                                                          'v-uni-text',
                                                                                          [
                                                                                              t._v(
                                                                                                  '精品',
                                                                                              ),
                                                                                          ],
                                                                                      ),
                                                                                  ],
                                                                                  1,
                                                                              )
                                                                            : t._e(),
                                                                        i(
                                                                            'v-uni-view',
                                                                            {
                                                                                class: [
                                                                                    'name',
                                                                                    'text-line',
                                                                                    {
                                                                                        short: t.getErrorStationStatusName,
                                                                                    },
                                                                                ],
                                                                            },
                                                                            [
                                                                                t._v(
                                                                                    t._s(
                                                                                        t.formatName ||
                                                                                            '',
                                                                                    ),
                                                                                ),
                                                                            ],
                                                                        ),
                                                                    ],
                                                                    1,
                                                                ),
                                                                t.showClose
                                                                    ? i(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'close-btn',
                                                                              on: {
                                                                                  click: function (
                                                                                      e,
                                                                                  ) {
                                                                                      e.stopPropagation(),
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                          t.closeEvent.apply(
                                                                                              void 0,
                                                                                              arguments,
                                                                                          );
                                                                                  },
                                                                              },
                                                                          },
                                                                          [
                                                                              i('v-uni-image', {
                                                                                  staticClass:
                                                                                      'seat-img',
                                                                                  attrs: {
                                                                                      src:
                                                                                          t.IMG_URL +
                                                                                          '/static/images/common/list-biz-seat.png',
                                                                                      'lazy-load':
                                                                                          !0,
                                                                                  },
                                                                                  on: {
                                                                                      load: function (
                                                                                          e,
                                                                                      ) {
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                              t.imageLoad.apply(
                                                                                                  void 0,
                                                                                                  arguments,
                                                                                              );
                                                                                      },
                                                                                  },
                                                                              }),
                                                                              i('v-uni-text', {
                                                                                  staticClass:
                                                                                      'iconfont iconguanbi',
                                                                              }),
                                                                          ],
                                                                          1,
                                                                      )
                                                                    : t.showPosition
                                                                    ? i(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'position-btn',
                                                                              on: {
                                                                                  click: function (
                                                                                      e,
                                                                                  ) {
                                                                                      e.stopPropagation(),
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                          t.openMap.apply(
                                                                                              void 0,
                                                                                              arguments,
                                                                                          );
                                                                                  },
                                                                              },
                                                                          },
                                                                          [
                                                                              i('v-uni-image', {
                                                                                  staticClass:
                                                                                      'seat-img',
                                                                                  attrs: {
                                                                                      src:
                                                                                          t.IMG_URL +
                                                                                          '/static/images/common/list-biz-seat.png',
                                                                                      'lazy-load':
                                                                                          !0,
                                                                                  },
                                                                                  on: {
                                                                                      load: function (
                                                                                          e,
                                                                                      ) {
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                              t.imageLoad.apply(
                                                                                                  void 0,
                                                                                                  arguments,
                                                                                              );
                                                                                      },
                                                                                  },
                                                                              }),
                                                                              i('v-uni-text', {
                                                                                  staticClass:
                                                                                      'iconfont icondaohang',
                                                                              }),
                                                                              t.hideDest
                                                                                  ? t._e()
                                                                                  : i(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            staticClass:
                                                                                                'pd-l-10',
                                                                                        },
                                                                                        [
                                                                                            t._v(
                                                                                                t._s(
                                                                                                    t.formatDistance ||
                                                                                                        '',
                                                                                                ),
                                                                                            ),
                                                                                        ],
                                                                                    ),
                                                                          ],
                                                                          1,
                                                                      )
                                                                    : t._e(),
                                                            ],
                                                            1,
                                                        ),
                                                        0 < t.getStationTag.length
                                                            ? i(
                                                                  'v-uni-view',
                                                                  { staticClass: 'tags-bar' },
                                                                  [
                                                                      i(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'tag-list',
                                                                          },
                                                                          t._l(
                                                                              t.getStationTag,
                                                                              function (e, n) {
                                                                                  return i(
                                                                                      'v-uni-view',
                                                                                      {
                                                                                          key: n,
                                                                                          staticClass:
                                                                                              'tag-item',
                                                                                      },
                                                                                      [
                                                                                          t._v(
                                                                                              t._s(
                                                                                                  e.tagName ||
                                                                                                      '',
                                                                                              ),
                                                                                          ),
                                                                                      ],
                                                                                  );
                                                                              },
                                                                          ),
                                                                          1,
                                                                      ),
                                                                  ],
                                                                  1,
                                                              )
                                                            : t._e(),
                                                        i(
                                                            'v-uni-view',
                                                            {
                                                                class: [
                                                                    'center-view',
                                                                    t.centerClassNames,
                                                                ],
                                                            },
                                                            [
                                                                i(
                                                                    'v-uni-view',
                                                                    { staticClass: 'left-view' },
                                                                    [
                                                                        t.showStationPrice
                                                                            ? i(
                                                                                  'v-uni-view',
                                                                                  {
                                                                                      staticClass:
                                                                                          'price-view',
                                                                                  },
                                                                                  [
                                                                                      i(
                                                                                          'v-uni-view',
                                                                                          {
                                                                                              class: [
                                                                                                  'price-bar',
                                                                                                  {
                                                                                                      'mg-t-5':
                                                                                                          !t.isCollect,
                                                                                                  },
                                                                                              ],
                                                                                          },
                                                                                          [
                                                                                              i(
                                                                                                  'v-uni-view',
                                                                                                  {
                                                                                                      class: [
                                                                                                          'price',
                                                                                                      ],
                                                                                                  },
                                                                                                  [
                                                                                                      i(
                                                                                                          'v-uni-text',
                                                                                                          {
                                                                                                              staticClass:
                                                                                                                  'price-unit mg-t-15',
                                                                                                          },
                                                                                                          [
                                                                                                              t._v(
                                                                                                                  '￥',
                                                                                                              ),
                                                                                                          ],
                                                                                                      ),
                                                                                                      i(
                                                                                                          'v-uni-text',
                                                                                                          {
                                                                                                              staticClass:
                                                                                                                  'price-value text-number',
                                                                                                          },
                                                                                                          [
                                                                                                              t._v(
                                                                                                                  t._s(
                                                                                                                      t.showStationPrice,
                                                                                                                  ),
                                                                                                              ),
                                                                                                          ],
                                                                                                      ),
                                                                                                      !t.isCollect &&
                                                                                                      t.getDiscountPrice &&
                                                                                                      t.chargingTypeCount <
                                                                                                          3
                                                                                                          ? t._e()
                                                                                                          : i(
                                                                                                                'v-uni-text',
                                                                                                                {
                                                                                                                    staticClass:
                                                                                                                        'price-unit mg-t-10',
                                                                                                                },
                                                                                                                [
                                                                                                                    t._v(
                                                                                                                        '/度',
                                                                                                                    ),
                                                                                                                ],
                                                                                                            ),
                                                                                                  ],
                                                                                                  1,
                                                                                              ),
                                                                                              !t.isCollect &&
                                                                                              t.getDiscountPrice &&
                                                                                              t.formatDiscountPrice
                                                                                                  ? i(
                                                                                                        'v-uni-view',
                                                                                                        {
                                                                                                            class: [
                                                                                                                'discount-price',
                                                                                                            ],
                                                                                                        },
                                                                                                        [
                                                                                                            t._v(
                                                                                                                t._s(
                                                                                                                    t.formatDiscountPrice,
                                                                                                                ),
                                                                                                            ),
                                                                                                        ],
                                                                                                    )
                                                                                                  : t._e(),
                                                                                          ],
                                                                                          1,
                                                                                      ),
                                                                                      t.isCollect
                                                                                          ? i(
                                                                                                'PieBar',
                                                                                                {
                                                                                                    attrs: {
                                                                                                        info: t.info,
                                                                                                    },
                                                                                                },
                                                                                            )
                                                                                          : t._e(),
                                                                                  ],
                                                                                  1,
                                                                              )
                                                                            : t._e(),
                                                                    ],
                                                                    1,
                                                                ),
                                                                i(
                                                                    'v-uni-view',
                                                                    {
                                                                        staticClass:
                                                                            'right-view mg-t-10',
                                                                    },
                                                                    [
                                                                        t.isCollect
                                                                            ? i(
                                                                                  'v-uni-view',
                                                                                  {
                                                                                      staticClass:
                                                                                          'charge-count',
                                                                                  },
                                                                                  [
                                                                                      0 <
                                                                                      t.info
                                                                                          .orderNumber
                                                                                          ? [
                                                                                                t._v(
                                                                                                    '充过' +
                                                                                                        t._s(
                                                                                                            t
                                                                                                                .info
                                                                                                                .orderNumber ||
                                                                                                                0,
                                                                                                        ) +
                                                                                                        '次',
                                                                                                ),
                                                                                            ]
                                                                                          : t._e(),
                                                                                  ],
                                                                                  2,
                                                                              )
                                                                            : i('PieBar', {
                                                                                  attrs: {
                                                                                      info: t.info,
                                                                                  },
                                                                              }),
                                                                    ],
                                                                    1,
                                                                ),
                                                            ],
                                                            1,
                                                        ),
                                                        !t.isCollect &&
                                                        0 < t.getSliceActTagList.length
                                                            ? i(
                                                                  'v-uni-view',
                                                                  {
                                                                      staticClass:
                                                                          'station-taglist',
                                                                  },
                                                                  [
                                                                      i(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'tag-list',
                                                                          },
                                                                          [
                                                                              t._l(
                                                                                  t.getSliceActTagList,
                                                                                  function (e, n) {
                                                                                      return [
                                                                                          e.vip
                                                                                              ? i(
                                                                                                    'v-uni-view',
                                                                                                    {
                                                                                                        class: [
                                                                                                            'tag-vip-item',
                                                                                                            {
                                                                                                                pro: t.getProVipPrice,
                                                                                                            },
                                                                                                        ],
                                                                                                    },
                                                                                                    [
                                                                                                        i(
                                                                                                            'v-uni-view',
                                                                                                            {
                                                                                                                staticClass:
                                                                                                                    'vip-item-label',
                                                                                                            },
                                                                                                            [
                                                                                                                t.getProVipPrice
                                                                                                                    ? [
                                                                                                                          t._v(
                                                                                                                              'Pro会员专享',
                                                                                                                          ),
                                                                                                                      ]
                                                                                                                    : [
                                                                                                                          t._v(
                                                                                                                              '会员价',
                                                                                                                          ),
                                                                                                                      ],
                                                                                                            ],
                                                                                                            2,
                                                                                                        ),
                                                                                                        i(
                                                                                                            'v-uni-view',
                                                                                                            {
                                                                                                                staticClass:
                                                                                                                    'vip-item-value',
                                                                                                            },
                                                                                                            [
                                                                                                                i(
                                                                                                                    'v-uni-text',
                                                                                                                    {
                                                                                                                        staticClass:
                                                                                                                            'unit',
                                                                                                                    },
                                                                                                                    [
                                                                                                                        t._v(
                                                                                                                            '¥',
                                                                                                                        ),
                                                                                                                    ],
                                                                                                                ),
                                                                                                                i(
                                                                                                                    'v-uni-text',
                                                                                                                    {
                                                                                                                        staticClass:
                                                                                                                            'text-number',
                                                                                                                    },
                                                                                                                    [
                                                                                                                        t._v(
                                                                                                                            t._s(
                                                                                                                                e.actName,
                                                                                                                            ),
                                                                                                                        ),
                                                                                                                    ],
                                                                                                                ),
                                                                                                            ],
                                                                                                            1,
                                                                                                        ),
                                                                                                    ],
                                                                                                    1,
                                                                                                )
                                                                                              : i(
                                                                                                    'v-uni-view',
                                                                                                    {
                                                                                                        key: n,
                                                                                                        class: [
                                                                                                            'act-tag-item',
                                                                                                            {
                                                                                                                light: e.light,
                                                                                                            },
                                                                                                        ],
                                                                                                        on: {
                                                                                                            click: function (
                                                                                                                i,
                                                                                                            ) {
                                                                                                                i.stopPropagation(),
                                                                                                                    (arguments[0] =
                                                                                                                        i =
                                                                                                                            t.$handleEvent(
                                                                                                                                i,
                                                                                                                            )),
                                                                                                                    t.touchDiscountEvent(
                                                                                                                        e,
                                                                                                                    );
                                                                                                            },
                                                                                                        },
                                                                                                    },
                                                                                                    [
                                                                                                        t._v(
                                                                                                            t._s(
                                                                                                                e.actName,
                                                                                                            ),
                                                                                                        ),
                                                                                                    ],
                                                                                                ),
                                                                                      ];
                                                                                  },
                                                                              ),
                                                                              0 < t.moreActNum
                                                                                  ? i(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            staticClass:
                                                                                                'more-item',
                                                                                        },
                                                                                        [
                                                                                            t._v(
                                                                                                t._s(
                                                                                                    t.moreDiscountActTagNum,
                                                                                                ) +
                                                                                                    '项优惠',
                                                                                            ),
                                                                                            i(
                                                                                                'v-uni-text',
                                                                                                {
                                                                                                    staticClass:
                                                                                                        'iconfont iconjiantou',
                                                                                                },
                                                                                            ),
                                                                                        ],
                                                                                        1,
                                                                                    )
                                                                                  : t._e(),
                                                                          ],
                                                                          2,
                                                                      ),
                                                                  ],
                                                                  1,
                                                              )
                                                            : t._e(),
                                                        !t.isCollect && t.info.parkingDesc
                                                            ? i(
                                                                  'v-uni-view',
                                                                  { staticClass: 'parking-bar' },
                                                                  [
                                                                      i(
                                                                          'v-uni-text',
                                                                          {
                                                                              staticClass:
                                                                                  'tag-icon',
                                                                          },
                                                                          [t._v('P')],
                                                                      ),
                                                                      i(
                                                                          'v-uni-view',
                                                                          {
                                                                              staticClass:
                                                                                  'desc text-line',
                                                                          },
                                                                          [
                                                                              t.info
                                                                                  .parkPriceLabelDisplayName
                                                                                  ? i(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            staticClass:
                                                                                                'desc-lable mg-r-5',
                                                                                        },
                                                                                        [
                                                                                            t._v(
                                                                                                t._s(
                                                                                                    t
                                                                                                        .info
                                                                                                        .parkPriceLabelDisplayName
                                                                                                        ? t
                                                                                                              .info
                                                                                                              .parkPriceLabelDisplayName +
                                                                                                              ':'
                                                                                                        : '',
                                                                                                ),
                                                                                            ),
                                                                                        ],
                                                                                    )
                                                                                  : t._e(),
                                                                              i(
                                                                                  'v-uni-text',
                                                                                  {
                                                                                      staticClass:
                                                                                          'desc-value text-line',
                                                                                  },
                                                                                  [
                                                                                      t._v(
                                                                                          t._s(
                                                                                              t.parkingDesc,
                                                                                          ),
                                                                                      ),
                                                                                  ],
                                                                              ),
                                                                          ],
                                                                          1,
                                                                      ),
                                                                  ],
                                                                  1,
                                                              )
                                                            : t._e(),
                                                    ],
                                                    1,
                                                ),
                                            ],
                                            1,
                                        ),
                                    ],
                                    1,
                                );
                            },
                            [],
                            !1,
                            null,
                            '64d40be0',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (w = i(15741)),
                    (h = {
                        props: {
                            moduleInfo: { type: Object, required: !0 },
                            assemblyClaimInfo: { type: Object },
                            isPreview: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                        },
                        components: {
                            ChargeTaskList: h,
                            ModuleInfoItem: f,
                            TaskStepItem: p,
                            SiginInfo: m,
                            SigninBar: g,
                            AwardItem: u,
                            StationItem: C,
                            DialogWindow: w.A,
                        },
                        provide: function () {
                            return { moduleActInfo: this.moduleActInfo };
                        },
                        inject: ['assemblyInfo'],
                        data: function () {
                            return {
                                IMG_URL: n.sE,
                                REWARD_TYPES: l.CC,
                                TASK_STATUS: l.Rr,
                                MODULE_ACT_TYPES: l.M5,
                                isToggle: !1,
                            };
                        },
                        mounted: function () {
                            this.isTaskAct &&
                                0 < this.moduleActTaskInfoList.length &&
                                this.taskShowEvent({});
                        },
                        computed: {
                            showPreview: function () {
                                return this.isPreview;
                            },
                            relaActType: function () {
                                return this.moduleInfo && this.moduleInfo.relaActType
                                    ? this.moduleInfo.relaActType
                                    : null;
                            },
                            relaActSubType: function () {
                                return this.moduleInfo && this.moduleInfo.relaActSubType
                                    ? this.moduleInfo.relaActSubType
                                    : null;
                            },
                            isSignAct: function () {
                                return this.relaActType === l.M5.SIGN;
                            },
                            isTaskAct: function () {
                                return (
                                    this.relaActType === l.M5.TASK &&
                                    this.relaActSubType !== l.jr.RECHARGE
                                );
                            },
                            isRechargeAct: function () {
                                return (
                                    this.relaActType === l.M5.RECHARGE &&
                                    this.relaActSubType === l.jr.RECHARGE
                                );
                            },
                            isAwardAct: function () {
                                return (
                                    (this.relaActType === l.M5.AWARD &&
                                        this.relaActSubType === l.jr.AWARD) ||
                                    this.relaActType === l.M5.EQUITY
                                );
                            },
                            isStationRemember: function () {
                                return this.relaActType === l.M5.STATION_REMEMBER;
                            },
                            moduleActInfo: function () {
                                return this.moduleInfo && this.moduleInfo.moduleActInfo
                                    ? this.moduleInfo.moduleActInfo
                                    : null;
                            },
                            curTaskList: function () {
                                return this.isToggle
                                    ? this.moduleActTaskInfoList
                                    : this.moduleActTaskInfoList.slice(0, 3);
                            },
                            moduleActTaskInfoList: function () {
                                return this.moduleActInfo &&
                                    this.moduleActInfo.moduleActTaskInfoList instanceof Array
                                    ? this.moduleActInfo.moduleActTaskInfoList
                                    : [];
                            },
                            mainModuleConfQueryVo: function () {
                                return this.moduleInfo && this.moduleInfo.mainModuleConfQueryVo
                                    ? this.moduleInfo.mainModuleConfQueryVo
                                    : null;
                            },
                            progressInfo: function () {
                                if (this.moduleActTaskInfoList instanceof Array) {
                                    var t = this.moduleActTaskInfoList.find(function (t) {
                                        return (
                                            t.currTaskConfQueryVo &&
                                            1 == t.currTaskConfQueryVo.showProgressFlag
                                        );
                                    });
                                    if (t)
                                        return {
                                            showProgressFlag: (t = t.currTaskConfQueryVo)
                                                .showProgressFlag,
                                            taskId: t.taskId,
                                            showContent: t.showContent,
                                        };
                                }
                                return null;
                            },
                            isMergeAct: function () {
                                return !(
                                    !this.moduleActInfo ||
                                    this.moduleActInfo.rewardType !== l.CC.MERGE
                                );
                            },
                            joinLimit: function () {
                                return this.moduleActInfo &&
                                    !(0, a.g9)(this.moduleActInfo.joinLimit)
                                    ? Number(this.moduleActInfo.joinLimit)
                                    : -1;
                            },
                            isFullJoin: function () {
                                var t = 0;
                                if (0 < this.joinLimit) {
                                    var e,
                                        i = (0, r.A)(this.moduleActTaskInfoList);
                                    try {
                                        for (i.s(); !(e = i.n()).done; ) {
                                            var n = e.value;
                                            (n.taskProgressStatus !== l.Rr.COMPLETE &&
                                                n.taskProgressStatus !== l.Rr.CLAIM) ||
                                                t++;
                                        }
                                    } catch (t) {
                                        i.e(t);
                                    } finally {
                                        i.f();
                                    }
                                    if (this.joinLimit <= t) return !0;
                                }
                                return !1;
                            },
                            signReceive: function () {
                                return this.moduleInfo && this.moduleInfo.vipSignReceiveVo
                                    ? this.moduleInfo.vipSignReceiveVo
                                    : null;
                            },
                            signinList: function () {
                                return this.signReceive &&
                                    this.signReceive.signList instanceof Array
                                    ? this.signReceive.signList
                                    : [];
                            },
                            stationQueryVos: function () {
                                return this.moduleInfo && this.moduleInfo.stationQueryVos
                                    ? this.moduleInfo.stationQueryVos
                                    : null;
                            },
                        },
                        methods: {
                            reloadEvent: function (t) {
                                this.$emit('reloadEvent', t);
                            },
                            moduleBtnEvent: function (t) {
                                this.$emit('moduleBtnEvent', t);
                            },
                            taskBtnEvent: function () {
                                this.$emit(
                                    'taskBtnEvent',
                                    (0, o.A)(
                                        (0, o.A)(
                                            {},
                                            0 < arguments.length && void 0 !== arguments[0]
                                                ? arguments[0]
                                                : {},
                                        ),
                                        {},
                                        { moduleInfo: this.moduleInfo },
                                    ),
                                );
                            },
                            taskShowEvent: function () {
                                this.$emit(
                                    'taskShowEvent',
                                    (0, o.A)(
                                        (0, o.A)(
                                            {},
                                            0 < arguments.length && void 0 !== arguments[0]
                                                ? arguments[0]
                                                : {},
                                        ),
                                        {},
                                        { moduleInfo: this.moduleInfo },
                                    ),
                                );
                            },
                            moduleSignEvent: function (t) {
                                this.$emit('moduleSignEvent', {
                                    signReceive: t,
                                    moduleInfo: this.moduleInfo,
                                });
                            },
                            moduleAwardEvent: function () {
                                this.$emit(
                                    'moduleAwardEvent',
                                    0 < arguments.length && void 0 !== arguments[0]
                                        ? arguments[0]
                                        : {},
                                );
                            },
                            touchRechargeCardEvent: (0, c.n)(function () {
                                var t, e;
                                this.mainModuleConfQueryVo &&
                                    this.mainModuleConfQueryVo.cardBtn &&
                                    ((e = this.mainModuleConfQueryVo.cardBtn.btnPath),
                                    (0, a.uk)(
                                        'PutClick',
                                        (0, o.A)(
                                            (0, o.A)({}, this.assemblyInfo.puttingVo),
                                            {},
                                            {
                                                putId:
                                                    null == (t = this.assemblyInfo) ||
                                                    null == (t = t.puttingVo)
                                                        ? void 0
                                                        : t.id,
                                                putName:
                                                    null == (t = this.assemblyInfo) ||
                                                    null == (t = t.puttingVo)
                                                        ? void 0
                                                        : t.puttingName,
                                                standType: '组件投放卡片点击',
                                                assemblyId:
                                                    null == (t = this.assemblyInfo)
                                                        ? void 0
                                                        : t.assemblyId,
                                                assemblymoduleId: this.moduleActInfo.moduleId,
                                            },
                                        ),
                                    ),
                                    (0, a.ZK)(e));
                            }, 1e3),
                        },
                    }),
                    (f =
                        (i(48295),
                        (0, s.A)(
                            h,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return i(
                                    'v-uni-view',
                                    { class: ['task-item', { full: t.isStationRemember }] },
                                    [
                                        t.isRechargeAct
                                            ? i(
                                                  'v-uni-view',
                                                  {
                                                      on: {
                                                          click: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.touchRechargeCardEvent.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                      },
                                                  },
                                                  [
                                                      i('ModuleInfoItem', {
                                                          attrs: {
                                                              info: t.moduleInfo,
                                                              progressInfo: t.progressInfo,
                                                          },
                                                          on: {
                                                              btnEvent: function (e) {
                                                                  (arguments[0] = e =
                                                                      t.$handleEvent(e)),
                                                                      t.moduleBtnEvent.apply(
                                                                          void 0,
                                                                          arguments,
                                                                      );
                                                              },
                                                              reloadEvent: function (e) {
                                                                  (arguments[0] = e =
                                                                      t.$handleEvent(e)),
                                                                      t.reloadEvent.apply(
                                                                          void 0,
                                                                          arguments,
                                                                      );
                                                              },
                                                          },
                                                      }),
                                                      i(
                                                          'v-uni-view',
                                                          { staticClass: 'mg-t-20' },
                                                          [
                                                              i('ChargeTaskList', {
                                                                  attrs: {
                                                                      taskList:
                                                                          t.moduleActTaskInfoList,
                                                                  },
                                                                  on: {
                                                                      doEvent: function (e) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.taskBtnEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                  },
                                                              }),
                                                          ],
                                                          1,
                                                      ),
                                                  ],
                                                  1,
                                              )
                                            : t.isTaskAct
                                            ? [
                                                  i('ModuleInfoItem', {
                                                      attrs: { info: t.moduleInfo },
                                                      on: {
                                                          btnEvent: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.moduleBtnEvent.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                          reloadEvent: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.reloadEvent.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                      },
                                                  }),
                                                  0 < t.moduleActTaskInfoList.length
                                                      ? [
                                                            i(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'task-step-list mg-t-20',
                                                                },
                                                                [
                                                                    t._l(
                                                                        t.curTaskList,
                                                                        function (e, n) {
                                                                            return i(
                                                                                'v-uni-view',
                                                                                {
                                                                                    key: n,
                                                                                    staticClass:
                                                                                        'task-step-item',
                                                                                },
                                                                                [
                                                                                    i(
                                                                                        'TaskStepItem',
                                                                                        {
                                                                                            attrs: {
                                                                                                moduleActInfo:
                                                                                                    t.moduleActInfo,
                                                                                                taskInfo:
                                                                                                    e,
                                                                                                disabled:
                                                                                                    t.isFullJoin &&
                                                                                                    e.taskProgressStatus !==
                                                                                                        t
                                                                                                            .TASK_STATUS
                                                                                                            .CLAIM &&
                                                                                                    e.taskProgressStatus !==
                                                                                                        t
                                                                                                            .TASK_STATUS
                                                                                                            .COMPLETE,
                                                                                            },
                                                                                            on: {
                                                                                                doEvent:
                                                                                                    function (
                                                                                                        e,
                                                                                                    ) {
                                                                                                        (arguments[0] =
                                                                                                            e =
                                                                                                                t.$handleEvent(
                                                                                                                    e,
                                                                                                                )),
                                                                                                            t.taskBtnEvent.apply(
                                                                                                                void 0,
                                                                                                                arguments,
                                                                                                            );
                                                                                                    },
                                                                                                taskShowEvent:
                                                                                                    function (
                                                                                                        e,
                                                                                                    ) {
                                                                                                        (arguments[0] =
                                                                                                            e =
                                                                                                                t.$handleEvent(
                                                                                                                    e,
                                                                                                                )),
                                                                                                            t.taskShowEvent.apply(
                                                                                                                void 0,
                                                                                                                arguments,
                                                                                                            );
                                                                                                    },
                                                                                            },
                                                                                        },
                                                                                    ),
                                                                                ],
                                                                                1,
                                                                            );
                                                                        },
                                                                    ),
                                                                    t.moduleActTaskInfoList &&
                                                                    3 <
                                                                        t.moduleActTaskInfoList
                                                                            .length
                                                                        ? i(
                                                                              'v-uni-view',
                                                                              {
                                                                                  staticClass:
                                                                                      'toggle-bar',
                                                                                  on: {
                                                                                      click: function (
                                                                                          e,
                                                                                      ) {
                                                                                          (arguments[0] =
                                                                                              e =
                                                                                                  t.$handleEvent(
                                                                                                      e,
                                                                                                  )),
                                                                                              (t.isToggle =
                                                                                                  !t.isToggle);
                                                                                      },
                                                                                  },
                                                                              },
                                                                              [
                                                                                  t._v(
                                                                                      t._s(
                                                                                          t.isToggle
                                                                                              ? '收起'
                                                                                              : '查看更多任务',
                                                                                      ),
                                                                                  ),
                                                                                  i('v-uni-text', {
                                                                                      class: [
                                                                                          'iconfont',
                                                                                          'iconxiala1',
                                                                                          {
                                                                                              toggle: !t.isToggle,
                                                                                          },
                                                                                      ],
                                                                                  }),
                                                                              ],
                                                                              1,
                                                                          )
                                                                        : t._e(),
                                                                ],
                                                                2,
                                                            ),
                                                        ]
                                                      : t._e(),
                                              ]
                                            : t.isSignAct
                                            ? [
                                                  t.showPreview
                                                      ? [
                                                            i('v-uni-image', {
                                                                staticClass: 'sign-preview',
                                                                attrs: {
                                                                    mode: 'widthFix',
                                                                    src:
                                                                        t.IMG_URL +
                                                                        '/static/images/task/sign-preview.png',
                                                                },
                                                            }),
                                                        ]
                                                      : [
                                                            i('SiginInfo', {
                                                                attrs: {
                                                                    info: t.moduleInfo,
                                                                    getSigninInfo: t.signReceive,
                                                                },
                                                                on: {
                                                                    signEvent: function (e) {
                                                                        (arguments[0] = e =
                                                                            t.$handleEvent(e)),
                                                                            t.moduleSignEvent.apply(
                                                                                void 0,
                                                                                arguments,
                                                                            );
                                                                    },
                                                                },
                                                            }),
                                                            t.signReceive && 0 < t.signinList.length
                                                                ? i(
                                                                      'v-uni-view',
                                                                      {
                                                                          staticClass:
                                                                              'sign-list-space mg-t-20',
                                                                      },
                                                                      [
                                                                          i('SigninBar', {
                                                                              attrs: {
                                                                                  signinList:
                                                                                      t.signinList,
                                                                              },
                                                                          }),
                                                                      ],
                                                                      1,
                                                                  )
                                                                : t._e(),
                                                        ],
                                              ]
                                            : t.isAwardAct
                                            ? [
                                                  i('AwardItem', {
                                                      attrs: {
                                                          info: t.moduleInfo,
                                                          assemblyClaimInfo: t.assemblyClaimInfo,
                                                      },
                                                      on: {
                                                          btnEvent: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.moduleAwardEvent.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                          reloadEvent: function (e) {
                                                              (arguments[0] = e =
                                                                  t.$handleEvent(e)),
                                                                  t.reloadEvent.apply(
                                                                      void 0,
                                                                      arguments,
                                                                  );
                                                          },
                                                      },
                                                  }),
                                              ]
                                            : t.isStationRemember
                                            ? [
                                                  t._l(t.stationQueryVos, function (e, n) {
                                                      return [
                                                          t.showPreview
                                                              ? [
                                                                    i('v-uni-image', {
                                                                        key: n,
                                                                        staticClass:
                                                                            'station-preview mg-b-20',
                                                                        attrs: {
                                                                            mode: 'widthFix',
                                                                            src:
                                                                                t.IMG_URL +
                                                                                '/static/images/task/station_preview.png',
                                                                        },
                                                                    }),
                                                                ]
                                                              : [
                                                                    i('StationItem', {
                                                                        key: n,
                                                                        attrs: { info: e },
                                                                    }),
                                                                ],
                                                      ];
                                                  }),
                                              ]
                                            : t._e(),
                                    ],
                                    2,
                                );
                            },
                            [],
                            !1,
                            null,
                            '572d4ea1',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (p = {
                        props: {
                            queryParams: {
                                type: Object,
                                default: function () {
                                    return {};
                                },
                            },
                            assemblySourceType: { type: String },
                            assemblyInfo: { type: Object, require: !0 },
                            isPreview: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                            hideHeader: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                        },
                        provide: function () {
                            return {
                                assemblyInfo: this.assemblyInfo,
                                assemblySourceType: this.assemblySourceType,
                                queryParams: this.queryParams,
                            };
                        },
                        components: { AssemblyLayout: e, ModuleItem: f },
                        data: function () {
                            return { IMG_URL: n.sE, ASSEMBLY_STYLE_TYPES: l.$J };
                        },
                        computed: {
                            assemblyClaimInfo: function () {
                                var t;
                                return this.assemblyInfo
                                    ? {
                                          puttingVo: this.assemblyInfo.puttingVo,
                                          putId:
                                              null == (t = this.assemblyInfo) ||
                                              null == (t = t.puttingVo)
                                                  ? void 0
                                                  : t.id,
                                          putName:
                                              null == (t = this.assemblyInfo) ||
                                              null == (t = t.puttingVo)
                                                  ? void 0
                                                  : t.puttingName,
                                          assemblyId: this.assemblyInfo.assemblyId,
                                      }
                                    : {};
                            },
                            moduleQueryVoList: function () {
                                return this.assemblyInfo &&
                                    this.assemblyInfo.moduleQueryVoList instanceof Array
                                    ? this.assemblyInfo.moduleQueryVoList
                                    : [];
                            },
                            assemblyStyle: function () {
                                return this.assemblyInfo && this.assemblyInfo.assemblyStyle
                                    ? this.assemblyInfo.assemblyStyle
                                    : l.$J.DEFAULT;
                            },
                            assemblyStyleInfo: function () {
                                switch (this.assemblyStyle) {
                                    case l.$J.DEFAULT:
                                        return {
                                            background: '#fff',
                                            showDivider: !0,
                                            headerImg: ''.concat(
                                                n.sE,
                                                '/static/images/task/card_bg.png',
                                            ),
                                        };
                                    case l.$J.ACTIVE:
                                        return {
                                            background:
                                                'linear-gradient(to bottom, #FF5520, #FF871C)',
                                            headerImg: ''.concat(
                                                n.sE,
                                                '/static/images/task/newStation_card_bg.png',
                                            ),
                                        };
                                    default:
                                        return {};
                                }
                            },
                        },
                        methods: {
                            touchMoreEvent: function () {
                                this.$emit('more', this.assemblyInfo);
                            },
                            reloadEvent: function (t) {
                                this.$emit('assemblyReloadEvent', {
                                    moduleInfo: t,
                                    assemblyInfo: this.assemblyInfo,
                                });
                            },
                            moduleBtnEvent: function (t) {
                                this.$emit('moduleBtnEvent', {
                                    moduleInfo: t,
                                    assemblyInfo: this.assemblyInfo,
                                });
                            },
                            taskBtnEvent: function (t) {
                                this.$emit(
                                    'taskBtnEvent',
                                    (0, o.A)(
                                        (0, o.A)({}, t),
                                        {},
                                        { assemblyInfo: this.assemblyInfo },
                                    ),
                                );
                            },
                            moduleSignEvent: function (t) {
                                this.$emit(
                                    'moduleSignEvent',
                                    (0, o.A)(
                                        (0, o.A)({}, t),
                                        {},
                                        { assemblyInfo: this.assemblyInfo },
                                    ),
                                );
                            },
                            moduleAwardEvent: function (t) {
                                this.$emit('moduleAwardEvent', {
                                    moduleInfo: t,
                                    assemblyInfo: this.assemblyInfo,
                                });
                            },
                            taskShowEvent: function (t) {
                                this.$emit(
                                    'taskShowEvent',
                                    (0, o.A)(
                                        (0, o.A)({}, t),
                                        {},
                                        { assemblyInfo: this.assemblyInfo },
                                    ),
                                );
                            },
                        },
                    }),
                    (m =
                        (i(8270),
                        (0, s.A)(
                            p,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return t.assemblyInfo
                                    ? i(
                                          'AssemblyLayout',
                                          {
                                              attrs: {
                                                  assemblyInfo: t.assemblyInfo,
                                                  hideHeader: t.hideHeader,
                                                  assemblyStyle: t.assemblyStyle,
                                                  assemblyStyleInfo: t.assemblyStyleInfo,
                                              },
                                              on: {
                                                  more: function (e) {
                                                      (arguments[0] = e = t.$handleEvent(e)),
                                                          t.touchMoreEvent.apply(void 0, arguments);
                                                  },
                                              },
                                          },
                                          t._l(t.moduleQueryVoList, function (e, n) {
                                              return i(
                                                  'v-uni-view',
                                                  { key: n, staticClass: 'assembly-item' },
                                                  [
                                                      t.assemblyStyleInfo.showDivider && 0 !== n
                                                          ? i(
                                                                'v-uni-view',
                                                                { staticClass: 'divider' },
                                                                [
                                                                    i('v-uni-view', {
                                                                        staticClass: 'divider-line',
                                                                    }),
                                                                ],
                                                                1,
                                                            )
                                                          : t._e(),
                                                      i(
                                                          'v-uni-view',
                                                          { class: ['module-item-wrap'] },
                                                          [
                                                              i('ModuleItem', {
                                                                  attrs: {
                                                                      moduleInfo: e,
                                                                      assemblyClaimInfo:
                                                                          t.assemblyClaimInfo,
                                                                      isPreview: t.isPreview,
                                                                  },
                                                                  on: {
                                                                      moduleBtnEvent: function (e) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.moduleBtnEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                      taskBtnEvent: function (e) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.taskBtnEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                      moduleSignEvent: function (
                                                                          e,
                                                                      ) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.moduleSignEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                      moduleAwardEvent: function (
                                                                          e,
                                                                      ) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.moduleAwardEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                      taskShowEvent: function (e) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.taskShowEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                      reloadEvent: function (e) {
                                                                          (arguments[0] = e =
                                                                              t.$handleEvent(e)),
                                                                              t.reloadEvent.apply(
                                                                                  void 0,
                                                                                  arguments,
                                                                              );
                                                                      },
                                                                  },
                                                              }),
                                                          ],
                                                          1,
                                                      ),
                                                  ],
                                                  1,
                                              );
                                          }),
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '1ccb6780',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    (g = {
                        props: {
                            assemblyList: { type: Array, require: !0 },
                            queryParams: {
                                type: Object,
                                default: function () {
                                    return {};
                                },
                            },
                            assemblySourceType: { type: String },
                            isPreview: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                            hideHeader: {
                                type: Boolean,
                                default: function () {
                                    return !1;
                                },
                            },
                        },
                        components: { AssemblyItem: m },
                        data: function () {
                            return { curIndex: 0 };
                        },
                        computed: {
                            curAssemblyInfo: function () {
                                return this.assemblyList[this.curIndex];
                            },
                        },
                        methods: {
                            preEvent: function () {
                                var t = this.curIndex - 1;
                                t < 0 && (t = this.assemblyList.length - 1),
                                    this.curIndex != t && (this.curIndex = t);
                            },
                            afterEvent: function () {
                                var t = this.curIndex + 1;
                                t >= this.assemblyList.length && (t = 0),
                                    this.curIndex != t && (this.curIndex = t);
                            },
                            assemblyReloadEvent: function (t) {
                                this.$emit('assemblyReloadEvent', t);
                            },
                            more: function (t) {
                                this.$emit('more', t);
                            },
                            moduleBtnEvent: function (t) {
                                this.$emit('moduleBtnEvent', t);
                            },
                            moduleSignEvent: function (t) {
                                this.$emit('moduleSignEvent', t);
                            },
                            moduleAwardEvent: function (t) {
                                this.$emit('moduleAwardEvent', t);
                            },
                            taskBtnEvent: function (t) {
                                this.$emit('taskBtnEvent', t);
                            },
                            taskShowEvent: function (t) {
                                this.$emit('taskShowEvent', t);
                            },
                        },
                    }),
                    (u =
                        (i(40269),
                        (0, s.A)(
                            g,
                            function () {
                                var t = this,
                                    e = t.$createElement,
                                    i = t._self._c || e;
                                return 0 < t.assemblyList.length
                                    ? i(
                                          'v-uni-view',
                                          { staticClass: 'assembly-list' },
                                          [
                                              i('AssemblyItem', {
                                                  attrs: {
                                                      isPreview: t.isPreview,
                                                      hideHeader: t.hideHeader,
                                                      assemblySourceType: t.assemblySourceType,
                                                      queryParams: t.queryParams,
                                                      assemblyInfo: t.curAssemblyInfo,
                                                  },
                                                  on: {
                                                      assemblyReloadEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.assemblyReloadEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                      more: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.more.apply(void 0, arguments);
                                                      },
                                                      moduleBtnEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.moduleBtnEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                      moduleSignEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.moduleSignEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                      moduleAwardEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.moduleAwardEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                      taskBtnEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.taskBtnEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                      taskShowEvent: function (e) {
                                                          (arguments[0] = e = t.$handleEvent(e)),
                                                              t.taskShowEvent.apply(
                                                                  void 0,
                                                                  arguments,
                                                              );
                                                      },
                                                  },
                                              }),
                                              1 < t.assemblyList.length
                                                  ? i(
                                                        'v-uni-view',
                                                        { staticClass: 'list-dot mg-t-10' },
                                                        [
                                                            i(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass: 'step-btn pre-btn',
                                                                    on: {
                                                                        click: function (e) {
                                                                            e.stopPropagation(),
                                                                                (arguments[0] = e =
                                                                                    t.$handleEvent(
                                                                                        e,
                                                                                    )),
                                                                                t.preEvent.apply(
                                                                                    void 0,
                                                                                    arguments,
                                                                                );
                                                                        },
                                                                    },
                                                                },
                                                                [
                                                                    i('v-uni-text', {
                                                                        staticClass:
                                                                            'iconfont iconjiantou',
                                                                    }),
                                                                ],
                                                                1,
                                                            ),
                                                            t._l(t.assemblyList, function (e, n) {
                                                                return i('v-uni-view', {
                                                                    key: n,
                                                                    class: [
                                                                        'dot-item',
                                                                        { active: n == t.curIndex },
                                                                    ],
                                                                });
                                                            }),
                                                            i(
                                                                'v-uni-view',
                                                                {
                                                                    staticClass:
                                                                        'step-btn after-btn',
                                                                    on: {
                                                                        click: function (e) {
                                                                            e.stopPropagation(),
                                                                                (arguments[0] = e =
                                                                                    t.$handleEvent(
                                                                                        e,
                                                                                    )),
                                                                                t.afterEvent.apply(
                                                                                    void 0,
                                                                                    arguments,
                                                                                );
                                                                        },
                                                                    },
                                                                },
                                                                [
                                                                    i('v-uni-text', {
                                                                        staticClass:
                                                                            'iconfont iconjiantou',
                                                                    }),
                                                                ],
                                                                1,
                                                            ),
                                                        ],
                                                        2,
                                                    )
                                                  : t._e(),
                                          ],
                                          1,
                                      )
                                    : t._e();
                            },
                            [],
                            !1,
                            null,
                            '7b997bf8',
                            null,
                            !1,
                            void 0,
                            void 0,
                        ).exports)),
                    i(48980),
                    i(90906),
                    i(47764),
                    i(37550),
                    i(62953),
                    i(98846)),
                T = i(53540),
                E =
                    ((C = {
                        components: { AssemblyList: u, AssemblyItem: m },
                        mixins: [
                            {
                                data: function () {
                                    return {
                                        assemblySourceType: null,
                                        showSignWindow: !1,
                                        signResultInfo: null,
                                        assemblyList: [],
                                        actProgressStatus: '',
                                    };
                                },
                                onShow: function () {
                                    this.pageHide &&
                                        this.assemblySourceType &&
                                        this.initAssemblyEvent(this.assemblySourceType);
                                },
                                computed: (0, o.A)(
                                    {},
                                    (0, y.mapGetters)('common', ['getLastLocation']),
                                ),
                                methods: (0, o.A)(
                                    (0, o.A)(
                                        (0, o.A)(
                                            {},
                                            (0, y.mapActions)('common', [
                                                'getLocation',
                                                'updateAwardPrizeWindow',
                                            ]),
                                        ),
                                        (0, y.mapActions)('active', ['updateAwardPrizeInfo']),
                                    ),
                                    {},
                                    {
                                        assemblyReloadEvent: function () {
                                            this.assemblySourceType &&
                                                this.initAssemblyEvent(this.assemblySourceType);
                                        },
                                        initAssemblyEvent: function (t) {
                                            var e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var n, s, r, l, c;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        if (
                                                                            ((i.prev = 0),
                                                                            (n = e.getLastLocation)
                                                                                .city)
                                                                        ) {
                                                                            i.next = 6;
                                                                            break;
                                                                        }
                                                                        return (
                                                                            (i.next = 5),
                                                                            e.getLocation({
                                                                                city: !0,
                                                                                refresh: !0,
                                                                            })
                                                                        );
                                                                    case 5:
                                                                        n = i.sent;
                                                                    case 6:
                                                                        return (
                                                                            (e.assemblySourceType =
                                                                                t),
                                                                            (s = {
                                                                                standType: t,
                                                                                city: n.city,
                                                                                lat: n.latitude,
                                                                                lon: n.longitude,
                                                                                stationId:
                                                                                    e.stationId,
                                                                                operId: e.operId,
                                                                            }),
                                                                            (i.next = 10),
                                                                            (0, x.u_)(s)
                                                                        );
                                                                    case 10:
                                                                        if (
                                                                            ((s = i.sent),
                                                                            !(
                                                                                (r =
                                                                                    s.data) instanceof
                                                                                Array
                                                                            ))
                                                                        ) {
                                                                            i.next = 21;
                                                                            break;
                                                                        }
                                                                        (l = (0, v.A)().mark(
                                                                            function t() {
                                                                                var i, n, s;
                                                                                return (0,
                                                                                v.A)().wrap(
                                                                                    function (t) {
                                                                                        for (;;)
                                                                                            switch (
                                                                                                (t.prev =
                                                                                                    t.next)
                                                                                            ) {
                                                                                                case 0:
                                                                                                    (i =
                                                                                                        r[
                                                                                                            c
                                                                                                        ]),
                                                                                                        e.assemblyList.some(
                                                                                                            function (
                                                                                                                t,
                                                                                                            ) {
                                                                                                                return (
                                                                                                                    t.assemblyId ===
                                                                                                                    i.assemblyId
                                                                                                                );
                                                                                                            },
                                                                                                        ) ||
                                                                                                            (0,
                                                                                                            a.uk)(
                                                                                                                'PutExpo',
                                                                                                                (0,
                                                                                                                o.A)(
                                                                                                                    (0,
                                                                                                                    o.A)(
                                                                                                                        {},
                                                                                                                        i.puttingVo,
                                                                                                                    ),
                                                                                                                    {},
                                                                                                                    {
                                                                                                                        putId:
                                                                                                                            null ==
                                                                                                                                i ||
                                                                                                                            null ==
                                                                                                                                (n =
                                                                                                                                    i.puttingVo)
                                                                                                                                ? void 0
                                                                                                                                : n.id,
                                                                                                                        putName:
                                                                                                                            null ==
                                                                                                                                i ||
                                                                                                                            null ==
                                                                                                                                (n =
                                                                                                                                    i.puttingVo)
                                                                                                                                ? void 0
                                                                                                                                : n.puttingName,
                                                                                                                        standType:
                                                                                                                            '组件投放',
                                                                                                                        assemblyId:
                                                                                                                            null ==
                                                                                                                            i
                                                                                                                                ? void 0
                                                                                                                                : i.assemblyId,
                                                                                                                        assemblymoduleId:
                                                                                                                            null ==
                                                                                                                                i ||
                                                                                                                            null ==
                                                                                                                                (s =
                                                                                                                                    i.moduleQueryVoList)
                                                                                                                                ? void 0
                                                                                                                                : s
                                                                                                                                      .map(
                                                                                                                                          function (
                                                                                                                                              t,
                                                                                                                                          ) {
                                                                                                                                              return t.moduleId;
                                                                                                                                          },
                                                                                                                                      )
                                                                                                                                      .join(
                                                                                                                                          ',',
                                                                                                                                      ),
                                                                                                                        sort:
                                                                                                                            c +
                                                                                                                            1,
                                                                                                                    },
                                                                                                                ),
                                                                                                            );
                                                                                                case 3:
                                                                                                case 'end':
                                                                                                    return t.stop();
                                                                                            }
                                                                                    },
                                                                                    t,
                                                                                );
                                                                            },
                                                                        )),
                                                                            (c = 0);
                                                                    case 15:
                                                                        if (c < r.length)
                                                                            return i.delegateYield(
                                                                                l(),
                                                                                't0',
                                                                                17,
                                                                            );
                                                                        i.next = 20;
                                                                        break;
                                                                    case 17:
                                                                        c++, (i.next = 15);
                                                                        break;
                                                                    case 20:
                                                                        e.assemblyList = r;
                                                                    case 21:
                                                                        return i.abrupt(
                                                                            'return',
                                                                            r,
                                                                        );
                                                                    case 24:
                                                                        return (
                                                                            (i.prev = 24),
                                                                            (i.t1 = i.catch(0)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t1,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 27:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[0, 24]],
                                                    );
                                                }),
                                            )();
                                        },
                                        taskShowEvent: function (t) {
                                            var e = t.taskInfo,
                                                i = t.moduleInfo;
                                            t = t.assemblyInfo;
                                            (0, a.uk)('TaskShow', {
                                                taskId: e ? e.taskId : i.packageId,
                                                taskType: e ? '任务' : '任务包',
                                                showPage: (0, a._B)(),
                                                showType: '组件展示',
                                                assemblyId: t.assemblyId,
                                                assemblymoduleId: i.moduleId,
                                            });
                                        },
                                        touchAssemblyMoreEvent: function (t) {
                                            t.morePath && this.jumpAssemblyConfigPath(t.morePath);
                                        },
                                        moduleBtnEvent: function (t) {
                                            var e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var n, s, r, c, u, d, f, m;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        if (
                                                                            ((i.prev = 0),
                                                                            (u = t.moduleInfo),
                                                                            (s = t.assemblyInfo),
                                                                            (r = (u =
                                                                                void 0 === u
                                                                                    ? {}
                                                                                    : u)
                                                                                .mainModuleConfQueryVo),
                                                                            (r =
                                                                                void 0 === r
                                                                                    ? {}
                                                                                    : r),
                                                                            (f = u.relaActId),
                                                                            (c = u.moduleId),
                                                                            (u = u.packageId),
                                                                            (d =
                                                                                r.currModuleBtnConfQueryVo),
                                                                            (f = {
                                                                                assemblyId:
                                                                                    s.assemblyId,
                                                                                moduleId: c,
                                                                                relaActId: f,
                                                                            }),
                                                                            d.status ===
                                                                                l.Rr.UNRECEIVE)
                                                                        )
                                                                            return (
                                                                                (i.next = 9),
                                                                                e.submitAssemblyTask(
                                                                                    f,
                                                                                )
                                                                            );
                                                                        i.next = 12;
                                                                        break;
                                                                    case 9:
                                                                        (0, a.uk)('TaskClick', {
                                                                            taskId: u,
                                                                            taskType: '任务包',
                                                                            showPage: (0, a._B)(),
                                                                            showType: '组件展示',
                                                                            assemblyId:
                                                                                s.assemblyId,
                                                                            assemblymoduleId: c,
                                                                            buttonName: '接受',
                                                                        }),
                                                                            (i.next = 17);
                                                                        break;
                                                                    case 12:
                                                                        if (
                                                                            d.status ===
                                                                            l.Rr.COMPLETE
                                                                        )
                                                                            return (
                                                                                (m = {}),
                                                                                r.prizeImagePath &&
                                                                                    r.title &&
                                                                                    (m = {
                                                                                        prizeImagePath:
                                                                                            r.prizeImagePath ||
                                                                                            r.prizeImage,
                                                                                        prizeName:
                                                                                            r.prizeName ||
                                                                                            r.title,
                                                                                    }),
                                                                                (i.next = 17),
                                                                                e.submitAssemblyClaim(
                                                                                    f,
                                                                                    (0, a.LK)(m)
                                                                                        ? void 0
                                                                                        : m,
                                                                                )
                                                                            );
                                                                        i.next = 17;
                                                                        break;
                                                                    case 17:
                                                                        return (
                                                                            (m =
                                                                                e.assemblyList.findIndex(
                                                                                    function (t) {
                                                                                        return (
                                                                                            t.assemblyId ===
                                                                                            s.assemblyId
                                                                                        );
                                                                                    },
                                                                                )),
                                                                            (0, a.uk)(
                                                                                'PutClick',
                                                                                (0, o.A)(
                                                                                    (0, o.A)(
                                                                                        {},
                                                                                        s.puttingVo,
                                                                                    ),
                                                                                    {},
                                                                                    {
                                                                                        putId:
                                                                                            null ==
                                                                                                s ||
                                                                                            null ==
                                                                                                (n =
                                                                                                    s.puttingVo)
                                                                                                ? void 0
                                                                                                : n.id,
                                                                                        putName:
                                                                                            null ==
                                                                                                s ||
                                                                                            null ==
                                                                                                (n =
                                                                                                    s.puttingVo)
                                                                                                ? void 0
                                                                                                : n.puttingName,
                                                                                        standType:
                                                                                            '组件投放',
                                                                                        assemblyId:
                                                                                            null ==
                                                                                            s
                                                                                                ? void 0
                                                                                                : s.assemblyId,
                                                                                        assemblymoduleId:
                                                                                            c,
                                                                                        sort:
                                                                                            -1 < m
                                                                                                ? m +
                                                                                                  1
                                                                                                : void 0,
                                                                                    },
                                                                                ),
                                                                            ),
                                                                            (i.next = 21),
                                                                            e.initAssemblyEvent(
                                                                                e.assemblySourceType,
                                                                            )
                                                                        );
                                                                    case 21:
                                                                        return i.abrupt('return');
                                                                    case 24:
                                                                        return (
                                                                            (i.prev = 24),
                                                                            (i.t0 = i.catch(0)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t0,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 27:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[0, 24]],
                                                    );
                                                }),
                                            )();
                                        },
                                        taskBtnEvent: function (t) {
                                            var e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var o, s, r, c, u, d, f, m, p;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        (i.prev = 0),
                                                                            (o = t.taskInfo),
                                                                            (o =
                                                                                void 0 === o
                                                                                    ? {}
                                                                                    : o),
                                                                            (s =
                                                                                void 0 ===
                                                                                (s = t.moduleInfo)
                                                                                    ? {}
                                                                                    : s),
                                                                            (r = t.assemblyInfo),
                                                                            (c = s.moduleActInfo),
                                                                            (u =
                                                                                s.mainModuleConfQueryVo),
                                                                            (d =
                                                                                o.currTaskConfQueryVo),
                                                                            (p = (d =
                                                                                void 0 === d
                                                                                    ? {}
                                                                                    : d)
                                                                                .currTaskBtnConf),
                                                                            (f = (p =
                                                                                void 0 === p
                                                                                    ? {}
                                                                                    : p).status),
                                                                            (m = p.btnPath),
                                                                            (0, a.uk)('TaskClick', {
                                                                                taskId: o.taskId,
                                                                                taskType: '任务',
                                                                                showPage: (0,
                                                                                a._B)(),
                                                                                showType:
                                                                                    '组件展示',
                                                                                assemblyId:
                                                                                    r.assemblyId,
                                                                                assemblymoduleId:
                                                                                    s.moduleId,
                                                                                buttonName:
                                                                                    p && p.btnDesc,
                                                                            }),
                                                                            f === l.Rr.RECEIVE && m
                                                                                ? (s.relaActId &&
                                                                                  -1 !==
                                                                                      m.indexOf(
                                                                                          'pinganCoupon',
                                                                                      )
                                                                                      ? ((p =
                                                                                            encodeURIComponent(
                                                                                                u.prizeImagePath,
                                                                                            )),
                                                                                        e.jumpAssemblyConfigPath(
                                                                                            ''
                                                                                                .concat(
                                                                                                    m,
                                                                                                    '?actNo=',
                                                                                                )
                                                                                                .concat(
                                                                                                    s.relaActId,
                                                                                                    '&prizeImagePath=',
                                                                                                )
                                                                                                .concat(
                                                                                                    p,
                                                                                                    '&prizeName=',
                                                                                                )
                                                                                                .concat(
                                                                                                    u.title,
                                                                                                ),
                                                                                        ))
                                                                                      : e.jumpAssemblyConfigPath(
                                                                                            m,
                                                                                        ),
                                                                                  (i.next = 19))
                                                                                : (i.next = 12);
                                                                        break;
                                                                    case 12:
                                                                        if (f !== l.Rr.COMPLETE);
                                                                        else if (
                                                                            c &&
                                                                            c.rewardType ===
                                                                                l.CC.SINGLE
                                                                        )
                                                                            return (
                                                                                (p = {
                                                                                    assemblyId:
                                                                                        r.assemblyId,
                                                                                    moduleId:
                                                                                        s.moduleId,
                                                                                    relaActId:
                                                                                        s.relaActId,
                                                                                    relaActType:
                                                                                        s.relaActType,
                                                                                    taskId: o.taskId,
                                                                                }),
                                                                                (i.next = 17),
                                                                                e.submitAssemblyClaim(
                                                                                    p,
                                                                                    {
                                                                                        prizeImagePath:
                                                                                            d.prizeImagePath ||
                                                                                            d.prizeImage ||
                                                                                            ''.concat(
                                                                                                n.sE,
                                                                                                '/static/images/task/task_prize_icon.png',
                                                                                            ),
                                                                                        prizeName:
                                                                                            d.prizeName,
                                                                                        prizeRemark:
                                                                                            u.prizeRemark,
                                                                                    },
                                                                                )
                                                                            );
                                                                        i.next = 19;
                                                                        break;
                                                                    case 17:
                                                                        return (
                                                                            (i.next = 19),
                                                                            e.initAssemblyEvent(
                                                                                e.assemblySourceType,
                                                                            )
                                                                        );
                                                                    case 19:
                                                                        return i.abrupt('return');
                                                                    case 22:
                                                                        return (
                                                                            (i.prev = 22),
                                                                            (i.t0 = i.catch(0)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t0,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 25:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[0, 22]],
                                                    );
                                                }),
                                            )();
                                        },
                                        moduleSignEvent: (0, c.n)(
                                            (() => {
                                                var t = (0, b.A)(
                                                    (0, v.A)().mark(function t(e) {
                                                        var i, n, s, r;
                                                        return (0, v.A)().wrap(
                                                            function (t) {
                                                                for (;;)
                                                                    switch ((t.prev = t.next)) {
                                                                        case 0:
                                                                            return (
                                                                                (t.prev = 0),
                                                                                (n =
                                                                                    e.assemblyInfo),
                                                                                (s = e.moduleInfo),
                                                                                (r =
                                                                                    this.assemblyList.findIndex(
                                                                                        function (
                                                                                            t,
                                                                                        ) {
                                                                                            return (
                                                                                                t.assemblyId ===
                                                                                                n.assemblyId
                                                                                            );
                                                                                        },
                                                                                    )),
                                                                                (0, a.uk)(
                                                                                    'PutClick',
                                                                                    (0, o.A)(
                                                                                        (0, o.A)(
                                                                                            {},
                                                                                            n.puttingVo,
                                                                                        ),
                                                                                        {},
                                                                                        {
                                                                                            putId:
                                                                                                null ==
                                                                                                    n ||
                                                                                                null ==
                                                                                                    (i =
                                                                                                        n.puttingVo)
                                                                                                    ? void 0
                                                                                                    : i.id,
                                                                                            putName:
                                                                                                null ==
                                                                                                    n ||
                                                                                                null ==
                                                                                                    (i =
                                                                                                        n.puttingVo)
                                                                                                    ? void 0
                                                                                                    : i.puttingName,
                                                                                            standType:
                                                                                                '组件投放',
                                                                                            assemblyId:
                                                                                                null ==
                                                                                                n
                                                                                                    ? void 0
                                                                                                    : n.assemblyId,
                                                                                            assemblymoduleId:
                                                                                                null ==
                                                                                                s
                                                                                                    ? void 0
                                                                                                    : s.moduleId,
                                                                                            sort:
                                                                                                -1 <
                                                                                                r
                                                                                                    ? r +
                                                                                                      1
                                                                                                    : void 0,
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                                (t.next = 6),
                                                                                this.submitAssemblySign(
                                                                                    e,
                                                                                )
                                                                            );
                                                                        case 6:
                                                                            return (
                                                                                (t.next = 8),
                                                                                this.initAssemblyEvent(
                                                                                    this
                                                                                        .assemblySourceType,
                                                                                )
                                                                            );
                                                                        case 8:
                                                                            return t.abrupt(
                                                                                'return',
                                                                            );
                                                                        case 11:
                                                                            return (
                                                                                (t.prev = 11),
                                                                                (t.t0 = t.catch(0)),
                                                                                t.abrupt(
                                                                                    'return',
                                                                                    Promise.reject(
                                                                                        t.t0,
                                                                                    ),
                                                                                )
                                                                            );
                                                                        case 14:
                                                                        case 'end':
                                                                            return t.stop();
                                                                    }
                                                            },
                                                            t,
                                                            this,
                                                            [[0, 11]],
                                                        );
                                                    }),
                                                );
                                                return function (e) {
                                                    return t.apply(this, arguments);
                                                };
                                            })(),
                                            1e3,
                                        ),
                                        moduleAwardEvent: (0, c.n)(
                                            (() => {
                                                var t = (0, b.A)(
                                                    (0, v.A)().mark(function t(e) {
                                                        var i, n, s, r, l, c;
                                                        return (0, v.A)().wrap(
                                                            function (t) {
                                                                for (;;)
                                                                    switch ((t.prev = t.next)) {
                                                                        case 0:
                                                                            return (
                                                                                (t.prev = 0),
                                                                                (n = e.moduleInfo),
                                                                                (s =
                                                                                    e.assemblyInfo),
                                                                                (c = (n =
                                                                                    void 0 === n
                                                                                        ? {}
                                                                                        : n)
                                                                                    .relaActId),
                                                                                (l = n.moduleId),
                                                                                (r =
                                                                                    n.mainModuleConfQueryVo),
                                                                                (l = {
                                                                                    assemblyId:
                                                                                        s.assemblyId,
                                                                                    moduleId: l,
                                                                                    relaActId: c,
                                                                                }),
                                                                                (c =
                                                                                    this.assemblyList.findIndex(
                                                                                        function (
                                                                                            t,
                                                                                        ) {
                                                                                            return (
                                                                                                t.assemblyId ===
                                                                                                s.assemblyId
                                                                                            );
                                                                                        },
                                                                                    )),
                                                                                (0, a.uk)(
                                                                                    'PutClick',
                                                                                    (0, o.A)(
                                                                                        (0, o.A)(
                                                                                            {},
                                                                                            s.puttingVo,
                                                                                        ),
                                                                                        {},
                                                                                        {
                                                                                            putId:
                                                                                                null ==
                                                                                                    s ||
                                                                                                null ==
                                                                                                    (i =
                                                                                                        s.puttingVo)
                                                                                                    ? void 0
                                                                                                    : i.id,
                                                                                            putName:
                                                                                                null ==
                                                                                                    s ||
                                                                                                null ==
                                                                                                    (i =
                                                                                                        s.puttingVo)
                                                                                                    ? void 0
                                                                                                    : i.puttingName,
                                                                                            standType:
                                                                                                '组件投放',
                                                                                            assemblyId:
                                                                                                null ==
                                                                                                s
                                                                                                    ? void 0
                                                                                                    : s.assemblyId,
                                                                                            assemblymoduleId:
                                                                                                null ==
                                                                                                n
                                                                                                    ? void 0
                                                                                                    : n.moduleId,
                                                                                            sort:
                                                                                                -1 <
                                                                                                c
                                                                                                    ? c +
                                                                                                      1
                                                                                                    : void 0,
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                                (t.next = 8),
                                                                                this.submitAssemblyClaim(
                                                                                    l,
                                                                                    {
                                                                                        prizeImagePath:
                                                                                            r.prizeImagePath,
                                                                                        prizeName:
                                                                                            r.title,
                                                                                        prizeRemark:
                                                                                            r.prizeRemark,
                                                                                    },
                                                                                )
                                                                            );
                                                                        case 8:
                                                                            return (
                                                                                (t.next = 10),
                                                                                this.initAssemblyEvent(
                                                                                    this
                                                                                        .assemblySourceType,
                                                                                )
                                                                            );
                                                                        case 10:
                                                                            return t.abrupt(
                                                                                'return',
                                                                            );
                                                                        case 13:
                                                                            return (
                                                                                (t.prev = 13),
                                                                                (t.t0 = t.catch(0)),
                                                                                t.abrupt(
                                                                                    'return',
                                                                                    Promise.reject(
                                                                                        t.t0,
                                                                                    ),
                                                                                )
                                                                            );
                                                                        case 16:
                                                                        case 'end':
                                                                            return t.stop();
                                                                    }
                                                            },
                                                            t,
                                                            this,
                                                            [[0, 13]],
                                                        );
                                                    }),
                                                );
                                                return function (e) {
                                                    return t.apply(this, arguments);
                                                };
                                            })(),
                                            1e3,
                                        ),
                                        submitAssemblyTask: function () {
                                            var t = arguments,
                                                e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var n, a, s;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        if (
                                                                            ((n =
                                                                                0 < t.length &&
                                                                                void 0 !== t[0]
                                                                                    ? t[0]
                                                                                    : {}),
                                                                            (i.prev = 1),
                                                                            (a = e.getLastLocation)
                                                                                .city)
                                                                        ) {
                                                                            i.next = 7;
                                                                            break;
                                                                        }
                                                                        return (
                                                                            (i.next = 6),
                                                                            e.getLocation({
                                                                                city: !0,
                                                                                refresh: !0,
                                                                            })
                                                                        );
                                                                    case 6:
                                                                        a = i.sent;
                                                                    case 7:
                                                                        return (
                                                                            (s = (0, o.A)(
                                                                                (0, o.A)({}, n),
                                                                                {},
                                                                                { city: a.city },
                                                                            )),
                                                                            (i.next = 10),
                                                                            (0, x.wj)(s)
                                                                        );
                                                                    case 10:
                                                                        return i.abrupt('return');
                                                                    case 13:
                                                                        return (
                                                                            (i.prev = 13),
                                                                            (i.t0 = i.catch(1)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t0,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 16:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[1, 13]],
                                                    );
                                                }),
                                            )();
                                        },
                                        submitAssemblyClaim: function () {
                                            var t = arguments,
                                                e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var n, a, s, r;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        if (
                                                                            ((n =
                                                                                0 < t.length &&
                                                                                void 0 !== t[0]
                                                                                    ? t[0]
                                                                                    : {}),
                                                                            (a =
                                                                                1 < t.length
                                                                                    ? t[1]
                                                                                    : void 0),
                                                                            (i.prev = 2),
                                                                            (s = e.getLastLocation)
                                                                                .city)
                                                                        ) {
                                                                            i.next = 8;
                                                                            break;
                                                                        }
                                                                        return (
                                                                            (i.next = 7),
                                                                            e.getLocation({
                                                                                city: !0,
                                                                                refresh: !0,
                                                                            })
                                                                        );
                                                                    case 7:
                                                                        s = i.sent;
                                                                    case 8:
                                                                        return (
                                                                            (r = (0, o.A)(
                                                                                (0, o.A)({}, n),
                                                                                {},
                                                                                { city: s.city },
                                                                            )),
                                                                            (i.next = 11),
                                                                            (0, x.rD)(r)
                                                                        );
                                                                    case 11:
                                                                        return (
                                                                            a &&
                                                                                a.prizeName &&
                                                                                ((e.actProgressStatus =
                                                                                    l.Rr.CLAIM),
                                                                                e.updateAwardPrizeWindow(
                                                                                    (0, o.A)(
                                                                                        {
                                                                                            isShow: !0,
                                                                                        },
                                                                                        a,
                                                                                    ),
                                                                                )),
                                                                            i.abrupt('return')
                                                                        );
                                                                    case 16:
                                                                        return (
                                                                            (i.prev = 16),
                                                                            (i.t0 = i.catch(2)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t0,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 19:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[2, 16]],
                                                    );
                                                }),
                                            )();
                                        },
                                        submitAssemblySign: function () {
                                            var t = arguments,
                                                e = this;
                                            return (0, b.A)(
                                                (0, v.A)().mark(function i() {
                                                    var n, o, s;
                                                    return (0, v.A)().wrap(
                                                        function (i) {
                                                            for (;;)
                                                                switch ((i.prev = i.next)) {
                                                                    case 0:
                                                                        return (
                                                                            (n =
                                                                                0 < t.length &&
                                                                                void 0 !== t[0]
                                                                                    ? t[0]
                                                                                    : {}),
                                                                            (i.prev = 1),
                                                                            (o = n.signReceive),
                                                                            (n = n.assemblyInfo),
                                                                            (o = {
                                                                                actId: o.signActId,
                                                                                linkActId: o.actId,
                                                                                buyNo: o.buyNo,
                                                                            }),
                                                                            (i.next = 6),
                                                                            (0, T.cW)(o)
                                                                        );
                                                                    case 6:
                                                                        return (
                                                                            (o = i.sent),
                                                                            (s = o.data),
                                                                            (e.signResultInfo = s),
                                                                            e.signResultInfo
                                                                                .success &&
                                                                                e.openSignView(),
                                                                            (0, a.uk)('SignSuc', {
                                                                                signPage: (0,
                                                                                a._B)(),
                                                                                assemblyId:
                                                                                    n.assemblyId,
                                                                                assemblymoduleId:
                                                                                    '',
                                                                            }),
                                                                            i.abrupt('return')
                                                                        );
                                                                    case 15:
                                                                        return (
                                                                            (i.prev = 15),
                                                                            (i.t0 = i.catch(1)),
                                                                            i.abrupt(
                                                                                'return',
                                                                                Promise.reject(
                                                                                    i.t0,
                                                                                ),
                                                                            )
                                                                        );
                                                                    case 18:
                                                                    case 'end':
                                                                        return i.stop();
                                                                }
                                                        },
                                                        i,
                                                        null,
                                                        [[1, 15]],
                                                    );
                                                }),
                                            )();
                                        },
                                        openSignView: function () {
                                            this.showSignWindow = !0;
                                        },
                                        confirmSignWindow: function () {
                                            this.closeSignWindow(), (this.signResultInfo = null);
                                        },
                                        closeSignWindow: function () {
                                            this.showSignWindow = !1;
                                        },
                                        jumpAssemblyConfigPath: function (t) {
                                            /^\//.test(t) || (t = '/' + t), (0, a.ZK)(t);
                                        },
                                    },
                                ),
                            },
                        ],
                        data: function () {
                            return {};
                        },
                        onLoad: function (t) {
                            t = t.assemblyInfo;
                            if (t)
                                try {
                                    this.assemblyList.push(JSON.parse(t));
                                } catch (t) {}
                            else
                                'public' === n.Kn &&
                                    ((t = window.sessionStorage.getItem('xdt_assemblyInfo')),
                                    (0, a.g9)(t) || this.assemblyList.push(JSON.parse(t)));
                        },
                    }),
                    i(78805),
                    (0, s.A)(
                        C,
                        function () {
                            var t = this.$createElement;
                            t = this._self._c || t;
                            return t(
                                'v-uni-view',
                                { staticClass: 'task-show-page' },
                                [
                                    t('AssemblyList', {
                                        attrs: { isPreview: !0, assemblyList: this.assemblyList },
                                    }),
                                ],
                                1,
                            );
                        },
                        [],
                        !1,
                        null,
                        'ed3f8c54',
                        null,
                        !1,
                        void 0,
                        void 0,
                    ).exports);
        },
        12719: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.pie-space[data-v-32d7f2ce]{display:flex;align-items:center}.pie-bar[data-v-32d7f2ce]{display:flex;align-items:center}.pie-bar .pie-item[data-v-32d7f2ce]{margin-left:%?12?%;padding-right:%?10?%;display:flex;align-items:center;font-size:%?20?%;line-height:%?31?%;color:#7e7e7e;background-color:#f4f6f9;border-radius:%?7?%}.pie-bar .pie-item .pie-icon[data-v-32d7f2ce]{margin-right:%?5?%;display:flex;align-items:center;justify-content:center;width:%?31?%;height:%?31?%;border-radius:%?7?%;color:#fff;font-weight:700;font-size:%?22?%}.pie-bar .pie-item .pie-icon.ac[data-v-32d7f2ce]{background-color:#0c0e0c}.pie-bar .pie-item .pie-icon.dc[data-v-32d7f2ce]{background-color:#ff2700}.pie-bar .pie-item .pie-icon.super[data-v-32d7f2ce]{color:#fff;background-color:#9401d2}.pie-bar .pie-item .pie-type[data-v-32d7f2ce]{color:#0c0e0c}.pie-bar .pie-item .pie-num[data-v-32d7f2ce]{color:#0c0e0c;font-weight:700}',
                '',
            ]),
                (e.default = i);
        },
        13930: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.assembly-item[data-v-1ccb6780]:not(:first-child){margin-top:%?14?%}.assembly-item .newpeople-tips[data-v-1ccb6780]{position:relative;width:100%;z-index:0}.assembly-item .module-item-wrap[data-v-1ccb6780]{position:relative;width:100%;z-index:1;background:#fff;border-radius:%?16?%}.assembly-item .module-item-wrap.has-tips[data-v-1ccb6780]{margin-top:%?-60?%}.assembly-item .divider[data-v-1ccb6780]{width:100%}.assembly-item .divider .divider-line[data-v-1ccb6780]{width:100%;height:1px;background:#f7e6df}',
                '',
            ]),
                (e.default = i);
        },
        4256: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.assembly-layout .layout-bg[data-v-6a32fecc]{position:absolute;width:100%;z-index:0}.assembly-layout .layout-header[data-v-6a32fecc]{position:relative;display:flex;align-items:center;justify-content:space-between;width:100%;padding:%?20?% %?20?% %?15?%;z-index:1}.assembly-layout .layout-header .header-title[data-v-6a32fecc]{position:relative;color:#662e08;font-weight:700;font-size:%?28?%;z-index:1;max-width:%?500?%}.assembly-layout .layout-header .header-title .title[data-v-6a32fecc]{position:relative;overflow:hidden}.assembly-layout .layout-header .header-title .title .title-text[data-v-6a32fecc]{position:relative;z-index:1}.assembly-layout .layout-header .header-title .title .title-text.act[data-v-6a32fecc]{font-weight:700;font-size:%?32?%;color:#fff;line-height:%?38?%;font-style:italic}.assembly-layout .layout-header .header-more[data-v-6a32fecc]{color:#662e08;font-size:%?24?%;z-index:1}.assembly-layout .layout-header .header-more.act[data-v-6a32fecc]{font-weight:700;font-size:%?22?%;color:#fff;font-style:italic;margin-top:%?-15?%}.assembly-layout .layout-header .header-img[data-v-6a32fecc]{position:absolute;right:%?20?%;bottom:0;z-index:0;width:%?143?%;height:%?69?%}.assembly-layout .layout-main[data-v-6a32fecc]{position:relative;z-index:1;width:100%;padding:%?10?%;border-radius:%?30?%}',
                '',
            ]),
                (e.default = i);
        },
        91753: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.assembly-list[data-v-7b997bf8]{position:relative;overflow:hidden}.assembly-list .list-dot[data-v-7b997bf8]{padding:%?15?% 0;border-radius:%?20?%;background:#fff;display:flex;align-items:center;justify-content:center}.assembly-list .list-dot .dot-item[data-v-7b997bf8]{margin:0 %?8?%;width:%?14?%;height:%?14?%;border-radius:50%;background:#dddada}.assembly-list .list-dot .dot-item.active[data-v-7b997bf8]{background:#ff2700}.assembly-list .list-dot .step-btn[data-v-7b997bf8]{margin:0 %?16?%;display:flex;align-items:center;justify-content:center;width:%?50?%;height:%?50?%;border-radius:50%;border:1px solid #d9d9d9;background-color:#fff;font-size:%?32?%;color:#ff2700;z-index:2}.assembly-list .list-dot .step-btn.pre-btn[data-v-7b997bf8]{-webkit-transform:scale(-1);transform:scale(-1)}',
                '',
            ]),
                (e.default = i);
        },
        55455: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.task-info-item[data-v-aa485f4e]{position:relative;display:flex;width:100%;align-items:center}.task-info-item .task-logo[data-v-aa485f4e]{flex-shrink:0;width:%?88?%;height:%?88?%}.task-info-item .task-content[data-v-aa485f4e]{flex:1;overflow:hidden;margin:0 %?30?%}.task-info-item .task-content.small[data-v-aa485f4e]{max-width:55%}.task-info-item .task-content .content-title[data-v-aa485f4e]{width:100%;display:flex;align-items:center;color:#662e08;font-size:%?28?%;font-weight:700}.task-info-item .task-content .content-remark[data-v-aa485f4e]{font-size:%?24?%;line-height:1.2;color:#845636}.task-info-item .task-content .content-remark .date-tip[data-v-aa485f4e]{flex-shrink:0;padding:0 %?10?%;display:inline-flex;align-items:center;justify-content:center;height:%?30?%;margin-right:%?5?%;font-size:%?22?%;color:#fff;background:linear-gradient(90deg,#ff895e,#ff6334) #828282;border-radius:%?8?%}.task-info-item .task-content .content-remark .date-exp-tip[data-v-aa485f4e]{display:inline-flex;align-items:center;justify-content:center;padding:0 %?10?%;height:%?37?%;background:linear-gradient(-90deg,#fef7f4 9%,#ffe6de);border-radius:%?6?%;font-size:%?24?%;color:#fb440d}.task-info-item .task-content .content-remark .date-exp-tip .iconshichang[data-v-aa485f4e]{margin-right:%?5?%;font-size:%?28?%}.task-info-item .task-content .content-remark.light[data-v-aa485f4e]{color:#fe0000}.task-info-item .task-touch[data-v-aa485f4e]{flex-shrink:0}.task-info-item .task-touch .done-view[data-v-aa485f4e]{position:absolute;right:0;top:0;width:%?129?%;height:%?100?%}.task-info-item .task-touch .touch-btn[data-v-aa485f4e]{display:flex;align-items:center;justify-content:center;padding:0 %?10?%;min-width:%?140?%;height:%?56?%;background:linear-gradient(180deg,#ff895e 0,#ff6334);border-radius:%?16?%;color:#fff;font-size:%?24?%;font-weight:700}.task-info-item .task-touch .complete[data-v-aa485f4e]{background:#b6aaa5}',
                '',
            ]),
                (e.default = i);
        },
        40342: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.task-main[data-v-848c1080]{padding:%?10?% 0;width:100%;min-width:%?120?%;display:flex;flex-direction:column;align-items:center}.task-main .task-prize[data-v-848c1080]{position:relative;display:flex;justify-content:center;align-items:center;width:100%;height:%?46?%;text-align:center}.task-main .task-prize[data-v-848c1080]::before, .task-main .task-prize[data-v-848c1080]::after{position:absolute;display:block;top:50%;width:50%;content:"";height:%?12?%;background:#f3edea;z-index:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.task-main .task-prize[data-v-848c1080]::before{left:0}.task-main .task-prize[data-v-848c1080]::after{right:0}.task-main .task-prize.first[data-v-848c1080]::before{display:none}.task-main .task-prize.end[data-v-848c1080]::after{display:none}.task-main .task-prize .task-prize-img[data-v-848c1080]{position:relative;z-index:1;width:%?38?%;height:%?44?%}.task-main .task-prize .task-prize-img.complete[data-v-848c1080]{animation:shakeTask-data-v-848c1080 4s ease-in-out infinite reverse}.task-main .task-prize .task-prize-done[data-v-848c1080]{position:relative;z-index:1;color:#00c803;border-radius:50%;font-size:%?36?%}.task-main .task-prize-name[data-v-848c1080]{margin-top:%?15?%;font-size:%?24?%;color:#ff2700}.task-main .task-title[data-v-848c1080]{margin-top:%?15?%;font-size:%?24?%;color:#662e08}.task-main .task-interactive[data-v-848c1080]{display:flex;align-items:center;justify-content:center;margin:%?15?% auto 0;width:%?95?%;height:%?34?%;background:linear-gradient(180deg,#febb6b,#fd2216 99%),#fff;border-radius:%?17?%;color:#fff;font-size:%?24?%}@-webkit-keyframes shakeTask-data-v-848c1080{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}90%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}91%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}93%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}94%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}95%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}97%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}98%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}99%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}100%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}}@keyframes shakeTask-data-v-848c1080{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}90%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}91%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}93%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}94%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}95%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}97%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}98%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}99%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}100%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}}',
                '',
            ]),
                (e.default = i);
        },
        15713: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.charge-task-list[data-v-0dd4fed1]{display:flex;justify-content:space-between;flex-wrap:wrap;padding:%?30?% 0;background:#fff9f7;border-radius:%?20?%}.charge-task-list .charge-task-item[data-v-0dd4fed1]{flex:1;min-width:20%}',
                '',
            ]),
                (e.default = i);
        },
        18533: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.task-info-item[data-v-575bb83a]{position:relative;display:flex;width:100%;align-items:center;justify-content:space-between}.task-info-item .task-logo[data-v-575bb83a]{margin-right:%?30?%;flex-shrink:0;width:%?88?%;height:%?88?%}.task-info-item .task-content[data-v-575bb83a]{flex:1;flex-shrink:0;overflow:hidden}.task-info-item .task-content.small[data-v-575bb83a]{max-width:55%}.task-info-item .task-content .content-title[data-v-575bb83a]{width:100%;display:flex;align-items:center;color:#662e08;font-size:%?26?%;font-weight:700}.task-info-item .task-content .content-remark[data-v-575bb83a]{font-size:%?24?%;line-height:1.2;color:#845636}.task-info-item .task-content .content-remark .date-tip[data-v-575bb83a]{flex-shrink:0;padding:0 %?10?%;display:inline-flex;align-items:center;justify-content:center;height:%?30?%;margin-right:%?5?%;font-size:%?22?%;color:#fff;background:linear-gradient(90deg,#ff895e,#ff6334) #828282;border-radius:%?8?%}.task-info-item .task-content .content-remark .date-exp-tip[data-v-575bb83a]{display:inline-flex;align-items:center;justify-content:center;padding:0 %?10?%;height:%?37?%;background:linear-gradient(-90deg,#fef7f4 9%,#ffe6de);border-radius:%?6?%;font-size:%?24?%;color:#ff2700}.task-info-item .task-content .content-remark .date-exp-tip .iconshichang[data-v-575bb83a]{margin-right:%?5?%;font-size:%?28?%}.task-info-item .task-content .content-remark.light[data-v-575bb83a]{color:#fe0000}.task-info-item .task-touch[data-v-575bb83a]{margin-left:%?30?%;flex-shrink:0}.task-info-item .task-touch .done-view[data-v-575bb83a]{position:absolute;right:0;top:0;width:%?129?%;height:%?100?%}.task-info-item .task-touch .progress-remark[data-v-575bb83a]{color:#ff2700;font-size:%?24?%;white-space:nowrap}.task-info-item .task-touch .touch-btn[data-v-575bb83a]{display:flex;align-items:center;justify-content:center;padding:0 %?10?%;min-width:%?140?%;height:%?56?%;background:linear-gradient(90deg,#ff895e 0,#ff6334);border-radius:%?16?%;color:#fff;font-size:%?24?%;font-weight:700}',
                '',
            ]),
                (e.default = i);
        },
        67795: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.task-item[data-v-572d4ea1]{padding:%?25?%}.task-item.full[data-v-572d4ea1]{padding:0}.task-item .task-step-list[data-v-572d4ea1]{position:relative;width:100%}.task-item .task-step-list .step-point[data-v-572d4ea1]{position:absolute;top:0;left:%?24?%;-webkit-transform:translateY(-65%);transform:translateY(-65%);font-size:%?40?%;color:#fff}.task-item .task-step-list .task-step-item[data-v-572d4ea1]{padding:0 0 0 %?24?%;background-color:#fff9f7;border-radius:%?20?%}.task-item .task-step-list .task-step-item[data-v-572d4ea1]:not(:first-child){margin-top:%?15?%}.task-item .task-step-list .toggle-bar[data-v-572d4ea1]{text-align:center;color:#b7bac2;font-size:%?24?%;padding:%?20?% 0}.task-item .task-step-list .toggle-bar .iconfont[data-v-572d4ea1]{margin:0 %?10?%}.task-item .task-step-list .toggle-bar .iconfont.toggle[data-v-572d4ea1]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.task-item .sign-preview[data-v-572d4ea1],\n.task-item .station-preview[data-v-572d4ea1]{width:100%}.task-item .sign-list-space[data-v-572d4ea1]{padding:0 %?20?%;background-color:#fff;border-radius:%?20?%}',
                '',
            ]),
                (e.default = i);
        },
        91441: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.signin-info-item[data-v-7bf82291]{display:flex;width:100%;align-items:center}.signin-info-item .signin-logo[data-v-7bf82291]{flex-shrink:0;width:%?88?%;height:%?88?%}.signin-info-item .signin-content[data-v-7bf82291]{flex:1;margin:0 %?30?%}.signin-info-item .signin-content .content-title[data-v-7bf82291]{display:flex;justify-content:space-between;color:#662e08;font-size:%?28?%;line-height:1.2;font-weight:700}.signin-info-item .signin-content .content-title .signin-result[data-v-7bf82291]{color:#ff2700;font-size:%?24?%;line-height:1.2;text-align:right;font-weight:400}.signin-info-item .signin-content .content-remark[data-v-7bf82291]{font-size:%?24?%;color:#845636;line-height:1.2}.signin-info-item .signin-content .content-remark.light[data-v-7bf82291]{color:#fe0000}.signin-info-item .signin-touch[data-v-7bf82291]{flex-shrink:0}.signin-info-item .signin-touch .touch-btn[data-v-7bf82291]{display:flex;align-items:center;justify-content:center;padding:0 %?10?%;min-width:%?140?%;height:%?56?%;background:linear-gradient(#ff895e,#ff6334);border-radius:%?16?%;color:#fff;font-size:%?24?%}',
                '',
            ]),
                (e.default = i);
        },
        99999: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.signin-space[data-v-7745f4c0]{width:100%}.signin-space .signin-bar[data-v-7745f4c0]{display:inline-flex;width:100%;align-items:center;background:#fff6ef;border-radius:%?18?%}.signin-space .signin-bar .signin-item[data-v-7745f4c0]{position:relative;width:14.28%}.signin-space .signin-bar .signin-item[data-v-7745f4c0]:not(:last-child)::after{position:absolute;top:%?45?%;left:50%;width:%?100?%;height:%?4?%;background:#f2e2d9;content:"";z-index:0}',
                '',
            ]),
                (e.default = i);
        },
        3331: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.signin-item-view[data-v-6b96a6f8]{position:relative;display:flex;flex-direction:column;align-items:center;width:100%}.signin-item-view .signin-item-content[data-v-6b96a6f8]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;width:%?80?%;height:%?90?%;z-index:1}.signin-item-view .signin-item-content .signin-item-dot[data-v-6b96a6f8]{position:relative;display:flex;align-items:center;justify-content:center;width:%?60?%;height:%?60?%;border-radius:50%;border:%?2?% solid #f4d0bd;background-color:#fff}.signin-item-view .signin-item-content .signin-item-dot .dot-arrow[data-v-6b96a6f8]{font-size:%?32?%;color:#f4d0bd}.signin-item-view .signin-item-content .signin-item-dot.done[data-v-6b96a6f8]{background:linear-gradient(0deg,#fe291a,#ff5d32 99%);border:%?2?% solid #f22415}.signin-item-view .signin-item-content .signin-item-dot.done .dot-arrow[data-v-6b96a6f8]{color:#fff}.signin-item-view .signin-item-content .signin-item-coupon[data-v-6b96a6f8]{position:relative;width:%?70?%;height:%?85?%}.signin-item-view .signin-item-content .signin-item-coupon .coupon-bg[data-v-6b96a6f8]{width:%?70?%;height:%?85?%}.signin-item-view .signin-item-content .signin-item-coupon .coupon-price[data-v-6b96a6f8]{position:absolute;display:flex;align-items:center;bottom:%?8?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);z-index:1;color:#f8f9fb;white-space:nowrap}.signin-item-view .signin-item-content .signin-item-coupon .coupon-price .price[data-v-6b96a6f8]{font-size:%?32?%;font-weight:700;white-space:nowrap}.signin-item-view .signin-item-content .signin-item-coupon .coupon-price .unit[data-v-6b96a6f8]{margin-top:%?4?%;font-size:%?20?%}.signin-item-view .signin-item-label[data-v-6b96a6f8]{margin-top:%?4?%;position:relative;z-index:1;font-size:%?22?%;color:#7f7f7f;white-space:nowrap;text-align:center}.signin-item-view .signin-item-label.light[data-v-6b96a6f8]{color:#fe2a1b}.signin-item-view .patch-label[data-v-6b96a6f8]{bottom:0;left:50%;position:absolute;-webkit-transform:translate(-50%,120%);transform:translate(-50%,120%);white-space:nowrap;font-size:%?22?%;color:#7f7f7f}',
                '',
            ]),
                (e.default = i);
        },
        10441: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.step-item[data-v-6c9ed35c]{display:flex;justify-content:space-between;align-items:center;width:100%;padding:%?30?% %?25?% %?30?% 0}.step-item .step-name[data-v-6c9ed35c]{padding-right:%?10?%;flex:1;display:flex;align-items:center;color:#0c0e0b;font-size:%?26?%}.step-item .step-name .step-title[data-v-6c9ed35c]{padding-right:%?10?%;line-height:1.25;white-space:pre-wrap}.step-item .step-name .disabled[data-v-6c9ed35c]{color:#7e7e7e}.step-item .step-name .light[data-v-6c9ed35c]{flex-shrink:0;display:flex;align-items:center;justify-content:center;padding:0 %?10?%;color:#ff2700;height:%?32?%;background:#fff6f3;border-radius:%?7?%;font-size:%?22?%}.step-item .step-status[data-v-6c9ed35c]{flex-shrink:0;font-size:%?24?%}.step-item .step-status .status-todo[data-v-6c9ed35c],\n.step-item .step-status .status-done[data-v-6c9ed35c]{display:flex;align-items:center}.step-item .step-status .status-todo[data-v-6c9ed35c]{font-weight:700;color:#ff2700}.step-item .step-status .status-done[data-v-6c9ed35c]{color:#00c803}.step-item .step-status .status-done .iconfont[data-v-6c9ed35c]{margin-right:%?10?%;font-size:%?36?%}.step-item .step-status .status-get[data-v-6c9ed35c]{display:flex;align-items:center;justify-content:center;padding:0 %?10?%;min-width:%?121?%;height:%?50?%;background:linear-gradient(90deg,#ff895e 0,#ff6334);border-radius:%?10?%;color:#fff}',
                '',
            ]),
                (e.default = i);
        },
        72434: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.station-item-box[data-v-64d40be0]{width:100%}.station-item-box .station-item[data-v-64d40be0]{position:relative;padding:0 %?22?%;margin-bottom:%?16?%;width:100%;border-radius:%?14?%;background-color:#fff;font-size:0}.station-item-box .station-item.shadow[data-v-64d40be0]{box-shadow:0 0 %?17?% 0 rgba(0,0,0,.05)}.station-item-box .station-item .promotion-tips[data-v-64d40be0]{position:absolute;left:0;top:0;line-height:%?41?%;color:#fff}.station-item-box .station-item .promotion-tips .promotion-tips-bg[data-v-64d40be0]{position:absolute;left:0;right:0;width:%?685?%;height:%?41?%;z-index:0}.station-item-box .station-item .promotion-tips .promotion-tips-content[data-v-64d40be0]{font-weight:700;width:%?212?%;position:absolute;display:flex;align-items:center;justify-content:center;color:#fff;font-size:%?24?%;top:0;height:%?41?%}.station-item-box .station-item .main-view[data-v-64d40be0]{position:relative;padding:%?25?% 0;width:100%;font-size:%?28?%}.station-item-box .station-item .main-view.large[data-v-64d40be0]{padding:%?55?% 0 %?25?%}.station-item-box .station-item .main-view .center-view[data-v-64d40be0]{margin-top:%?4?%;display:flex;width:100%}.station-item-box .station-item .main-view .left-view[data-v-64d40be0]{display:flex;align-items:center;flex:1}.station-item-box .station-item .main-view .left-view .price-view[data-v-64d40be0]{padding:%?5?% 0;flex:1;display:flex;align-items:center}.station-item-box .station-item .main-view .left-view .price-bar[data-v-64d40be0]{margin-top:%?4?%;display:flex;align-items:center}.station-item-box .station-item .main-view .left-view .price[data-v-64d40be0]{position:relative;display:flex;align-items:center;color:#ff4e00;font-size:%?46?%;white-space:nowrap;flex-shrink:0}.station-item-box .station-item .main-view .left-view .price .price-unit[data-v-64d40be0]{font-size:%?24?%}.station-item-box .station-item .main-view .left-view .price .newpeople-tag[data-v-64d40be0]{position:absolute;top:0;right:50%;-webkit-transform:translate(50%,-115%);transform:translate(50%,-115%)}.station-item-box .station-item .main-view .left-view .discount-price[data-v-64d40be0]{margin-left:%?10?%;margin-top:%?10?%;display:flex;align-items:center;color:#ff3224;font-size:%?20?%;padding:%?4?% %?10?%;border:%?1?% solid #f0917e;border-radius:%?4?%}.station-item-box .station-item .main-view .right-view[data-v-64d40be0]{display:flex;align-items:center;flex-shrink:0}.station-item-box .station-item .main-view .right-view .charge-count[data-v-64d40be0]{font-size:%?24?%;color:#999}.station-item-box .station-item .main-view .cell-bar[data-v-64d40be0]{width:100%}.station-item-box .station-item .main-view .name-bar[data-v-64d40be0]{display:flex;align-items:center;justify-content:space-between}.station-item-box .station-item .main-view .name-bar .close-btn[data-v-64d40be0]{display:flex;align-items:center;justify-content:flex-end;min-width:%?80?%;margin-left:%?20?%;padding:%?10?% 0 %?10?% %?20?%;flex-shrink:0;font-size:%?24?%;color:#7e7e7e}.station-item-box .station-item .main-view .name-bar .close-btn .iconguanbi[data-v-64d40be0]{font-size:%?32?%}.station-item-box .station-item .main-view .name-bar .close-btn .seat-img[data-v-64d40be0]{width:%?4?%;height:%?4?%}.station-item-box .station-item .main-view .name-bar .position-btn[data-v-64d40be0]{display:flex;align-items:center;justify-content:center;min-width:%?120?%;height:%?47?%;margin-left:%?20?%;flex-shrink:0;font-size:%?24?%;color:#666;border:%?1?% solid #e5e5e5;border-radius:%?25?%}.station-item-box .station-item .main-view .name-bar .position-btn .seat-img[data-v-64d40be0]{width:%?4?%;height:%?4?%}.station-item-box .station-item .main-view .name-bar .position-btn .iconguanbi[data-v-64d40be0]{font-size:%?32?%}.station-item-box .station-item .main-view .name-bar .position-btn .icondaohang[data-v-64d40be0]{font-size:%?20?%}.station-item-box .station-item .main-view .name-bar .position-btn .unit[data-v-64d40be0]{font-size:%?24?%}.station-item-box .station-item .main-view .name-bar .name-info[data-v-64d40be0]{display:flex;align-items:center}.station-item-box .station-item .main-view .name-bar .name-info .name[data-v-64d40be0]{flex:1;margin-right:%?10?%;color:#0c0e0c;font-size:%?32?%;font-weight:700;line-height:1;line-height:%?36?%;white-space:nowrap}.station-item-box .station-item .main-view .name-bar .name-info .name.short[data-v-64d40be0]{color:#8b9097}.station-item-box .station-item .main-view .name-bar .name-info .offline-tag[data-v-64d40be0]{flex-shrink:0;margin-right:%?10?%;display:flex;justify-content:center;align-items:center;height:%?34?%;padding:0 %?10?%;border-radius:%?4?%;background-color:#a1a8af;color:#fff;font-size:%?22?%}.station-item-box .station-item .main-view .name-bar .name-info .offline-tag.blue[data-v-64d40be0]{color:#fff;background-color:#1075ff}.station-item-box .station-item .main-view .name-bar .name-info .offline-tag.green[data-v-64d40be0]{color:#fff;background-color:#57b8a3}.station-item-box .station-item .main-view .name-bar .name-info .offline-tag.orange[data-v-64d40be0]{color:#fff;background-color:#ff2700}.station-item-box .station-item .main-view .name-bar .name-info .top-tag[data-v-64d40be0]{flex-shrink:0;padding:0 %?10?%;display:flex;height:%?34?%;align-items:center;margin-right:%?10?%;text-align:center;font-size:%?22?%;border-radius:%?6?%;color:#fff;background:#fa5f2b;white-space:nowrap}.station-item-box .station-item .main-view .name-bar .name-info .top-tag .top-tag-icon[data-v-64d40be0]{margin-right:%?5?%;width:%?22?%;height:%?22?%}.station-item-box .station-item .main-view .name-bar .active-tag[data-v-64d40be0]{flex-shrink:0;margin:%?-25?% 0 0 %?-30?%;width:%?66?%;height:%?66?%}.station-item-box .station-item .main-view .parking-bar[data-v-64d40be0]{margin-top:%?22?%;display:flex;align-items:center;font-size:%?22?%;background-color:#f7f7f7;border-radius:%?6?%}.station-item-box .station-item .main-view .parking-bar .tag-icon[data-v-64d40be0]{display:flex;align-items:center;justify-content:center;margin-right:%?10?%;width:%?34?%;height:%?34?%;flex-shrink:0;color:#0c0e0c;font-size:%?24?%;background-color:#f2ffc1;border-radius:%?6?%;font-weight:700}.station-item-box .station-item .main-view .parking-bar .desc[data-v-64d40be0]{display:flex;align-items:center;flex:1;overflow:hidden;padding-right:%?10?%;color:#0c0e0c;line-height:%?34?%}.station-item-box .station-item .main-view .parking-bar .desc .desc-lable[data-v-64d40be0]{flex-shrink:0;font-weight:700;white-space:nowrap}.station-item-box .station-item .main-view .parking-bar .desc .desc-value[data-v-64d40be0]{flex:1;white-space:nowrap}.station-item-box .station-item .main-view .tags-bar[data-v-64d40be0]{margin-top:%?13?%;width:100%;display:flex;align-items:flex-end}.station-item-box .station-item .main-view .station-taglist[data-v-64d40be0]{margin-top:%?12?%}.station-item-box .station-item .main-view .tag-list[data-v-64d40be0]{display:flex;flex:1;align-items:center;flex-wrap:wrap}.station-item-box .station-item .main-view .tag-list .tag-vip-item[data-v-64d40be0]{position:relative;display:inline-flex;font-size:%?22?%;margin-right:%?12?%;padding-right:%?12?%;line-height:1}.station-item-box .station-item .main-view .tag-list .tag-vip-item .vip-item-label[data-v-64d40be0]{position:relative;display:flex;align-items:center;padding:0 %?10?%;height:%?30?%;line-height:1;color:#fff;background:#440812;border-radius:%?8?% 0 0 %?8?%;font-weight:700}.station-item-box .station-item .main-view .tag-list .tag-vip-item .vip-item-label[data-v-64d40be0]:after{position:absolute;right:0;top:50%;content:"";width:%?10?%;height:%?10?%;border-radius:50%;background:#fff1df;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.station-item-box .station-item .main-view .tag-list .tag-vip-item .vip-item-value[data-v-64d40be0]{display:flex;padding:0 %?10?%;align-items:center;height:%?30?%;color:#440812;background:#fff1df;border-radius:0 %?8?% %?8?% 0;font-size:%?28?%;line-height:1}.station-item-box .station-item .main-view .tag-list .tag-vip-item .vip-item-value .unit[data-v-64d40be0]{font-size:%?22?%;font-weight:700;-webkit-transform:translateY(%?4?%);transform:translateY(%?4?%)}.station-item-box .station-item .main-view .tag-list .tag-vip-item.pro .vip-item-label[data-v-64d40be0]{background:linear-gradient(90deg,#aa5800,#7f3a00)}.station-item-box .station-item .main-view .tag-list .tag-item[data-v-64d40be0]{position:relative;display:inline-flex;margin-right:%?12?%;padding-right:%?12?%;font-size:%?24?%;color:#7e7e7e;line-height:%?30?%}.station-item-box .station-item .main-view .tag-list .tag-item[data-v-64d40be0]:not(:last-child):after{position:absolute;content:"";top:50%;right:0;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:1px;height:%?15?%;background-color:#ddd}.station-item-box .station-item .main-view .tag-list .act-tag-item[data-v-64d40be0]{display:inline-block;margin:%?4?% %?8?% %?4?% 0;padding:0 %?8?%;font-size:%?20?%;line-height:%?30?%;border-radius:%?4?%;color:#7e7e7e;background-color:#fff;border:%?1?% solid #d5d5d5;white-space:nowrap}.station-item-box .station-item .main-view .tag-list .act-tag-item.light[data-v-64d40be0]{color:#ff2700;border:1px solid #ff937f}.station-item-box .station-item .main-view .tag-list .act-tag-item.vip[data-v-64d40be0]{background-color:#fff2e2;border:1px solid #fff2e2;color:#934b00}.station-item-box .green-tips-bar[data-v-64d40be0]{position:relative;display:flex;margin-bottom:%?-28?%;align-items:center;width:100%;height:%?100?%;padding:0 %?30?%;font-size:%?34?%;color:#fff;font-weight:700}.station-item-box .green-tips-bar .green-station-bg[data-v-64d40be0]{position:absolute;left:0;top:0;width:100%;height:%?90?%;z-index:0}',
                '',
            ]),
                (e.default = i);
        },
        2631: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.newpeople-discount-bar[data-v-7a2b8e01]{position:relative;display:inline-flex;align-items:center;font-size:%?20?%;height:%?34?%;border-radius:%?12?%;white-space:nowrap}.newpeople-discount-bar.round[data-v-7a2b8e01]{border-radius:%?20?%!important}.newpeople-discount-bar.topLeft[data-v-7a2b8e01]{border-bottom-right-radius:0!important}.newpeople-discount-bar.topRight[data-v-7a2b8e01]{border-bottom-left-radius:0!important}.newpeople-discount-bar.bottomLeft[data-v-7a2b8e01]{border-top-right-radius:0!important}.newpeople-discount-bar.bottomRight[data-v-7a2b8e01]{border-top-left-radius:0!important}.newpeople-discount-bar .discount-bar-main[data-v-7a2b8e01]{white-space:nowrap;display:flex;align-items:center;padding:0 %?12?%;font-weight:700}.newpeople-discount-bar .iconxiala1[data-v-7a2b8e01]{position:absolute}.newpeople-discount-bar .iconxiala1.left[data-v-7a2b8e01]{left:0;top:50%;-webkit-transform:translate(-60%,-50%) rotate(-90deg);transform:translate(-60%,-50%) rotate(-90deg)}.newpeople-discount-bar .iconxiala1.top[data-v-7a2b8e01]{left:50%;bottom:0;-webkit-transform:translate(-50%,66%) rotate(180deg);transform:translate(-50%,66%) rotate(180deg)}',
                '',
            ]),
                (e.default = i);
        },
        29385: function (t, e, i) {
            i.r(e);
            var n = i(45522),
                a = ((n = i.n(n)), i(67643));
            i = i.n(a)()(n());
            i.push([
                t.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.task-show-page[data-v-ed3f8c54]{padding:%?25?%}',
                '',
            ]),
                (e.default = i);
        },
        59489: function (t, e, i) {
            i.d(e, {
                $J: function () {
                    return r;
                },
                CC: function () {
                    return n;
                },
                M5: function () {
                    return o;
                },
                Rr: function () {
                    return a;
                },
                S$: function () {
                    return l;
                },
                jr: function () {
                    return s;
                },
            });
            var n = { SINGLE: '01', MERGE: '02' },
                a = {
                    UNSTART: 'unstart',
                    UNRECEIVE: 'unreceive',
                    RECEIVE: 'receive',
                    COMPLETE: 'complete',
                    CLAIM: 'claim',
                },
                o = {
                    TASK: '60',
                    SIGN: '27',
                    AWARD: '14',
                    EQUITY: '61',
                    RECHARGE: '60',
                    STATION_REMEMBER: '66',
                },
                s = { TASK: null, SIGN: null, AWARD: '1410', EQUITY: null, RECHARGE: '6002' },
                r = { DEFAULT: 'default', ACTIVE: 'act' },
                l = { TRANSACTION: '25', CHARGE: '26', ORDER_DETAIL: '27' };
        },
    },
]);
