import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Row, Select, Space } from 'antd';
import React from 'react';
import moment from 'moment';
import {
    AppealStatusOptions,
    AppealTypeOptions,
    AppealPriorityOptions,
} from '@/constants/AppealManagement';

const { RangePicker } = DatePicker;

interface SearchFormProps {
    onSearch: (values: any) => void;
    onReset: () => void;
    loading?: boolean;
}

const SearchForm: React.FC<SearchFormProps> = ({ onSearch, onReset, loading }) => {
    const [form] = Form.useForm();

    const handleSearch = () => {
        form.validateFields().then((values) => {
            const searchParams = { ...values };
            
            // 处理时间范围
            if (values.timeRange && values.timeRange.length === 2) {
                searchParams.startTime = values.timeRange[0].format('YYYY-MM-DD HH:mm:ss');
                searchParams.endTime = values.timeRange[1].format('YYYY-MM-DD HH:mm:ss');
            }
            delete searchParams.timeRange;

            onSearch(searchParams);
        });
    };

    const handleReset = () => {
        form.resetFields();
        onReset();
    };

    return (
        <Form
            form={form}
            layout="vertical"
            style={{
                padding: '24px',
                backgroundColor: '#fafafa',
                borderRadius: '6px',
                marginBottom: '16px',
            }}
        >
            <Row gutter={[16, 16]}>
                <Col span={6}>
                    <Form.Item label="申诉单号" name="appealNo">
                        <Input placeholder="请输入申诉单号" allowClear />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="用户手机号" name="userPhone">
                        <Input placeholder="请输入用户手机号" allowClear />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="商户号" name="merchantId">
                        <Input placeholder="请输入商户号" allowClear />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="申诉状态" name="appealStatus">
                        <Select
                            placeholder="请选择申诉状态"
                            allowClear
                            options={AppealStatusOptions}
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={[16, 16]}>
                <Col span={6}>
                    <Form.Item label="申诉类型" name="appealType">
                        <Select
                            placeholder="请选择申诉类型"
                            allowClear
                            options={AppealTypeOptions}
                        />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="优先级" name="priority">
                        <Select
                            placeholder="请选择优先级"
                            allowClear
                            options={AppealPriorityOptions}
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item label="申诉时间" name="timeRange">
                        <RangePicker
                            style={{ width: '100%' }}
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder={['开始时间', '结束时间']}
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Row>
                <Col span={24} style={{ textAlign: 'right' }}>
                    <Space>
                        <Button icon={<ReloadOutlined />} onClick={handleReset}>
                            重置
                        </Button>
                        <Button
                            type="primary"
                            icon={<SearchOutlined />}
                            onClick={handleSearch}
                            loading={loading}
                        >
                            查询
                        </Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

export default SearchForm;
