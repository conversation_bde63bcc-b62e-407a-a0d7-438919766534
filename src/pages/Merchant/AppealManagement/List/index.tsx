import type { ProFormInstance } from '@ant-design/pro-form';
import { DownloadOutlined, SyncOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { Button, Card, message, Space, Tag, Typography, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import { Link } from 'umi';
import moment from 'moment';

import XdtProTable from '@/components/XdtProTable';
import SearchForm from './components/SearchForm';
import AppealActions from './components/AppealActions';
import {
    queryAppealListApi,
    exportAppealListApi,
    getAppealStatisticsApi,
} from '@/services/Merchant/AppealManagementApi';
import {
    AppealStatusEnum,
    AppealStatusTitle,
    AppealTypeTitle,
    AppealPriorityTitle,
} from '@/constants/AppealManagement';

const AppealListPage = () => {
    const actionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();
    const [searchParams, setSearchParams] = useState<any>({});

    // 获取统计数据
    const { data: statistics, refresh: refreshStatistics } = useRequest(getAppealStatisticsApi);

    // 导出功能
    const { run: exportData, loading: exportLoading } = useRequest(exportAppealListApi, {
        manual: true,
        onSuccess: () => {
            message.success('导出成功');
        },
        onError: () => {
            message.error('导出失败');
        },
    });

    const handleSearch = (values: any) => {
        setSearchParams(values);
        actionRef.current?.reload();
    };

    const handleReset = () => {
        setSearchParams({});
        actionRef.current?.reload();
    };

    const handleExport = () => {
        exportData(searchParams);
    };

    const handleRefresh = () => {
        actionRef.current?.reload();
        refreshStatistics();
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case AppealStatusEnum.PENDING:
                return 'orange';
            case AppealStatusEnum.PROCESSING:
                return 'blue';
            case AppealStatusEnum.PROCESSED:
                return 'green';
            case AppealStatusEnum.REJECTED:
                return 'red';
            case AppealStatusEnum.CLOSED:
                return 'default';
            default:
                return 'default';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case '01': // 低
                return 'default';
            case '02': // 中
                return 'blue';
            case '03': // 高
                return 'orange';
            case '04': // 紧急
                return 'red';
            default:
                return 'default';
        }
    };

    const columns: ProColumnType<API.AppealListItem>[] = [
        {
            title: '申诉单号',
            dataIndex: 'appealNo',
            width: 160,
            fixed: 'left',
            render: (text, record) => (
                <Link to={`/merchant/appealManagement/detail?appealNo=${record.appealNo}`}>
                    {text}
                </Link>
            ),
        },
        {
            title: '用户手机号',
            dataIndex: 'userPhone',
            width: 120,
            render: (text) => text || '-',
        },
        {
            title: '商户号',
            dataIndex: 'merchantId',
            width: 140,
            render: (text) => text || '-',
        },
        {
            title: '申诉类型',
            dataIndex: 'appealType',
            width: 120,
            render: (text) => AppealTypeTitle[text] || text || '-',
        },
        {
            title: '申诉状态',
            dataIndex: 'appealStatus',
            width: 100,
            render: (text) => (
                <Tag color={getStatusColor(text)}>
                    {AppealStatusTitle[text] || text || '-'}
                </Tag>
            ),
        },
        {
            title: '优先级',
            dataIndex: 'priority',
            width: 80,
            render: (text) => (
                <Tag color={getPriorityColor(text)}>
                    {AppealPriorityTitle[text] || text || '-'}
                </Tag>
            ),
        },
        {
            title: '申诉标题',
            dataIndex: 'appealTitle',
            width: 200,
            ellipsis: true,
            render: (text) => (
                <Tooltip title={text}>
                    {text || '-'}
                </Tooltip>
            ),
        },
        {
            title: '申诉金额',
            dataIndex: 'appealAmount',
            width: 100,
            render: (text) => text ? `¥${text}` : '-',
        },
        {
            title: '关联订单',
            dataIndex: 'orderNo',
            width: 160,
            render: (text) => text || '-',
        },
        {
            title: '场站名称',
            dataIndex: 'stationName',
            width: 150,
            ellipsis: true,
            render: (text) => (
                <Tooltip title={text}>
                    {text || '-'}
                </Tooltip>
            ),
        },
        {
            title: '运营商',
            dataIndex: 'operatorName',
            width: 120,
            render: (text) => text || '-',
        },
        {
            title: '城市',
            dataIndex: 'cityName',
            width: 100,
            render: (text) => text || '-',
        },
        {
            title: '申诉时间',
            dataIndex: 'appealTime',
            width: 160,
            render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '处理时间',
            dataIndex: 'handleTime',
            width: 160,
            render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '处理人',
            dataIndex: 'handlePerson',
            width: 100,
            render: (text) => text || '-',
        },
        {
            title: '操作',
            key: 'action',
            width: 150,
            fixed: 'right',
            render: (_, record) => (
                <AppealActions
                    record={record}
                    onSuccess={() => {
                        actionRef.current?.reload();
                        refreshStatistics();
                    }}
                />
            ),
        },
    ];

    return (
        <PageHeaderWrapper>
            {/* 统计卡片 */}
            {statistics && (
                <Card style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                                {statistics.totalCount}
                            </div>
                            <div>总申诉数</div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                                {statistics.pendingCount}
                            </div>
                            <div>待处理</div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                                {statistics.processedCount}
                            </div>
                            <div>已处理</div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#f5222d' }}>
                                {statistics.timeoutCount}
                            </div>
                            <div>超时处理</div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold' }}>
                                {statistics.avgProcessTime}h
                            </div>
                            <div>平均处理时长</div>
                        </div>
                    </div>
                </Card>
            )}

            {/* 搜索表单 */}
            <SearchForm onSearch={handleSearch} onReset={handleReset} />

            {/* 数据表格 */}
            <XdtProTable
                actionRef={actionRef}
                columns={columns}
                formRef={formRef}
                requestApi={(params) => queryAppealListApi({ ...params, ...searchParams })}
                rowKey="id"
                scroll={{ x: 1800 }}
                toolButtons={[
                    <Button
                        key="refresh"
                        icon={<SyncOutlined />}
                        onClick={handleRefresh}
                    >
                        刷新
                    </Button>,
                    <Button
                        key="export"
                        icon={<DownloadOutlined />}
                        onClick={handleExport}
                        loading={exportLoading}
                    >
                        导出
                    </Button>,
                ]}
            />
        </PageHeaderWrapper>
    );
};

export default AppealListPage;
