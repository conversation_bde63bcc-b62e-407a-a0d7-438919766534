import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { 
    Card, 
    Descriptions, 
    Spin, 
    Tag, 
    Timeline, 
    Typography, 
    Image,
    Button,
    Space,
    message 
} from 'antd';
import React from 'react';
import { useLocation, history } from 'umi';
import moment from 'moment';

import { getAppealDetailApi } from '@/services/Merchant/AppealManagementApi';
import {
    AppealStatusTitle,
    AppealTypeTitle,
    AppealPriorityTitle,
} from '@/constants/AppealManagement';
import AppealActions from '../List/components/AppealActions';

const { Title, Text } = Typography;

const AppealDetailPage = () => {
    const location = useLocation();
    const query = new URLSearchParams(location.search);
    const appealNo = query.get('appealNo');

    const { data: appealDetail, loading, refresh } = useRequest(
        () => getAppealDetailApi(appealNo!),
        {
            ready: !!appealNo,
            onError: () => {
                message.error('获取申诉详情失败');
            },
        }
    );

    if (!appealNo) {
        return (
            <PageHeaderWrapper>
                <Card>
                    <div style={{ textAlign: 'center', padding: '50px' }}>
                        <Text type="secondary">申诉单号不能为空</Text>
                    </div>
                </Card>
            </PageHeaderWrapper>
        );
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case '01': return 'orange';
            case '02': return 'blue';
            case '03': return 'green';
            case '04': return 'red';
            case '05': return 'default';
            default: return 'default';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case '01': return 'default';
            case '02': return 'blue';
            case '03': return 'orange';
            case '04': return 'red';
            default: return 'default';
        }
    };

    return (
        <PageHeaderWrapper
            title="申诉详情"
            extra={[
                <Button key="back" onClick={() => history.goBack()}>
                    返回
                </Button>,
            ]}
        >
            <Spin spinning={loading}>
                {appealDetail?.data && (
                    <div style={{ display: 'flex', gap: '16px' }}>
                        {/* 左侧主要信息 */}
                        <div style={{ flex: 2 }}>
                            {/* 基本信息 */}
                            <Card title="基本信息" style={{ marginBottom: '16px' }}>
                                <Descriptions column={2} bordered>
                                    <Descriptions.Item label="申诉单号">
                                        {appealDetail.data.appealNo}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申诉状态">
                                        <Tag color={getStatusColor(appealDetail.data.appealStatus)}>
                                            {AppealStatusTitle[appealDetail.data.appealStatus]}
                                        </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申诉类型">
                                        {AppealTypeTitle[appealDetail.data.appealType]}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="优先级">
                                        <Tag color={getPriorityColor(appealDetail.data.priority)}>
                                            {AppealPriorityTitle[appealDetail.data.priority]}
                                        </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="用户手机号">
                                        {appealDetail.data.userPhone}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="商户号">
                                        {appealDetail.data.merchantId}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申诉金额">
                                        {appealDetail.data.appealAmount ? `¥${appealDetail.data.appealAmount}` : '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="关联订单">
                                        {appealDetail.data.orderNo || '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申诉时间">
                                        {moment(appealDetail.data.appealTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="处理截止时间">
                                        {appealDetail.data.deadlineTime ? 
                                            moment(appealDetail.data.deadlineTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                                    </Descriptions.Item>
                                </Descriptions>
                            </Card>

                            {/* 申诉内容 */}
                            <Card title="申诉内容" style={{ marginBottom: '16px' }}>
                                <div style={{ marginBottom: '16px' }}>
                                    <Title level={5}>申诉标题</Title>
                                    <Text>{appealDetail.data.appealTitle}</Text>
                                </div>
                                <div style={{ marginBottom: '16px' }}>
                                    <Title level={5}>申诉详情</Title>
                                    <Text>{appealDetail.data.appealContent}</Text>
                                </div>
                                {appealDetail.data.attachments && appealDetail.data.attachments.length > 0 && (
                                    <div>
                                        <Title level={5}>相关附件</Title>
                                        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                                            {appealDetail.data.attachments.map((attachment, index) => (
                                                <Image
                                                    key={index}
                                                    width={100}
                                                    height={100}
                                                    src={attachment.fileUrl}
                                                    alt={attachment.fileName}
                                                    style={{ objectFit: 'cover' }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </Card>

                            {/* 处理结果 */}
                            {appealDetail.data.handleResult && (
                                <Card title="处理结果" style={{ marginBottom: '16px' }}>
                                    <Descriptions column={1} bordered>
                                        <Descriptions.Item label="处理结果">
                                            {appealDetail.data.handleResult}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理说明">
                                            {appealDetail.data.handleRemark || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理人">
                                            {appealDetail.data.handlePerson || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理时间">
                                            {appealDetail.data.handleTime ? 
                                                moment(appealDetail.data.handleTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Card>
                            )}
                        </div>

                        {/* 右侧操作和时间线 */}
                        <div style={{ flex: 1 }}>
                            {/* 操作区域 */}
                            <Card title="操作" style={{ marginBottom: '16px' }}>
                                <AppealActions
                                    record={appealDetail.data}
                                    onSuccess={refresh}
                                />
                            </Card>

                            {/* 处理时间线 */}
                            {appealDetail.data.handleHistory && appealDetail.data.handleHistory.length > 0 && (
                                <Card title="处理记录">
                                    <Timeline>
                                        {appealDetail.data.handleHistory.map((record, index) => (
                                            <Timeline.Item key={index}>
                                                <div>
                                                    <Text strong>{record.operationTypeName}</Text>
                                                    <br />
                                                    <Text type="secondary">
                                                        {moment(record.operationTime).format('YYYY-MM-DD HH:mm:ss')}
                                                    </Text>
                                                    <br />
                                                    <Text>操作人：{record.operatorName}</Text>
                                                    {record.operationContent && (
                                                        <>
                                                            <br />
                                                            <Text>{record.operationContent}</Text>
                                                        </>
                                                    )}
                                                </div>
                                            </Timeline.Item>
                                        ))}
                                    </Timeline>
                                </Card>
                            )}
                        </div>
                    </div>
                )}
            </Spin>
        </PageHeaderWrapper>
    );
};

export default AppealDetailPage;
