import {
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    EllipsisOutlined,
    DownOutlined,
} from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    Modal,
    Select,
    Space,
    Input,
    DatePicker,
    Tooltip,
    message,
    Dropdown,
    Menu,
    TreeSelect,
    Typography,
    Radio,
} from 'antd';
import React, { Fragment, useEffect, useState, useMemo, useRef } from 'react';
import { history, connect, Link } from 'umi';

import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import styles from '@/assets/styles/common.less';
import stationStyles from './StationManageListPage.less';
import { getCityListApi, getCountryListApi } from '@/services/CommonApi';
import ConfirmModal from '../TimerTaskManage/ConfirmModal';
import ConfirmDownModal from './components/ConfirmDownModal';
import DuplicateStationModal from './components/DuplicateStationModal';
import StationAbandonModal from './components/StationAbandonModal';
import StationSyncModal from './components/StationSyncModal';
import WorkOrderHXTool, { WORK_TYPES } from '../../MarketingManage/WorkOrder/WorkOrderHXTool';
import { checkStationPriceEvent } from './checkStationFunctions';

import {
    stationOffLineApi,
    exportStationListApi,
    stationManageDiscountOpenOrCloseApi,
    stationLabelRelSaveApi,
    updateStationGoodFlagApi,
    batchSetStationNoticeApi,
    updateCodeStatusApi,
    getChannelSelectListsApi,
} from '@/services/AssetCenter/StationManangeApi';
import { validStationRepeatApi } from '@/services/AssetCenter/StationDucplicateApi';
import { getStationTagTreeApi } from '@/services/AssetCenter/TagManageApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import CacheAreaView from '@/components/CacheAreaView';

import TagSetModal from './components/TagSetModal';
import CodeStatusEditModal from './components/CodeStatusEditModal';
import ImportCodeStatusModal from './components/ImportCodeStatusModal';

import TagImportModal from './components/TagImportModal';
import NoticeInfoModal from './components/NoticeInfoModal';
import AreaCascader from './components/AreaCascader';
import { BATCH_TYPES, BATCH_ACTIONS } from './StationManageConfig';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Select';
import {
    stationHidden,
    stationManageRecover,
    stationOffLine,
    stationShow,
    stationUpLine,
    sendStationPriceMq,
    validStationCustody,
} from '@/services/MngAstApi';
import { AUDIT_OPER_TYPE, AuditOperTitle } from '@/constants/station';
import { isEmpty } from '@/utils/utils';
import { DingTalkAuditModal } from './components/DingTalkAuditModal';
import { ConfirmDuplicateStationModal } from './components/ConfirmDuplicateStationModal';

const { RangePicker } = DatePicker;
const { SHOW_CHILD } = TreeSelect;
const { TextArea } = Input;
const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const PAGE_TYPES = {
    STATION: '01', // 场站管理
    MONITOR: '02', // 场站监控
};
const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const {
        dispatch,
        stationManageModel: {
            operationStatusList,
            openStatusList,
            goodFlagStatusList,
            hiddenStatusList,
            gunOffLineStatusList,
            operationWayStatusList,
            stationBestName,
            stationRepeatValidTypeList,
            stationMonitorStat,
        },
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { codeInfo = {} },
        stationRef,
        user,
    } = props;

    const {
        location: {
            pathname,
            query: { queryType, province, city, country, from, groupName },
        },
    } = history;
    const [cityTreeData, changeCityTreeData] = useState([]);
    const [tagTreeData, changeTagTreeData] = useState([]);
    const [pageType, changePageType] = useState(queryType ? queryType : PAGE_TYPES.STATION);
    const [monitor, changeMonitor] = useState('');
    const [channelList, setChannelList] = useState([]);
    const { parkPriceLabel: parkPriceLabelList, tempCloseType: tempCloseTypeList } = codeInfo;

    const sentToMP = (key, params = {}) => {
        const win = window;
        win._bzt?.trackEvent(key, {
            ...params,
            immediate: true,
        });
    };

    const onChangePageTypeEvent = (e) => {
        changePageType(e.target.value);
        resetForm();
        if (e.target.value == PAGE_TYPES.MONITOR) {
            const { currentUser } = user;
            sentToMP('StationMonitorBtn', {
                xdt_name: currentUser.displayName,
                xdt_account: currentUser.accountName,
                xdt_organization: currentUser.orgs?.map((ele) => ele.orgName)?.join('、'),
            });
        }
    };

    const toggleBlanceView = () => {
        changePageType(PAGE_TYPES.STATION);
    };

    useEffect(() => {
        if (from == 'dingtalk') {
            // 从钉钉跳转来的带参数的查询链接
            sentToMP('PcBulletinBtn', {
                group_name: groupName,
            });
        }
        initTagData();
        initCityTree();
        if (!stationMonitorStat?.length) {
            let params = {
                province,
                city,
            };
            getStationMonitorStatInfo(params);
        }
        if (!parkPriceLabelList) {
            dispatch({
                type: 'global/initCode',
                code: 'parkPriceLabel',
            });
        }
        if (!tempCloseTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'tempCloseType',
            });
        }
        getChannelSelectListsApi().then((res) => {
            const { data = {} } = res;
            const { records = [] } = data;
            setChannelList(records);
        });
    }, []);
    // 获取监控数据
    const getStationMonitorStatInfo = async (params) => {
        try {
            dispatch({
                type: 'stationManageModel/getStationMonitorStat',
                options: {
                    ...params,
                },
            });
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const formatCityItem = (data, isLeaf = false) => {
        const list = [];
        for (const item of data) {
            list.push({
                key: item.areaCode,
                label: item.areaName,
                value: item.areaCode,
                isLeaf,
            });
        }
        return list;
    };

    const initTagData = async () => {
        try {
            const {
                data: { list },
            } = await getStationTagTreeApi();

            changeTagTreeData(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const initCityTree = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = [];
            for (const item of areaList) {
                list.push({
                    key: item.areaCode,
                    label: item.areaName,
                    value: item.areaCode,
                    children: item.cityList ? formatCityItem(item.cityList) : [],
                });
            }
            changeCityTreeData(list);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const tagTreeOptions = useMemo(() => {
        let list = [];
        if (tagTreeData instanceof Array) {
            for (const item of tagTreeData) {
                if (item.parentId === 0) {
                    const children = tagTreeData.filter((ele) => ele.parentId === item.id);
                    if (children && children.length > 0) {
                        item.children = children;
                    }
                    list.push(item);
                }
            }
        }
        return list;
    }, [tagTreeData]);

    const onFinish = async (values) => {
        const data = await form.validateFields();
        let params = {
            province: data.province,
            city: data.city,
            buildId: data.buildId,
        };
        getStationMonitorStatInfo(params);
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
        getStationMonitorStatInfo({});
    };

    const operGroupRef = useRef();
    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    return (
        <Fragment>
            <div style={{ marginBottom: '20px' }}>
                <Radio.Group value={pageType} onChange={onChangePageTypeEvent} buttonStyle="solid">
                    <Radio.Button value={PAGE_TYPES.STATION}>场站管理</Radio.Button>
                    <Radio.Button value={PAGE_TYPES.MONITOR}>场站监控</Radio.Button>
                </Radio.Group>
            </div>

            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{}}
                scrollToFirstError
                {...formItemLayout}
            >
                {pageType === PAGE_TYPES.STATION ? (
                    <>
                        <SearchOptionsBar
                            key={pageType}
                            loading={listLoading}
                            onReset={resetForm}
                            onExportForm={onExportForm}
                            exportName="导出至暂存区"
                            minSpan={24 * 3}
                            onSearchGroup={onSearchGroup}
                        >
                            <Col span={8}>
                                <OperSelectTypeItem name="buildId" form={form}></OperSelectTypeItem>
                            </Col>
                            <Col span={8}>
                                <AreaCascader
                                    form={form}
                                    key={pageType}
                                    formItemLayout={formItemLayout}
                                    label={'所在地区'}
                                    areaKey="district"
                                    partEnabled
                                />
                            </Col>

                            <Col span={8}>
                                <FormItem
                                    label="场站名称"
                                    name="searchKey"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value?.length) {
                                                    const searchKeyList = value.split('\n');
                                                    const filterList = searchKeyList.filter(
                                                        (ele) => ele,
                                                    );
                                                    if (filterList?.length > 100) {
                                                        return Promise.reject('场站名称限制100条');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <TextArea placeholder="多个场站使用回车间隔" allowClear />
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="场站编号:" name="stationNo">
                                    <Input placeholder="请填写" autoComplete="off" allowClear />
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="运营状态:" name="stationOperationStatus">
                                    <Select placeholder="请选择" allowClear>
                                        {operationStatusList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="是否上线:" name="openFlagStatus">
                                    <Select placeholder="请选择" allowClear>
                                        {openStatusList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="是否隐藏:" name="hiddenFlagStatus">
                                    <Select placeholder="请选择" allowClear>
                                        {hiddenStatusList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="暂停开放:" name="tempCloseType">
                                    <Select
                                        placeholder="请选择"
                                        allowClear
                                        options={tempCloseTypeList}
                                        fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    />
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="运营方式:" name="operationWayStatus">
                                    <Select placeholder="请选择" allowClear>
                                        {operationWayStatusList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="合作平台:" name="cooperationPlatform">
                                    <SelectCooperationPlatform multiple></SelectCooperationPlatform>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="内部底层" name="internalChannelFlag">
                                    <Select placeholder="请选择" allowClear>
                                        <Option value={'0'}>否</Option>
                                        <Option value={'1'}>是</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="场站标签:" name="labelIdList">
                                    <TreeSelect
                                        treeData={tagTreeOptions}
                                        showCheckedStrategy={SHOW_CHILD}
                                        treeCheckable
                                        showArrow
                                        treeDefaultExpandAll
                                        placeholder="请选择"
                                        allowClear
                                        treeNodeFilterProp="name"
                                        fieldNames={{
                                            label: 'name',
                                            value: 'id',
                                            children: 'children',
                                        }}
                                    />
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label={`是否${stationBestName}`} name="goodFlag">
                                    <Select placeholder="请选择" allowClear>
                                        {goodFlagStatusList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>

                            <Col span={8}>
                                <FormItem label="已配路书" name="guideFlag">
                                    <Select placeholder="请选择" allowClear>
                                        <Option value={'0'}>否</Option>
                                        <Option value={'1'}>是</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="停车标签" name="parkPriceLabelDisplay">
                                    <Select
                                        placeholder="请选择"
                                        allowClear
                                        options={parkPriceLabelList}
                                        fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="收取附加费" name="delayFeeFlag">
                                    <Select placeholder="请选择" allowClear>
                                        <Option value={'0'}>否</Option>
                                        <Option value={'1'}>是</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="是否有地锁" name="floorLockFlag">
                                    <Select placeholder="请选择" allowClear>
                                        <Option value={'0'}>否</Option>
                                        <Option value={'1'}>是</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="三方渠道" name="assetChannelList">
                                    <Select
                                        placeholder="请选择"
                                        options={channelList}
                                        fieldNames={{ label: 'channelName', value: 'channelId' }}
                                        showSearch
                                        mode="multiple"
                                        filterOption={(input, option) =>
                                            option.channelName
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        }
                                        allowClear
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem
                                    label="上线时间:"
                                    name="upDates"
                                    // rules={[
                                    //     ({ getFieldValue }) => ({
                                    //         validator(rule, value) {
                                    //             if (!value) {
                                    //                 return Promise.reject('请选择日期');
                                    //             }
                                    //             if (!value[0]) {
                                    //                 return Promise.reject('请选择开始日期');
                                    //             }
                                    //             if (!value[1]) {
                                    //                 return Promise.reject('请选择结束日期');
                                    //             }
                                    //             if (value[0] && value[1]) {
                                    //                 const startTime = +new Date(value[0]);
                                    //                 const endTime = +new Date(value[1]);
                                    //                 const dest = 60 * 1000 * 60 * 24 * 60;

                                    //                 if (Math.abs(startTime - endTime) > dest) {
                                    //                     return Promise.reject('选取范围最大不超过60天');
                                    //                 }
                                    //             }
                                    //             return Promise.resolve();
                                    //         },
                                    //     }),
                                    // ]}
                                >
                                    <RangePicker format="YYYY-MM-DD" />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem
                                    label="下线时间:"
                                    name="downDates"
                                    // rules={[
                                    //     ({ getFieldValue }) => ({
                                    //         validator(rule, value) {
                                    //             if (!value) {
                                    //                 return Promise.reject('请选择日期');
                                    //             }
                                    //             if (!value[0]) {
                                    //                 return Promise.reject('请选择开始日期');
                                    //             }
                                    //             if (!value[1]) {
                                    //                 return Promise.reject('请选择结束日期');
                                    //             }
                                    //             if (value[0] && value[1]) {
                                    //                 const startTime = +new Date(value[0]);
                                    //                 const endTime = +new Date(value[1]);
                                    //                 const dest = 60 * 1000 * 60 * 24 * 60;

                                    //                 if (Math.abs(startTime - endTime) > dest) {
                                    //                     return Promise.reject('选取范围最大不超过60天');
                                    //                 }
                                    //             }
                                    //             return Promise.resolve();
                                    //         },
                                    //     }),
                                    // ]}
                                >
                                    <RangePicker format="YYYY-MM-DD" />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem
                                    label="创建时间:"
                                    name="createDates"
                                    // rules={[
                                    //     ({ getFieldValue }) => ({
                                    //         validator(rule, value) {
                                    //             if (!value) {
                                    //                 return Promise.reject('请选择日期');
                                    //             }
                                    //             if (!value[0]) {
                                    //                 return Promise.reject('请选择开始日期');
                                    //             }
                                    //             if (!value[1]) {
                                    //                 return Promise.reject('请选择结束日期');
                                    //             }
                                    //             if (value[0] && value[1]) {
                                    //                 const startTime = +new Date(value[0]);
                                    //                 const endTime = +new Date(value[1]);
                                    //                 const dest = 60 * 1000 * 60 * 24 * 60;

                                    //                 if (Math.abs(startTime - endTime) > dest) {
                                    //                     return Promise.reject('选取范围最大不超过60天');
                                    //                 }
                                    //             }
                                    //             return Promise.resolve();
                                    //         },
                                    //     }),
                                    // ]}
                                >
                                    <RangePicker format="YYYY-MM-DD" />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="校验结果" name="validProblemFlag">
                                    <Select placeholder="请选择" allowClear>
                                        {stationRepeatValidTypeList?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="疑似重复" name="repeatStationFlag">
                                    <Select placeholder="请选择" allowClear>
                                        {[
                                            { codeValue: '1', codeName: '有' },
                                            { codeValue: '0', codeName: '无' },
                                        ]?.map((ele, index) => (
                                            <Option value={ele.codeValue} key={index}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            </Col>
                            <FormItem noStyle name="stationIds" />
                        </SearchOptionsBar>
                    </>
                ) : (
                    <>
                        <SearchOptionsBar
                            key={pageType}
                            loading={listLoading}
                            onReset={resetForm}
                            onExportForm={onExportForm}
                            exportName="导出至暂存区"
                            minSpan={24 * 3}
                            onSearchGroup={onSearchGroup}
                        >
                            <Col span={8}>
                                <OperSelectTypeItem name="buildId" form={form}></OperSelectTypeItem>
                            </Col>
                            <Col span={8}>
                                <AreaCascader
                                    form={form}
                                    key={pageType}
                                    formItemLayout={formItemLayout}
                                    label={'所在地区'}
                                    areaKey="district"
                                    partEnabled
                                    areaLevel="03"
                                    initialProvince={province}
                                    initialCity={city}
                                    initialArea={country}
                                />
                            </Col>

                            <Col span={24}>
                                <FormItem
                                    label={
                                        <span>
                                            监控指标
                                            <Tooltip
                                                title={
                                                    <>
                                                        <p>
                                                            新站：最近1天内新拉取到的站，且未废弃，且未上线的场站数量
                                                        </p>
                                                        <p>
                                                            开放站：站点状态是运营，且上线的站点数量
                                                        </p>
                                                        <p>
                                                            不开放站：站点状态是运营，且未上线的站点数量
                                                        </p>
                                                        <p>
                                                            长期不开放站：站点状态是运营，且未上线，且创建时间&gt;30天，且上线时间为空
                                                            <br></br>
                                                            的场站数量，以及站点状态是运营，且未上线，且有上线时间，且下线时间&gt;30天的场站数量
                                                            ，总和
                                                        </p>
                                                        <p>
                                                            P0不开放站：P0站点状态是运营，且未上线的站点数量
                                                        </p>
                                                        <p>
                                                            P1不开放站：P1站点状态是运营，且未上线的站点数量
                                                        </p>
                                                    </>
                                                }
                                            >
                                                <InfoCircleOutlined style={{ marginLeft: '4px' }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    name="statType"
                                >
                                    <Radio.Group>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="1"
                                        >
                                            <div className={[stationStyles.title]}>新站</div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.newStationCount}
                                            </div>
                                        </Radio.Button>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="2"
                                        >
                                            <div className={[stationStyles.title]}>开放站</div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.openStationCount}
                                            </div>
                                        </Radio.Button>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="3"
                                        >
                                            <div className={[stationStyles.title]}>不开放站</div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.notOpenStationCount}
                                            </div>
                                        </Radio.Button>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="4"
                                        >
                                            <div className={[stationStyles.title]}>
                                                长期不开放站
                                            </div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.longNotOpenStationCount}
                                            </div>
                                        </Radio.Button>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="5"
                                        >
                                            <div className={[stationStyles.title]}>P0不开放站</div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.p0NotOpenStationCount}
                                            </div>
                                        </Radio.Button>
                                        <Radio.Button
                                            className={[stationStyles.monitorBtn]}
                                            value="6"
                                        >
                                            <div className={[stationStyles.title]}>P1不开放站</div>
                                            <div className={[stationStyles.value]}>
                                                {stationMonitorStat.p1NotOpenStationCount}
                                            </div>
                                        </Radio.Button>
                                    </Radio.Group>
                                </FormItem>
                            </Col>
                            <FormItem noStyle name="stationIds" />
                        </SearchOptionsBar>
                    </>
                )}

                <OperGroupImportModal
                    title="批量查询"
                    initRef={operGroupRef}
                    onConfirm={(addStationList) => {
                        const list =
                            (addStationList?.length &&
                                addStationList.map((item) => item.stationId)) ||
                            [];
                        form.setFieldsValue({ stationIds: list });
                        const values = form.getFieldsValue();
                        onSubmit(values);
                    }}
                />
            </Form>
        </Fragment>
    );
};

const StationManageListPage = (props) => {
    const {
        dispatch,
        history,
        stationManageModel: {
            stationManageList,
            stationManageListTotal,
            stationManageEnum,
            stationBestName,
            stationAbandonReasonList,
        },
        listLoading,
        global,
        user,
    } = props;

    const { pageInit } = global;
    const [form] = Form.useForm();
    const stationRef = useRef();
    const auditModal = useRef();
    const confirmDuplicateModal = useRef();

    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [curSelectItem, changeCurSelectItem] = useState(undefined);

    // 上线确认弹窗状态
    const [confirmUpVisible, toggleConfirmUpModal] = useState(false);
    // 下线确认弹窗状态
    const [confirmDownVisible, toggleConfirmDownModal] = useState(false);
    // 可上线或下线得站点
    const [stationInfoList, setStationInfoList] = useState([]);
    // 部分场站有误
    const [partialStation, setPartialStation] = useState([]);
    // 校验过的返回的上线列表
    const [validProfitRuleStations, setValidProfitRuleStations] = useState([]);

    const [showTagView, toggleTagView] = useState(false);
    const [showImportView, toggleImportView] = useState(false);

    const [showCodeStatusView, toggleCodeStatusView] = useState(false);
    const [showImportCodeStatusView, toggleImporCodeStatustView] = useState(false);

    const [batchType, changeBatchType] = useState(BATCH_TYPES.LINE);

    // 废弃场站
    const abandonRef = useRef();
    // 重复场站
    const duplicateRef = useRef();
    // 公告设置
    const noticeRef = useRef();
    // 同步站点
    const asyncStationRef = useRef();
    const {
        location: { pathname },
    } = history;
    const cacheName = `${pathname}city`;

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cacheName);

    useEffect(() => {
        if (pageInit[cacheName]) {
            const { stationId, stationName } = pageInit[cacheName].form || {};
            if (stationId && stationName) {
                stationRef.current?.init?.([
                    {
                        stationName: stationName,
                        stationId: stationId,
                    },
                ]);
            }
            form.setFieldsValue(pageInit[cacheName].form);
        }
    }, []);

    useEffect(() => {
        if (!stationManageEnum?.length) {
            dispatch({
                type: 'stationManageModel/getStationEnum',
                options: {},
            });
        }
        if (!stationBestName?.length) {
            dispatch({
                type: 'stationManageModel/getStationGoodFlagName',
                options: {},
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const cacheRef = useRef();
    const searchData = async (isDownLoad) => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                cooperationPlatform:
                    data.cooperationPlatform?.join?.(',') || data.cooperationPlatform || undefined,
                labelIdList: data.labelIdList?.join?.(',') || data.labelIdList || undefined,
                stationIds: data.stationIds?.join?.(',') || data.stationIds || undefined,

                upLineBeginTime: data?.upDates?.[0]?.format('YYYY-MM-DD') || undefined,
                upLineEndTime: data?.upDates?.[1]?.format('YYYY-MM-DD') || undefined,
                upDates: undefined,

                downLineBeginTime: data?.downDates?.[0]?.format('YYYY-MM-DD') || undefined,
                downLineEndTime: data?.downDates?.[1]?.format('YYYY-MM-DD') || undefined,
                downDates: undefined,

                createBeginTime: data?.createDates?.[0]?.format('YYYY-MM-DD') || undefined,
                createEndTime: data?.createDates?.[1]?.format('YYYY-MM-DD') || undefined,
                createDates: undefined,
            };
            if (!params?.labelIdList?.length) {
                delete params.labelIdList;
            }
            delete params.area_key;
            if (data.searchKey) {
                const searchKeyList = data.searchKey.split('\n');
                const filterList = searchKeyList.filter((ele) => ele);
                params.stationNames = filterList?.join(',');
                params.searchKey = undefined;
            }
            if (isEmpty(params.cooperationPlatform)) {
                delete params.cooperationPlatform;
            }

            if (isDownLoad) {
                await exportStationListApi(params);
                cacheRef?.current?.count();
                message.success('提交成功，请到文件暂存区查看');
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;

                dispatch({
                    type: 'global/setPageInit',
                    pathname: cacheName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });
                dispatch({
                    type: 'stationManageModel/getStationManageList',
                    options: params,
                });
            }
            return;
        } catch (error) {
            console.log(5555, error);
            return error;
        }
    };

    const resetData = () => {
        form.resetFields();
        changeSelectItems([]);
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '合作平台',
            width: 180,
            dataIndex: 'cooperationPlatformName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '内部底层',
            width: 120,
            dataIndex: 'internalChannelFlagName',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '场站ID',
            width: 180,
            dataIndex: 'stationId',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '场站编号',
            width: 140,
            dataIndex: 'stationNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '所在地区',
            width: 200,
            dataIndex: 'stationArea',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '交/直流桩数',
            width: 160,
            dataIndex: 'pileNum',
            render(text, record) {
                if (record.acEquipNum || record.dcEquipNum) {
                    const keys = Object.keys(record);
                    const values = keys.map((ele) => record[ele] && `${ele}=${record[ele]}`);
                    const params = values.filter((ele) => ele?.length > 0);
                    return (
                        <Link
                            to={`/assetCenter/pileManage/list?${params?.join?.('&')}`}
                            key="detail"
                            target={'_blank'}
                        >
                            {`${record.acEquipNum === undefined ? '-' : record.acEquipNum} / ${
                                record.dcEquipNum === undefined ? '-' : record.dcEquipNum
                            }`}
                        </Link>
                    );
                }
                return <span>-</span>;
            },
        },
        {
            title: '充电枪数',
            width: 120,
            dataIndex: 'gunNum',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '运营状态',
            width: 120,
            dataIndex: 'operationStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '是否上线',
            width: 120,
            dataIndex: 'openFlagName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '是否隐藏',
            width: 120,
            dataIndex: 'hiddenFlagName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: (
                <span>
                    价格基准
                    <Tooltip title={`原商家优惠关闭，价格基准为原价；开启则为结算价`}>
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 130,
            dataIndex: 'operDiscountName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '运营方式',
            width: 120,
            dataIndex: 'operationWayName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '场站标签',
            width: 200,
            dataIndex: 'labelName',
            render(text, record) {
                return (
                    <div className="text-third" style={{ width: '120px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: `是否${stationBestName}`,
            width: 120,
            dataIndex: 'goodFlagName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '营业时间',
            width: 120,
            dataIndex: 'busiTime',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '服务电话',
            width: 120,
            dataIndex: 'serviceTel',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '场地属性',
            width: 120,
            dataIndex: 'constructionName',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '场站类型',
            width: 120,
            dataIndex: 'stationTypeName',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '地址',
            width: 200,
            dataIndex: 'address',
            render(text, record) {
                return (
                    <div className="text-third" style={{ width: '200px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '计价标准',
            width: 120,
            dataIndex: 'priceStandardName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '当前公告',
            width: 160,
            dataIndex: 'noticeInfo',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '停车费说明',
            width: 160,
            dataIndex: 'parkPrice',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '停车费说明（外显）',
            width: 180,
            dataIndex: 'parkPriceDesc',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '收取附加费',
            width: 160,
            dataIndex: 'delayFeeFlag',
            defaultDismiss: true,
            render(text, record) {
                const delayFeeFlagName = record.delayFeeFlag === '1' ? '是' : '否';
                return <span title={delayFeeFlagName}> {delayFeeFlagName || '-'}</span>;
            },
        },
        {
            title: '附加费说明',
            width: 160,
            dataIndex: 'incrementExplain',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '贴码状态',
            width: 120,
            dataIndex: 'codeStatusName',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '贴码情况说明',
            width: 220,
            dataIndex: 'codeDescription',
            defaultDismiss: true,
            ellipse: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '三方渠道',
            width: 120,
            dataIndex: 'channelName',
            defaultDismiss: true,
            render(text, record) {
                return (
                    <Typography.Text ellipsis={{ rows: 2, tooltip: true }} style={{ width: 120 }}>
                        {text || '-'}
                    </Typography.Text>
                );
            },
        },
        {
            title: '三方场站编号',
            width: 160,
            dataIndex: 'thirdStationNo',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '站点加权',
            width: 120,
            dataIndex: 'weight',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '备注',
            width: 140,
            dataIndex: 'remark',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '备注（本地）',
            width: 140,
            dataIndex: 'noSyncRemark',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },

        {
            title: '上线时间',
            width: 200,
            dataIndex: 'newOpenTime',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '下线时间',
            width: 200,
            dataIndex: 'newCloseTime',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '废弃原因',
            width: 160,
            dataIndex: 'abandonReasonName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '下线原因',
            width: 160,
            dataIndex: 'downReasonName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '最后互通时间',
            width: 200,
            dataIndex: 'newUpdateTime',
            defaultDismiss: true,
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '场站校验结果',
            width: 240,
            dataIndex: 'validResult',
            fixed: 'right',
            render(text, record) {
                if (record.validResult?.length || record.problemType?.length) {
                    return (
                        <Tooltip>
                            <Space direction="vertical">
                                <span>
                                    疑似重复：
                                    {(record.validResult?.length && (
                                        <a
                                            onClick={() => {
                                                duplicateRef.current.show(record.stationId);
                                            }}
                                        >
                                            {record.validResult}
                                        </a>
                                    )) ||
                                        '无'}
                                </span>
                                <span>
                                    数据校验：
                                    {(record.problemType?.length && (
                                        <span>{record.problemType}</span>
                                    )) ||
                                        '正常'}
                                </span>
                            </Space>
                        </Tooltip>
                    );
                }
                return <Tooltip title={record.validTime}>正常</Tooltip>;
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btns = [];

                const detailBtn = (
                    <span className={styles['table-btn']} onClick={() => goDetailEvent(record)}>
                        详情
                    </span>
                );
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editStationEvent(record)}>
                        编辑
                    </span>
                );
                const lineBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            lineStationEvent([record.stationId], true);
                        }}
                    >
                        上线
                    </span>
                );
                const downBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            lineStationEvent([record.stationId]);
                        }}
                    >
                        下线
                    </span>
                );
                const hideBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            hideStationEvent([record.stationId], true);
                        }}
                    >
                        隐藏
                    </span>
                );
                const showBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            hideStationEvent([record.stationId], false);
                        }}
                    >
                        显示
                    </span>
                );
                const discardBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.stationId], true);
                        }}
                    >
                        废弃
                    </span>
                );
                const cardBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.stationId], false);
                        }}
                    >
                        恢复
                    </span>
                );
                const syncPriceBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            syncPriceEvent([record.stationId]);
                        }}
                    >
                        同步价格
                    </span>
                );

                btns.push(detailBtn);
                btns.push(editBtn);
                if (record.openFlag == '1') {
                    // 已上线，显示下线按钮
                    btns.push(downBtn);
                } else if (record.problemType) {
                    // 场站校验不通过，则隐藏单个场站的上线按钮
                } else {
                    btns.push(lineBtn);
                }

                if (record.hiddenFlag == '1') {
                    // 已显示，显示显示按钮
                    btns.push(showBtn);
                } else {
                    btns.push(hideBtn);
                }

                if (record.operationStatus == '05') {
                    // 已废弃，显示恢复按钮
                    btns.push(cardBtn);
                } else {
                    btns.push(discardBtn);
                }

                //同步价格
                btns.push(syncPriceBtn);

                if (user.currentUser?.relaFlag) {
                    btns.push(
                        <WorkOrderHXTool
                            buttonProps={{ type: 'link', style: { padding: 0 } }}
                            key="tool"
                            inParams={record}
                            user={user}
                            dispatch={dispatch}
                            global={global}
                            type={WORK_TYPES.STATION}
                        />,
                    );
                }

                const showBtns = btns.slice(0, 2);
                const moreBtns = btns.slice(2);

                return (
                    <Space>
                        {showBtns}
                        <Dropdown
                            overlay={
                                <Menu
                                    items={moreBtns.map((ele, index) => {
                                        if (!ele) {
                                            return null;
                                        }
                                        return {
                                            key: index,
                                            label: ele,
                                        };
                                    })}
                                />
                            }
                            placement="top"
                        >
                            <EllipsisOutlined className={styles['table-btn']} />
                        </Dropdown>
                    </Space>
                );
            },
        },
    ];

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.stationId),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            name: record.stationId,
        }),
    };

    const goDetailEvent = (item) => {
        history.push(`/assetCenter/stationManage/list/detail/${item.stationId}`);
    };
    const editStationEvent = (item) => {
        history.push(`/assetCenter/stationManage/list/update/${item.stationId}`);
    };

    const lineStationEvent = async (stationIds = [], isUp) => {
        try {
            if (isUp) {
                const { data = [] } = await validStationCustody({
                    stationIds: stationIds.join(','),
                });
                if (!isEmpty(data)) {
                    const allStationName = data?.map((ele) => ele.stationName) || [];
                    Modal.error({
                        title: '上线失败',
                        content: (
                            <div>
                                <Typography.Title level={5}>
                                    {allStationName.join('、')}
                                </Typography.Title>
                                <Typography.Text type="danger">
                                    充电商家管存账户创建异常，请联系接入组
                                </Typography.Text>
                            </div>
                        ),
                    });
                    return;
                }

                const res = await checkStationPriceEvent(stationIds, selectItems);
                if (res && res?.uniqueObjects) {
                    setPartialStation(res?.partialStation);
                    setStationInfoList(res?.uniqueObjects);
                } else {
                    setPartialStation([]);
                    setStationInfoList(selectItems);
                }
                toggleConfirmUpModal(true);
            } else {
                toggleConfirmDownModal(true);
                return;
            }
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const bestStationEvent = (stationIds, isSet) => {
        confirm({
            title: `${isSet ? '设为' : '取消'}${stationBestName}`,
            icon: <ExclamationCircleOutlined />,
            content: `确认将对应场站${isSet ? '设为' : '移出'}${stationBestName}`,
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const params = {
                        stationIds: stationIds?.join?.(',') || stationIds,
                        goodFlag: isSet ? '1' : '0',
                    };
                    await updateStationGoodFlagApi(params);
                    operateFinish(true);
                } catch (error) {}
            },
            onCancel() {
                operateFinish();
            },
        });
    };
    // handle: { request, params } 用于处理重新发起操作，包含接口和参数
    const showOperationResult = (result, type, handle) => {
        searchData();
        if (result?.data) {
            auditModal.current.show({ ...result?.data, type, handle });
        } else {
            message.success('操作成功，请到审批记录或钉钉查看审批进度');
        }
        changeCurSelectItem(undefined);
        changeSelectItems([]);
    };

    const operateFinish = (isSuccess = false) => {
        if (isSuccess) {
            searchData();
            message.success('操作成功');
            changeCurSelectItem(undefined);
            changeSelectItems([]);
        } else if (curSelectItem) {
            changeCurSelectItem(undefined);
        }
    };

    const hideStationEvent = (stationIds, isHide) => {
        confirm({
            title: `${isHide ? '隐藏' : '确认显示'}站点`,
            icon: <ExclamationCircleOutlined />,
            content:
                (isHide &&
                    '已上线的站点隐藏后，将无法在小程序中找到对应的场站，但用户还可以正常扫码充电。') ||
                '',
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const params = {
                        stationIds: stationIds?.join?.(',') || stationIds,
                    };
                    let result;
                    if (isHide) {
                        result = await stationHidden(params);
                        showOperationResult(result, AUDIT_OPER_TYPE.HIDE, {
                            request: stationHidden,
                            params: params,
                        });
                    } else {
                        result = await stationShow(params);
                        showOperationResult(result, AUDIT_OPER_TYPE.SHOW, {
                            request: stationShow,
                            params: params,
                        });
                    }
                } catch (error) {}
            },
            onCancel() {
                operateFinish();
            },
        });
    };

    const [abandonChecking, updateAbandonChecking] = useState(false);
    const discardStationEvent = async (stationIds, isDiscard) => {
        if (isDiscard) {
            updateAbandonChecking(true);
            await abandonRef.current.show(stationIds);
            updateAbandonChecking(false);
        } else {
            confirm({
                title: `确认恢复站点`,
                icon: <ExclamationCircleOutlined />,
                content: '',
                okText: '确定',
                okType: 'danger',
                cancelText: '取消',
                onOk: async () => {
                    try {
                        const params = {
                            stationIds: stationIds?.join?.(',') || stationIds,
                        };
                        const result = await stationManageRecover(params);
                        showOperationResult(result, AUDIT_OPER_TYPE.RESTORE, {
                            request: stationManageRecover,
                            params: params,
                        });
                    } catch (error) {}
                },
                onCancel() {
                    operateFinish();
                },
            });
        }
    };

    const operatePrice = (stationIds, isOpen) => {
        confirm({
            title: `确定设为${isOpen ? '结算价' : '原价'}？`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const params = {
                        stationIds: stationIds?.join?.(',') || stationIds,
                        discountFlag: (isOpen && '1') || '0',
                    };
                    await stationManageDiscountOpenOrCloseApi(params);
                    operateFinish(true);
                } catch (error) {}
            },
            onCancel() {
                operateFinish();
            },
        });
    };

    const syncPriceEvent = (stationIds) => {
        confirm({
            title: `确定同步价格？`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const params = {
                        stationIds: stationIds?.join?.(',') || stationIds,
                    };
                    await sendStationPriceMq(params);
                    message.success('价格同步中，请稍后刷新查看 若同步后仍没有价格，请联系运营商');
                    operateFinish(true);
                } catch (error) {}
            },
            onCancel() {
                operateFinish();
            },
        });
    };

    const openTagView = () => {
        toggleTagView(true);
    };

    const closeTagView = () => {
        toggleTagView(false);
    };

    const openImportView = () => {
        toggleImportView(true);
    };

    const closeImportView = () => {
        toggleImportView(false);
    };

    const onTagFinish = async (values) => {
        try {
            let params = {
                stationIds: selectItems.map((ele) => ele.stationId)?.join?.(','),
                labelIds: values?.stationLables?.join?.(','),
            };
            await stationLabelRelSaveApi(params);
            operateFinish(true);
            closeTagView();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onCodeStatusFinish = async (values) => {
        try {
            let params = {
                stationIds: selectItems.map((ele) => ele.stationId)?.join?.(','),
                ...values,
            };
            await updateCodeStatusApi(params);
            operateFinish(true);
            toggleCodeStatusView(false);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 上线
    const handleStationUpLine = async (validStations) => {
        toggleConfirmUpModal(false);
        try {
            const stationIds = validStations?.filter((v) => v?.hasRule)?.map((v) => v?.stationId);
            if (isEmpty(stationIds)) {
                message.error('没有可上线场站');
                return;
            }
            const params = {
                stationIds: stationIds?.join?.(',') || stationIds,
            };
            const result = await stationUpLine(params);
            showOperationResult(result, AUDIT_OPER_TYPE.ONLINE, {
                request: stationUpLine,
                params: params,
            });
            searchData();
            if (curSelectItem) {
                changeCurSelectItem(undefined);
            } else {
                changeSelectItems([]);
            }
        } catch (error) {
            toggleConfirmUpModal(false);
        }
    };

    const batchBtnBar = useMemo(() => {
        let btns = [];
        switch (batchType) {
            case BATCH_TYPES.LINE:
                btns.push(
                    <Button type="primary" onClick={() => batchSubmitEvent(BATCH_ACTIONS.LINE)}>
                        批量上线
                    </Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.DOWN)}>批量下线</Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.TIME)}>定时管理</Button>,
                );
                break;

            case BATCH_TYPES.HIDE:
                btns.push(
                    <Button type="primary" onClick={() => batchSubmitEvent(BATCH_ACTIONS.HIDE)}>
                        批量隐藏
                    </Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.SHOW)}>批量显示</Button>,
                );
                break;

            case BATCH_TYPES.DISCARD:
                btns.push(
                    <Button
                        type="primary"
                        onClick={() => batchSubmitEvent(BATCH_ACTIONS.DISCARD)}
                        loading={abandonChecking}
                    >
                        批量作废
                    </Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.CARD)}>批量恢复</Button>,
                );
                break;

            case BATCH_TYPES.TAG:
                btns.push(
                    <Dropdown
                        overlay={
                            <Menu
                                items={[
                                    {
                                        label: (
                                            <a
                                                onClick={() =>
                                                    batchSubmitEvent(BATCH_ACTIONS.IMPORT)
                                                }
                                            >
                                                批量导入
                                            </a>
                                        ),
                                        key: '0',
                                    },
                                ]}
                            />
                        }
                    >
                        <Button type="primary" onClick={() => batchSubmitEvent(BATCH_ACTIONS.TAG)}>
                            标签设置
                            <DownOutlined style={{ marginLeft: '6px' }} />
                        </Button>
                    </Dropdown>,
                );
                btns.push(
                    <Dropdown
                        overlay={
                            <Menu
                                items={[
                                    {
                                        label: (
                                            <a
                                                onClick={() =>
                                                    batchSubmitEvent(
                                                        BATCH_ACTIONS.IMPORT_CODE_STATUS,
                                                    )
                                                }
                                            >
                                                批量导入
                                            </a>
                                        ),
                                        key: '0',
                                    },
                                ]}
                            />
                        }
                    >
                        <Button
                            type="primary"
                            onClick={() => batchSubmitEvent(BATCH_ACTIONS.CODE_STATUS)}
                        >
                            贴码状态
                            <DownOutlined style={{ marginLeft: '6px' }} />
                        </Button>
                    </Dropdown>,
                );
                btns.push(
                    <Dropdown
                        overlay={
                            <Menu
                                items={[
                                    {
                                        label: (
                                            <a
                                                onClick={() =>
                                                    batchSubmitEvent(BATCH_ACTIONS.BEST_OFF)
                                                }
                                            >
                                                取消{stationBestName}
                                            </a>
                                        ),
                                        key: '0',
                                    },
                                ]}
                            />
                        }
                    >
                        <Button
                            type="primary"
                            onClick={() => batchSubmitEvent(BATCH_ACTIONS.BEST_ON)}
                        >
                            设为{stationBestName}
                            <DownOutlined style={{ marginLeft: '6px' }} />
                        </Button>
                    </Dropdown>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.NOTICE)}>
                        公告设置
                    </Button>,
                );
                break;

            case BATCH_TYPES.PRICE:
                btns.push(
                    <Button
                        type="primary"
                        onClick={() => batchSubmitEvent(BATCH_ACTIONS.SETTLE_PRICE)}
                    >
                        设为结算价
                    </Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.ORI_PRICE)}>
                        设为原价
                    </Button>,
                );
                break;
            case BATCH_TYPES.SYNC:
                btns.push(
                    <Button
                        type="primary"
                        onClick={() => batchSubmitEvent(BATCH_ACTIONS.SYNC_PRICE)}
                    >
                        同步价格
                    </Button>,
                );
                btns.push(
                    <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.SYNC_STATION)}>
                        同步站点
                    </Button>,
                );
                break;

            default:
                break;
        }
        return btns;
    }, [batchType, selectItems, abandonChecking]);

    /**
     * 批量处理交互统一处理方法
     */
    const batchSubmitEvent = (type) => {
        if (
            type !== BATCH_ACTIONS.TIME &&
            type !== BATCH_ACTIONS.IMPORT &&
            type !== BATCH_ACTIONS.IMPORT_CODE_STATUS &&
            type !== BATCH_ACTIONS.SYNC_STATION &&
            !selectItems?.length
        ) {
            message.info('请选择批量操作的场站');
            return;
        }

        switch (type) {
            case BATCH_ACTIONS.LINE:
                // openLineView();
                if (selectItems.find((ele) => ele.openFlag == '1')) {
                    message.error('勾选项包含已上线场站，请勿重复上线');
                    return;
                }
                if (selectItems.find((ele) => ele.problemType?.length)) {
                    message.error('存在未通过校验的场站，暂无法上线');
                    return;
                }
                lineStationEvent(
                    selectItems.map((ele) => ele.stationId),
                    true,
                );
                break;
            case BATCH_ACTIONS.DOWN:
                if (selectItems.find((ele) => ele.openFlag == '0')) {
                    message.error('勾选项包含已下线场站，请勿重复下线');
                    return;
                }
                lineStationEvent(selectItems.map((ele) => ele.stationId));
                break;
            case BATCH_ACTIONS.TIME:
                history.push('/assetCenter/TimerTaskManage/list');
                break;

            case BATCH_ACTIONS.HIDE:
                if (selectItems.find((ele) => ele.hiddenFlag == '1')) {
                    message.error('勾选项包含已隐藏场站，请勿重复隐藏');
                    return;
                }
                hideStationEvent(
                    selectItems.map((ele) => ele.stationId),
                    true,
                );
                break;
            case BATCH_ACTIONS.SHOW:
                if (selectItems.find((ele) => ele.hiddenFlag == '0')) {
                    message.error('勾选项包含已显示场站，请勿重复显示');
                    return;
                }
                hideStationEvent(selectItems.map((ele) => ele.stationId));
                break;

            case BATCH_ACTIONS.DISCARD:
                if (selectItems.find((ele) => ele.operationStatus == '05')) {
                    message.error('勾选项包含已废弃场站，请勿重复废弃');
                    return;
                }
                discardStationEvent(
                    selectItems.map((ele) => ele.stationId),
                    true,
                );
                break;
            case BATCH_ACTIONS.CARD:
                if (selectItems.find((ele) => ele.operationStatus !== '05')) {
                    message.error('勾选项包含未废弃场站，请勿重复恢复');
                    return;
                }
                discardStationEvent(selectItems.map((ele) => ele.stationId));
                break;

            case BATCH_ACTIONS.TAG:
                openTagView();
                break;
            case BATCH_ACTIONS.IMPORT:
                openImportView();
                break;
            case BATCH_ACTIONS.CODE_STATUS:
                toggleCodeStatusView(true);
                break;
            case BATCH_ACTIONS.IMPORT_CODE_STATUS:
                toggleImporCodeStatustView(true);
                break;
            case BATCH_ACTIONS.BEST_ON:
                if (selectItems.find((ele) => ele.goodFlag == '1')) {
                    message.error(`所选场站包含${stationBestName}，请重新选择`);
                    return;
                }
                bestStationEvent(
                    selectItems.map((ele) => ele.stationId),
                    true,
                );
                break;
            case BATCH_ACTIONS.BEST_OFF:
                if (selectItems.find((ele) => ele.goodFlag != '1')) {
                    message.error(`所选场站包含非${stationBestName}，请重新选择`);
                    return;
                }
                bestStationEvent(
                    selectItems.map((ele) => ele.stationId),
                    false,
                );
                break;

            case BATCH_ACTIONS.SETTLE_PRICE:
                operatePrice(
                    selectItems.map((ele) => ele.stationId),
                    true,
                );
                break;
            case BATCH_ACTIONS.ORI_PRICE:
                operatePrice(selectItems.map((ele) => ele.stationId));
                break;

            case BATCH_ACTIONS.NOTICE:
                noticeRef.current.show();
                break;
            case BATCH_ACTIONS.SYNC_PRICE:
                syncPriceEvent(selectItems.map((ele) => ele.stationId));
                break;

            case BATCH_ACTIONS.SYNC_STATION:
                asyncStationRef?.current?.show();
                break;
            default:
                break;
        }
    };

    return (
        <PageHeaderWrapper extra={<CacheAreaView bizType="stationManageData" initRef={cacheRef} />}>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                    stationRef={stationRef}
                />

                <div className={styles['btn-bar']}>
                    <Space>
                        批量操作：
                        <Select
                            value={batchType}
                            style={{ width: '140px' }}
                            onChange={(type) => {
                                changeBatchType(type);
                            }}
                        >
                            <Option value={BATCH_TYPES.LINE}>场站上线</Option>
                            <Option value={BATCH_TYPES.HIDE}>场站隐藏</Option>
                            <Option value={BATCH_TYPES.DISCARD}>场站作废</Option>
                            <Option value={BATCH_TYPES.TAG}>批量编辑</Option>
                            <Option value={BATCH_TYPES.PRICE}>价格基准</Option>
                            <Option value={BATCH_TYPES.SYNC}>同步数据</Option>
                        </Select>
                        {batchBtnBar}
                    </Space>
                </div>

                <TablePro
                    rowSelection={
                        (stationManageList?.length && {
                            type: 'checkbox',
                            ...rowSelection,
                            fixed: true,
                        }) ||
                        null
                    }
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.stationId}
                    dataSource={stationManageList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationManageListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [10, 20, 50, 100, 300],
                    }}
                />
            </Card>

            {/* 场站上线确认弹窗 */}
            <ConfirmModal
                confirmVisible={confirmUpVisible}
                stationInfoList={
                    (selectItems?.length || curSelectItem) &&
                    confirmUpVisible &&
                    JSON.stringify((selectItems?.length && stationInfoList) || [curSelectItem])
                }
                partialStation={partialStation?.length && partialStation}
                onFinish={async (validStations) => {
                    try {
                        const upArr =
                            (selectItems?.length || curSelectItem) &&
                            confirmUpVisible &&
                            ((selectItems?.length && stationInfoList) || [curSelectItem]);
                        const stationIds = upArr?.map((ele) => ele.stationId);
                        const params = {
                            stationIds: stationIds?.join?.(','),
                        };
                        const { data } = await validStationRepeatApi({
                            ...params,
                        });
                        if (data?.length > 0) {
                            // 疑似处理
                            setValidProfitRuleStations(validStations);
                            confirmDuplicateModal.current.show(data);
                        } else {
                            handleStationUpLine(validStations);
                        }
                        return;
                    } catch (error) {
                        handleStationUpLine(validStations);
                        return Promise.reject(error);
                    }

                    // const upArr =
                    //     (selectItems?.length || curSelectItem) &&
                    //     confirmUpVisible &&
                    //     ((selectItems?.length && stationInfoList) || [curSelectItem]);
                    // const duplicateArr = [];
                    // upArr?.forEach((element) => {
                    //     if (element?.validResult) {
                    //         duplicateArr.push(element);
                    //     }
                    // });
                }}
                onClose={() => toggleConfirmUpModal(false)}
            />

            <ConfirmDownModal
                visible={confirmDownVisible}
                onFinish={async (otherParams) => {
                    try {
                        const stationIds =
                            curSelectItem?.stationId || selectItems.map((ele) => ele.stationId);
                        const params = {
                            ...otherParams,
                            stationIds: stationIds?.join?.(',') || stationIds,
                        };
                        if (otherParams?.tempDownFlag === '1') {
                            await stationOffLine({
                                ...params,
                                operateDesc: undefined,
                                operateReason: undefined,
                            });
                            message.success('操作成功');
                        } else {
                            const result = await stationOffLine({
                                ...params,
                                tempDownFlag: undefined,
                            });
                            showOperationResult(result, AUDIT_OPER_TYPE.OFFLINE, {
                                request: stationOffLine,
                                params: {
                                    ...params,
                                    tempDownFlag: undefined,
                                },
                            });
                        }
                        toggleConfirmDownModal(false);
                        searchData();
                        if (curSelectItem) {
                            changeCurSelectItem(undefined);
                        } else {
                            changeSelectItems([]);
                        }
                    } catch (error) {
                        toggleConfirmDownModal(false);
                    }
                }}
                onClose={() => toggleConfirmDownModal(false)}
            ></ConfirmDownModal>

            <TagSetModal
                visible={showTagView}
                onClose={closeTagView}
                onFinish={onTagFinish}
            ></TagSetModal>

            <TagImportModal
                visible={showImportView}
                onClose={closeImportView}
                onFinish={() => searchData()}
            />
            {/* 贴码状态编辑弹窗 */}
            <CodeStatusEditModal
                visible={showCodeStatusView}
                onClose={() => {
                    toggleCodeStatusView(false);
                }}
                onFinish={onCodeStatusFinish}
            />
            {/* 贴码状态批量导入弹窗 */}
            <ImportCodeStatusModal
                visible={showImportCodeStatusView}
                onClose={() => toggleImporCodeStatustView(false)}
                onFinish={() => searchData()}
            />
            <DuplicateStationModal
                initRef={duplicateRef}
                onFinish={(index) => {
                    if (index === 0 || index > 0) {
                        confirmDuplicateModal.current.setIndex(index);
                    } else {
                        searchData();
                    }
                }}
            />

            <StationAbandonModal
                global={global}
                initRef={abandonRef}
                onFinish={(result, handle) => {
                    showOperationResult(result, AUDIT_OPER_TYPE.ABANDON, handle);
                }}
            />

            <NoticeInfoModal
                initRef={noticeRef}
                onFinish={async (values) => {
                    try {
                        await batchSetStationNoticeApi({
                            stationIds: selectItems.map((ele) => ele.stationId)?.join?.(','),
                            ...values,
                        });
                        noticeRef.current.onClose();
                        operateFinish(true);
                    } catch (error) {}
                }}
            />
            <StationSyncModal ref={asyncStationRef}></StationSyncModal>
            <DingTalkAuditModal initRef={auditModal} />
            <ConfirmDuplicateStationModal
                initRef={confirmDuplicateModal}
                duplicateStation={(stationId, index) => {
                    duplicateRef.current.show(stationId, index);
                }}
                onFinish={() => handleStationUpLine(validProfitRuleStations)}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading, user }) => ({
    stationManageModel,
    global,
    listLoading: loading.effects['stationManageModel/getStationManageList'],
    user,
}))(StationManageListPage);
