import { <PERSON><PERSON><PERSON>erWrapper } from '@ant-design/pro-layout';
import React, { Fragment, useEffect, useState, useMemo, useRef, useReducer } from 'react';
import { connect } from 'umi';
import { LeftOutlined } from '@ant-design/icons';

import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import {
    filetoDataURL,
    urltoImage,
    imagetoCanvas,
    dataURLtoFile,
    formatTableColumns,
} from '@/utils/utils';
import { saveCouponApi, getCouponAddOptionsApi } from '@/services/Marketing/MarketingCouponApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import couponImg from '@/assets/images/coupon.png';
import OperAndStationListByCity from '@/components/OperAndStationListByCity';
import DateLimit from '@/components/DateLimit/index';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import { isEmpty } from '@/utils/utils';
import BudgetFormItem from '@/components/BudgetFormItem/index';
import { BEARING_TYPES } from '@/components//BudgetFormItem/budgetConfig';

import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import { saveStationScopeInfoApi, getStationScopeListApi } from '@/services/CommonApi';

import { COUPON_TYPES, STATION_CONFIG_PAGE_TYPES } from '@/config/declare';

import {
    Button,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    TimePicker,
    Upload,
    TreeSelect,
} from 'antd';
import {
    InfoCircleOutlined,
    MinusCircleOutlined,
    PlusOutlined,
    LoadingOutlined,
    DownOutlined,
} from '@ant-design/icons';
import styles from './CouponUpdatePage.less';
import { RANGE_TYPES } from '@/config/declare';

const { Option } = Select;
const { TreeNode } = TreeSelect;

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;

const formItemLayout = {
    labelCol: {
        flex: '0 0 130px',
    },
    wrapperCol: {
        span: 8,
    },
};
const formItemListLayout = {
    wrapperCol: {
        span: 12,
        offset: 6,
    },
};
const modalformItemLayout = {};

// 日期多选项
const dataOptions = [
    {
        label: '周一',
        value: '1',
    },
    {
        label: '周二',
        value: '2',
    },
    {
        label: '周三',
        value: '3',
    },
    {
        label: '周四',
        value: '4',
    },
    {
        label: '周五',
        value: '5',
    },
    {
        label: '周六',
        value: '6',
    },
    {
        label: '周日',
        value: '7',
    },
];

const CAPITALTYPES = {
    CAPITAL: '01', // 资金券
    PREFERENTIAL: '02', // 优惠券
    DIR: '03', // 定向券
};

// 运营商选择弹窗
const OperatorModelLayout = (props) => {
    const {
        dispatch,
        operatorLoading,
        isLock,
        show,
        couponManageModel: { operationList, operationListTotal },
        onConfirm,
        onCancel,
        selectOperators,
    } = props;

    const [selectItemsKey, changeSelectItemsKey] = useState(''); // 选中项
    const [operform] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: 1000,
    });

    useEffect(() => {
        let tempList = [];
        if (selectOperators != '') {
            tempList = selectOperators.split(',');
        }

        changeSelectItemsKey(tempList);
    }, [selectOperators]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const onFinish = (values) => {
        // onSubmit(values);
        searchData();
    };

    // 调用搜索接口
    const searchData = () => {
        const data = operform.getFieldsValue();

        dispatch({
            type: 'couponManageModel/getOperationList',
            payload: {
                pageNum: pageInfo.pageIndex,
                totalNum: pageInfo.pageSize,
                buildName: data.buildName,
                actFlag: data.actFlag,
            },
        });
    };

    const resetData = () => {
        operform.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const rowSelection = {
        selectedRowKeys: selectItemsKey,
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItemsKey(selectedRowKeys);
        },
        getCheckboxProps: (record) => ({
            disabled: isLock, // Column configuration not to be checked
            name: record.buildId,
        }),
    };

    const onOk = () => {
        if (selectItemsKey.length == 0) {
            message.error('请选择运营商');
            return;
        }
        onConfirm(selectItemsKey.join(','));
    };
    const onCancelEvent = () => {
        let tempList = [];
        if (selectOperators != '') {
            tempList = selectOperators.split(',');
        }
        changeSelectItemsKey(tempList);
        onCancel();
    };

    const columns = formatTableColumns([
        {
            title: '运营商编号',
            dataIndex: 'operNo',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商名称',
            dataIndex: 'operName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商简称',
            dataIndex: 'orgCodeName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ]);
    return (
        <Fragment>
            <Modal
                title="可核销运营商"
                width={800}
                visible={show}
                onCancel={() => {
                    onCancelEvent();
                }}
                footer={null}
                maskClosable={false}
            >
                <Form
                    {...modalformItemLayout}
                    form={operform}
                    onFinish={onFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                        <Col span={8}>
                            <FormItem label="参与平台活动" name="actFlag">
                                <Select placeholder="请选择">
                                    <Option value="1">是</Option>
                                    <Option value="0">否</Option>
                                </Select>
                            </FormItem>
                        </Col>
                        <Col span={8}>
                            <FormItem label="运营商名称" name="buildName">
                                <Input type="search" />
                            </FormItem>
                        </Col>
                        <Col>
                            <Space>
                                <Button type="primary" htmlType="submit">
                                    查询
                                </Button>
                                <Button className={styles['btn-margin']} onClick={resetData}>
                                    重置
                                </Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
                <TablePro
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    loading={operatorLoading}
                    scroll={{ x: 'max-content', y: 450 }}
                    rowKey={(record) => record.buildId}
                    dataSource={operationList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={false}
                    sticky={{ offsetHeader: 0 }}
                    // pagination={{
                    //     current: pageInfo.pageIndex,
                    //     total: operationListTotal,
                    //     pageSize: pageInfo.pageSize,
                    //     showSizeChanger: true,
                    //     showQuickJumper: true,
                    //     showTotal: total => `共 ${total} 条`,
                    // }}
                />
                <div className={commonStyles['btn-bar']} style={{ textAlign: 'center' }}>
                    {!isLock ? (
                        <Button className={commonStyles['btn-item']} type="primary" onClick={onOk}>
                            提交
                        </Button>
                    ) : null}

                    <Button className={commonStyles['btn-item']} onClick={onCancelEvent}>
                        取消
                    </Button>
                </div>
            </Modal>
        </Fragment>
    );
};

// 上传图片按钮
const UploadButton = ({ uploading }) => (
    <div>
        {uploading ? <LoadingOutlined /> : <PlusOutlined />}
        <div className="ant-upload-text">上传</div>
    </div>
);

// 优惠券编辑页面
const CouponUpdatePage = (props) => {
    const {
        dispatch,
        match,
        history,
        location,
        route,
        user,
        global: { channelOptions = [], cpnOwnerOptions, enterpriseList },
        couponManageModel: { conponInfo },
    } = props;

    const { currentUser } = user;
    const { operId } = currentUser;

    const budgetRef = useRef();

    const [showOperatorView, toggleOperatorView] = useState(false); // 运营商弹窗开关
    const [uploading, changeUploading] = useState(false); // 上传加载
    const [imageUrl, changeImageUrl] = useState(''); // 上传文件
    const [fileInfo, changeFileInfo] = useState(null); // 上传文件
    const [buildStatus, changeBuildStatus] = useState('validating'); // 校验选择运营商
    const [isLock, changeLock] = useState(() => {
        if (route.path.indexOf('/marketing/couponCenter/coupon/list/look') >= 0) {
            return true;
        }
        return false;
    }); // 是否不可编辑
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [cpnId, changeCpnId] = useState(match.params.cpnId || null);
    const [isCopy, changeCopy] = useState(() => {
        if (route.path.indexOf('/marketing/couponCenter/coupon/list/copy') >= 0) {
            return true;
        }
        return false;
    });
    const [form] = Form.useForm();

    const operSelectList = useRef();

    const [createOperator, changeCreateOperator] = useState(null);
    const [createOperatorType, changeCreateOperatorType] = useState(null);

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    useEffect(() => {
        if (cpnId) {
            dispatch({
                type: 'couponManageModel/getCouponInfo',
                options: {
                    cpnId,
                },
            });
        }
        const params = {};
        initCouponOptions();
        if (!cpnOwnerOptions || cpnOwnerOptions.length == 0) {
            dispatch({
                type: 'global/initCpnOwnerOptions',
            });
        }
        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
                ...params,
            });
        }
        dispatch({
            type: 'global/getEnterpriseList',
        });
        return () => {
            dispatch({
                type: 'couponManageModel/updateCouponInfo',
                info: {},
            });
        };
    }, []);

    const initCouponOptions = async () => {
        try {
            const {
                data: {
                    createOperator: createOperatorName,
                    createOperatorType: createOperatorTypeValue,
                    brandName,
                },
            } = await getCouponAddOptionsApi();
            form.setFieldsValue({ brandName });
            changeCreateOperator(createOperatorName);
            changeCreateOperatorType(createOperatorTypeValue);
            if (createOperatorTypeValue == '01') {
                form.setFieldsValue({
                    cpnOwner: COUPON_TYPES.ALI,
                    capitalType: CAPITALTYPES.DIR,
                    contributParty: '01',
                });

                const dctCondBuildStr = form.getFieldValue('dctCondBuild');
                const dctCondBuildList =
                    dctCondBuildStr && dctCondBuildStr != '' ? dctCondBuildStr.split(',') : [];
                if (dctCondBuildList.indexOf(operId) < 0) {
                    dctCondBuildList.push(operId);

                    form.setFieldsValue({
                        dctCondBuild: dctCondBuildList.join(','),
                    });
                }
            }
        } catch (error) {}
    };

    const initSelectOperators = useMemo(() => {
        if (conponInfo && conponInfo.dctCondBuild) {
            if (createOperatorType == '01') {
                const dctCondBuildList =
                    conponInfo.dctCondBuild && conponInfo.dctCondBuild != ''
                        ? conponInfo.dctCondBuild.split(',')
                        : [];

                return [...new Set([operId, ...dctCondBuildList])].join(',');
            }
            return conponInfo && conponInfo.dctCondBuild;
        }

        const dctCondBuildStr = form.getFieldValue('dctCondBuild');
        const oldList = dctCondBuildStr && dctCondBuildStr != '' ? dctCondBuildStr.split(',') : [];
        if (oldList.indexOf(operId) < 0) {
            oldList.push(operId);

            return oldList.join(',');
        }

        return '';
    }, [conponInfo, createOperator, createOperatorType, operId]);

    const channelList = useMemo(
        () => channelOptions.map((ele) => ({ value: ele.codeValue, label: ele.codeName })),
        [channelOptions],
    );

    useEffect(() => {
        if (conponInfo) {
            initCoupont(conponInfo);
        }
    }, [conponInfo]);

    const cpnOwnerOptionsView = useMemo(
        () =>
            cpnOwnerOptions.map((ele) => {
                if (operId && ele.codeValue == '02') {
                    // 运营商账号不能设第三方外部券
                    return null;
                }
                return (
                    <Radio key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Radio>
                );
            }),
        [cpnOwnerOptions],
    );

    const enterpriseTreeData = useMemo(() => {
        const loop = (_list) => {
            return _list.map((ele, index) => (
                <TreeNode
                    value={ele.enterpriseId}
                    title={ele.enterpriseName}
                    key={`${ele.enterpriseName}_${index}`}
                >
                    {(ele.childList?.length && loop(ele.childList)) || null}
                </TreeNode>
            ));
        };

        return loop(enterpriseList);
    }, [enterpriseList]);

    // 初始化默认数据
    const initCoupont = async (info) => {
        try {
            const params = {
                cpnOwner: info.cpnOwner || COUPON_TYPES.ALI,
                capitalType: info.capitalType,
                dctCondBuild: info.dctCondBuild || '',
                contributParty: info.contributParty || '02',
                cpnName: info.cpnName,
                brandName: info.brandName,
                cpnNo: info.cpnNo,
                cpnType: info.cpnType,
                templateId: info.templateId,
                chargeCouponFlag: info.chargeCouponFlag,
                cpnNum: info.cpnNum,
                cpnAmt: info.cpnAmt,
                cpnTimeType: info.cpnTimeType,
                dctCondValue: info.dctCondValue,
                fundAccount: info.fundAccount,
                thirdPartName: info.thirdPartName,
                vipCouponFlag: info.vipCouponFlag,
                operatorContributRatio: info.operatorContributRatio,
                platformContributRatio: info.platformContributRatio,
                enterpriseId: info.enterpriseId,
            };

            if (info.getBgnDate && info.getEndDate) {
                params.dateTime = [moment(info.getBgnDate), moment(info.getEndDate)];
            }

            if (info.useMarks) {
                params.useMarks = info.useMarks.split(',');
            }

            if (!isCopy && info.eftDate && info.invDate) {
                params.cpnTime = [moment(info.eftDate), moment(info.invDate)];
            }

            if (info.period && info.period.length > 0) {
                params.dateFlag = '02';
                params.dateList = info.period.map((ele) => {
                    return {
                        availableDay: ele.availableDay.split(','),
                        period: [moment(ele.beginTime, 'HH:mm'), moment(ele.endTime, 'HH:mm')],
                    };
                });
            }
            if (info.cpnTimeType == '2') {
                params.timeDuration = info.timeDuration;
                params.bgnTimeDuration = info.bgnTimeDuration;
            }
            if (params.cpnOwner !== COUPON_TYPES.XDT) {
                params.bgnTimeDuration = 0;
            }

            if (info.filePath) {
                changeImageUrl(info.filePath);
                urlToFile(info.filePath)
                    .then((imgInfo) => {
                        changeFileInfo(imgInfo);
                    })
                    .catch((err) => err);
            }

            if (info.cpnChannel) {
                params.cpnChannel = info.cpnChannel.split(',');
            }
            if (info.cpnDeduction) {
                params.cpnDeduction = info.cpnDeduction.split(',');
            }
            params.cpnVerificationTimes = info.cpnVerificationTimes;

            if (info.budgetMap) {
                //预算配置初始化
                for (const key in info.budgetMap) {
                    if (Object.hasOwnProperty.call(info.budgetMap, key)) {
                        const element = info.budgetMap[key];
                        params[key] = element;
                    }
                }

                if (info.budgetMap?.expenseBearing) {
                    params.expenseBearing =
                        (info.budgetMap?.expenseBearing &&
                            info.budgetMap?.expenseBearing.split(',')) ||
                        [];
                }

                if (params.budgetId && params.dateTime[0] && params.dateTime[1]) {
                    if (budgetRef?.current?.update) {
                        let options = {
                            budgetId: params.budgetId,
                            effTime: params.dateTime[0],
                            expTime: params.dateTime[1],
                            actId: cpnId || '',
                        };
                        budgetRef?.current?.update(options);
                    }
                }
            }

            if (
                (info.stationList &&
                    info.stationList instanceof Array &&
                    !isEmpty(info.stationList)) ||
                info.scopeType !== '1'
            ) {
                params.areaRangeType = RANGE_TYPES.OTHER;
                // params.stationList = info.scopeList.map((ele) => {
                //     let stationId = [];
                //     if (ele.stationId && ele.stationId instanceof Array) {
                //         stationId = ele.stationId.map((item) => Number(item));
                //     }
                //     return {
                //         stationId,
                //         city: ele.city,
                //     };
                // });
                // setTimeout(() => {
                //     operSelectList.current && operSelectList.current.init(params.stationList);
                // }, 1000);
            } else {
                params.areaRangeType = RANGE_TYPES.ALL;
            }

            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    // 图片url转文件
    const urlToFile = async (path) => {
        try {
            const img = await urltoImage(path);
            const [cvs, img64] = imagetoCanvas(img, 'image/png');
            const blob = await dataURLtoFile(img64);
            return blob;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    /**
     * 保存优惠券
     * type  save/send
     */
    const saveCouponEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();

            const params = {
                saveType: type,
                createOperator,
                createOperatorType,
                cpnName: values.cpnName, // 类型：String  必有字段  备注：优惠券名称
                cpnOwner: values.cpnOwner,
                capitalType: values.capitalType, // 类型：String  必有字段  备注：资金类型 01资金券 02优惠券 03 定向券
                brandName: values.brandName || '', // 类型：String  可有字段  备注：品牌名称（capitalType=3时必填）
                cpnNum: values.cpnNum, // 类型：String  必有字段  备注：发行数量
                getBgnDate:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '', // 类型：String  必有字段  备注：领券开始时间
                getEndDate:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '', // 类型：String  必有字段  备注：发券结束时间
                // cpnType: values.cpnType, //类型：String  必有字段  备注：优惠券类型，01现金券 02折扣券
                useMarks: values.useMarks && values.useMarks.join(','), // 类型：String  必有字段  备注：使用说明 逗号隔开
                cpnAmt: values.cpnAmt, // 类型：String  必有字段  备注： 面额，或者折扣
                dctCondValue: values.dctCondValue || '', // 最低消费金额
                cpnTimeType: values.cpnTimeType,

                expenseBearing: values.expenseBearing.join(','),
                budgetId: values.budgetId || '',
                budgetAmount: values.budgetAmount || 0,

                areaRangeType: values.areaRangeType,
                scopeType: values.areaRangeType,
            };

            if (values.vipCouponFlag) {
                params.vipCouponFlag = values.vipCouponFlag;
            }

            if (!isCopy && cpnId) {
                params.cpnId = cpnId;
            }
            if (values.dateFlag == '02') {
                // 可用日期限制
                if (values.dateList) {
                    const dateList = values.dateList.map((ele) => {
                        const period = ele.period.map((item) => {
                            return (item && item.format('HH:mm')) || '';
                        });
                        return {
                            availableDay: (ele.availableDay && ele.availableDay.join(',')) || '',
                            period: period.join(',') || '',
                        };
                    });
                    params.dateList = JSON.stringify(dateList);
                }
            }

            if (values.contributParty == '05') {
                params.enterpriseId = values.enterpriseId;
            }

            if (values.cpnOwner == COUPON_TYPES.OTHER) {
                params.templateId = values.templateId || '';
                params.chargeCouponFlag = values.chargeCouponFlag || '0';
            }
            if (values.cpnOwner == COUPON_TYPES.XDT) {
                params.cpnChannel = (values.cpnChannel && values.cpnChannel.join(',')) || '';
                params.cpnDeduction = (values.cpnDeduction && values.cpnDeduction.join(',')) || '';
                params.cpnVerificationTimes = values.cpnVerificationTimes;
                params.capitalType = CAPITALTYPES.PREFERENTIAL;
                params.contributParty = values.contributParty;

                if (values.areaRangeType !== '1') {
                    const stationOptions = {};
                    const {
                        addStations = [],
                        allStations = [],
                        defaultStations = [],
                        delStations = [],
                    } = values.stationConfig || {};

                    stationOptions.addStationIds = addStations.map((ele) => ele.stationId);
                    stationOptions.submitStationIds = allStations.map((ele) => ele.stationId);
                    stationOptions.delStationIds = delStations.map((ele) => ele.stationId);
                    const {
                        data: { stationScopeKey },
                    } = await saveStationScopeInfoApi(stationOptions);

                    params.stationScopeKey = stationScopeKey;
                }
            }

            if (params.cpnOwner == COUPON_TYPES.BUY) {
                // 购卡券有运营商和出资方
                params.contributParty = values.contributParty;
                params.thirdPartName = values.thirdPartName;
                if (!params.capitalType) {
                    params.capitalType = '02';
                }
            }

            if (params.contributParty == '04') {
                params.operatorContributRatio = values.operatorContributRatio;
            }

            if (params.cpnTimeType == '2') {
                params.timeDuration = values.timeDuration;
                params.bgnTimeDuration = values.bgnTimeDuration;
            } else if (params.cpnTimeType == '1') {
                if (!values.cpnTime[0] || !values.cpnTime[1]) {
                    message.error('请选择生效时间');
                    return;
                }
                params.eftDate =
                    values.cpnTime[0] && values.cpnTime[0].format('YYYY-MM-DD HH:mm:ss');
                params.invDate =
                    values.cpnTime[1] && values.cpnTime[1].format('YYYY-MM-DD HH:mm:ss');
            }

            if (params.cpnOwner !== COUPON_TYPES.XDT) {
                params.bgnTimeDuration = 0;
            }

            if (type == 'save') {
                params.cpnStatus = '01';
            } else if (type == 'send') {
                params.cpnStatus = '02';
            }

            if (params.capitalType == CAPITALTYPES.DIR) {
                // 定向券有运营商和出资方
                params.contributParty = values.contributParty; // 类型：String  必有字段  备注：出资方 02-平台 01-运营商
                if (operId) {
                    params.contributParty = '01';
                }

                const dctCondBuild = form.getFieldValue('dctCondBuild');
                if (!dctCondBuild || dctCondBuild.split(',').length == 0) {
                    message.error('清选择运营商');
                    changeBuildStatus('error');
                    return;
                }
                if (dctCondBuild) {
                    params.dctCondBuild = dctCondBuild;
                    changeBuildStatus('validating');
                }
            }
            if (params.capitalType == CAPITALTYPES.CAPITAL) {
                params.fundAccount = values.fundAccount;
            }

            const fd = new FormData();
            for (const key in params) {
                if (params.hasOwnProperty(key)) {
                    const element = params[key];
                    fd.append(key, element);
                }
            }

            if (params.capitalType == CAPITALTYPES.DIR) {
                // 只有定向券必须传封面
                if (fileInfo) {
                    fd.append('fileName', fileInfo, `${Math.random().toString(36).substr(2)}.png`);
                } else {
                    const imgInfo = await urlToFile(couponImg);
                    fd.append('fileName', imgInfo, `${Math.random().toString(36).substr(2)}.png`);
                }
            }

            changeSubmitLoading(true);
            const {
                data: { cpnId: newCpnId },
            } = await saveCouponApi(fd);

            changeCopy(false);

            if (newCpnId) {
                changeCpnId(newCpnId);
            }

            if (type == 'save') {
                message.success('保存成功');
                if (params?.budgetId) {
                    let options = {
                        budgetId: params?.budgetId || '',
                        effTime: values.dateTime[0],
                        expTime: values.dateTime[1],
                        actId: newCpnId,
                    };
                    budgetRef?.current?.update(options);
                }
            } else if (type == 'send') {
                message.success('提交成功');
                goBack();
            }
        } catch (error) {
            console.log(9999, error);
            if (budgetRef?.current?.update) {
                const values = form.getFieldsValue();
                if (values?.budgetId) {
                    let options = {
                        budgetId: values.budgetId,
                        effTime: values.dateTime[0],
                        expTime: values.dateTime[1],
                        actId: cpnId || '',
                    };
                    budgetRef?.current?.update(options);
                }
            }
        } finally {
            changeSubmitLoading(false);
        }
    };
    const goBack = () => {
        history.replace('/marketing/couponCenter/coupon/list');
    };
    /**
     * 关闭运营商弹窗
     */
    const closeOperatorViewEvent = () => {
        toggleOperatorView(false);
    };
    // 选择运营商
    const submitOperatorEvent = (values) => {
        form.setFieldsValue({
            dctCondBuild: values,
        });
        closeOperatorViewEvent();
    };

    const changeCpnOwnerEvent = (event) => {
        const {
            target: { value },
        } = event;
        if (value == COUPON_TYPES.OTHER) {
            form.setFieldsValue({
                capitalType: CAPITALTYPES.CAPITAL,
            });
        }
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('图片格式错误!');
                rej();
            }
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                message.error('图片不大于 2MB!');
                rej();
            }

            const reader = new FileReader();
            reader.onload = function (e) {
                const data = e.target.result;
                // 加载图片获取图片真实宽度和高度
                const image = new Image();
                image.onload = function () {
                    const { width } = image;
                    const { height } = image;
                    const isAllow = width == 800 && height == 600;
                    if (!isAllow) {
                        message.error('封面图片尺寸必须为800*600');
                    }
                    if (isJpgOrPng && isLt2M && isAllow) {
                        res();
                    } else {
                        rej();
                    }
                };
                image.src = data;
            };
            reader.readAsDataURL(file);
        });

    const handleUploadChange = (info) => {
        filetoDataURL(info)
            .then((imgUrl) => {
                // form.setFieldsValue({
                //     filePath: info,
                // });
                changeFileInfo(info);
                changeImageUrl(imgUrl);
                changeUploading(false);
            })
            .catch((err) => {});
    };

    const uploadProps = {
        name: 'avatar',
        className: styles['avatar-uploader'],
        showUploadList: false,
        customRequest: ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            onSuccess(file);
            // const formData = new FormData();
            // if (data) {
            //     Object.keys(data).forEach(key => {
            //         formData.append(key, data[key]);
            //     });
            // }
            // formData.append(filename, file);

            // axios
            //     .post(action, formData, {
            //         withCredentials,
            //         headers,
            //         onUploadProgress: ({ total, loaded }) => {
            //             onProgress(
            //                 { percent: Number(Math.round((loaded / total) * 100).toFixed(2)) },
            //                 file,
            //             );
            //         },
            //     })
            //     .then(({ data: response }) => {
            //         // this.setState({ legalImgOssKey: response.data.imageId });
            //         onSuccess(response, file);
            //     })
            //     .catch(onError);

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType: 'picture-card',
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    initialValues={{
                        // capitalType: '03',
                        contributParty: '02',
                    }}
                    scrollToFirstError
                >
                    <div className={commonStyles['form-title']}>基础信息</div>
                    <FormItem label={operId ? '创建方' : '平台创建方'}>
                        {createOperator || ''}
                    </FormItem>
                    <FormItem
                        label={
                            <span>
                                券名称
                                <Tooltip title="在新电途卡包显示">
                                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                </Tooltip>
                            </span>
                        }
                        name="cpnName"
                        {...formItemLayout}
                        rules={[{ required: true, whitespace: true, message: '请填写券名称' }]}
                    >
                        <Input
                            disabled={isLock}
                            maxLength={20}
                            placeholder="请填写券名称"
                            autoComplete="off"
                        />
                    </FormItem>
                    <FormItem
                        label="券类型"
                        name="cpnOwner"
                        initialValue={COUPON_TYPES.ALI}
                        {...formItemLayout}
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group
                            disabled={createOperatorType == '01' || (cpnId && !isCopy) || isLock}
                            onChange={changeCpnOwnerEvent}
                        >
                            {cpnOwnerOptionsView}
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.cpnOwner !== curValues.cpnOwner
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const cpnOwner = getFieldValue('cpnOwner');
                            let radioOptions = null;

                            if (cpnOwner == COUPON_TYPES.OTHER) {
                                radioOptions = [
                                    <Radio
                                        key={CAPITALTYPES.PREFERENTIAL}
                                        value={CAPITALTYPES.PREFERENTIAL}
                                    >
                                        优惠券
                                    </Radio>,
                                ];
                            } else if (cpnOwner == COUPON_TYPES.ALI) {
                                radioOptions = [
                                    <Radio key={CAPITALTYPES.CAPITAL} value={CAPITALTYPES.CAPITAL}>
                                        资金券
                                    </Radio>,
                                    <Radio
                                        key={CAPITALTYPES.PREFERENTIAL}
                                        value={CAPITALTYPES.PREFERENTIAL}
                                    >
                                        优惠券
                                    </Radio>,
                                    <Radio key={CAPITALTYPES.DIR} value={CAPITALTYPES.DIR}>
                                        定向券
                                    </Radio>,
                                ];
                            } else if (
                                cpnOwner == COUPON_TYPES.XDT ||
                                cpnOwner == COUPON_TYPES.BUY
                            ) {
                                return;
                            }

                            return (
                                <FormItem
                                    label="资金类型"
                                    name="capitalType"
                                    initialValue={CAPITALTYPES.CAPITAL}
                                    {...formItemLayout}
                                    rules={[{ required: true, message: '请选择' }]}
                                >
                                    <Radio.Group disabled={createOperatorType == '01' || isLock}>
                                        {operId ? (
                                            <Radio value={CAPITALTYPES.DIR}>定向券</Radio>
                                        ) : (
                                            radioOptions
                                        )}
                                    </Radio.Group>
                                </FormItem>
                            );
                        }}
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.cpnOwner !== curValues.cpnOwner ||
                            prevValues.capitalType !== curValues.capitalType ||
                            prevValues.contributParty !== curValues.contributParty ||
                            prevValues.operatorContributRatio !== curValues.operatorContributRatio
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const cpnOwner = getFieldValue('cpnOwner');
                            const capitalType = getFieldValue('capitalType');
                            if (cpnOwner == COUPON_TYPES.XDT) {
                                const contributParty = getFieldValue('contributParty');
                                const operatorContributRatio =
                                    getFieldValue('operatorContributRatio');
                                return (
                                    <Fragment>
                                        <FormItem
                                            label="出资方"
                                            name="contributParty"
                                            {...formItemLayout}
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Radio.Group
                                                disabled={isLock}
                                                onChange={(event) => {
                                                    const {
                                                        target: { value },
                                                    } = event;
                                                    if (value === '01') {
                                                        form.setFieldsValue({
                                                            expenseBearing: [
                                                                BEARING_TYPES.BUSINESS,
                                                            ],
                                                        });
                                                    } else {
                                                        form.setFieldsValue({
                                                            expenseBearing: [],
                                                        });
                                                    }
                                                }}
                                            >
                                                <Radio value="01">运营商</Radio>
                                                <Radio value="02">平台</Radio>
                                                <Radio value="04">混合出资</Radio>
                                                <Radio value="05">大客户</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                        {(contributParty == '04' && (
                                            <FormItem
                                                label="运营商出资比例"
                                                {...formItemLayout}
                                                required
                                            >
                                                <FormItem
                                                    name="operatorContributRatio"
                                                    rules={[{ required: true, message: '请填写' }]}
                                                    noStyle
                                                    initialValue={undefined}
                                                >
                                                    <InputNumber
                                                        disabled={isLock}
                                                        min={1}
                                                        max={99}
                                                        placeholder="请填写"
                                                        precision={2}
                                                        step={0.01}
                                                    />
                                                </FormItem>
                                                %
                                            </FormItem>
                                        )) ||
                                            null}
                                        {(contributParty == '04' && (
                                            <FormItem
                                                label="平台出资比例"
                                                name="platformContributRatio"
                                                {...formItemLayout}
                                                required
                                            >
                                                {!operatorContributRatio ||
                                                isNaN(operatorContributRatio)
                                                    ? '0.00'
                                                    : (
                                                          100.0 - parseFloat(operatorContributRatio)
                                                      ).toFixed(2)}
                                                %
                                            </FormItem>
                                        )) ||
                                            null}
                                        {(contributParty == '05' && (
                                            <FormItem
                                                label="选择大客户"
                                                name="enterpriseId"
                                                {...formItemLayout}
                                                rules={[{ required: true, message: '请选择' }]}
                                            >
                                                <TreeSelect
                                                    disabled={isLock}
                                                    showSearch
                                                    style={{ width: '100%' }}
                                                    dropdownStyle={{
                                                        maxHeight: 400,
                                                        overflow: 'auto',
                                                    }}
                                                    placeholder="请选择"
                                                    allowClear
                                                    treeDefaultExpandAll
                                                >
                                                    {enterpriseTreeData}
                                                </TreeSelect>
                                            </FormItem>
                                        )) ||
                                            null}
                                        {/* <FormItem
                                            {...{
                                                labelCol: {
                                                    span: 6,
                                                },
                                                wrapperCol: {
                                                    span: 18,
                                                },
                                            }}
                                            name="cpnChannel"
                                            label="投放渠道"
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Checkbox.Group
                                                disabled={isLock}
                                                options={channelList}
                                            />
                                        </FormItem> */}
                                        <CheckBoxGroup
                                            label="投放渠道"
                                            name={'cpnChannel'}
                                            form={form}
                                            selectList={channelOptions}
                                            disabled={isLock}
                                            required
                                            wrapperCol={{ span: 8 }}
                                            rules={[{ required: true, message: '请选择投放渠道' }]}
                                            valueType="select"
                                        ></CheckBoxGroup>
                                        <FormItem
                                            {...{
                                                labelCol: {
                                                    flex: '0 0 130px',
                                                },
                                                wrapperCol: {
                                                    span: 18,
                                                },
                                            }}
                                            name="cpnDeduction"
                                            label="抵扣费用"
                                            initialValue={['01', '02']}
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Checkbox.Group
                                                disabled={isLock}
                                                options={[
                                                    {
                                                        label: '电费',
                                                        value: '01',
                                                    },
                                                    {
                                                        label: '服务费',
                                                        value: '02',
                                                    },
                                                    // {
                                                    //     label: '附加费',
                                                    //     value: '03',
                                                    // },
                                                ]}
                                            />
                                        </FormItem>

                                        {/* <OperAndStationListByCity
                                            form={form}
                                            ref={operSelectList}
                                            disabled={isLock}
                                            currentUser={currentUser}
                                            formItemLayout={formItemLayout}
                                            stationFormItemLayout={{
                                                wrapperCol: {
                                                    span: 18,
                                                },
                                            }}
                                        /> */}

                                        <FormItem
                                            label="活动范围"
                                            name="areaRangeType"
                                            {...formItemLayout}
                                            initialValue={RANGE_TYPES.ALL}
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Radio.Group disabled={isLock}>
                                                <Radio value={RANGE_TYPES.ALL}>全部</Radio>
                                                <Radio value={RANGE_TYPES.OTHER}>部分</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                        <FormItem
                                            shouldUpdate={(prevValues, curValues) => {
                                                return (
                                                    prevValues['areaRangeType'] !==
                                                    curValues['areaRangeType']
                                                );
                                            }}
                                            noStyle
                                        >
                                            {({ getFieldValue }) => {
                                                const areaRangeType =
                                                    getFieldValue('areaRangeType');
                                                return areaRangeType == RANGE_TYPES.OTHER ? (
                                                    <FormItem
                                                        label="活动范围"
                                                        name={'stationConfig'}
                                                        wrapperCol={{ span: 24 }}
                                                        required
                                                        rules={[
                                                            ({ getFieldValue }) => ({
                                                                validator(rule, value) {
                                                                    if (isEmpty(value)) {
                                                                        return Promise.reject(
                                                                            '请配置指定场站',
                                                                        );
                                                                    }
                                                                    const {
                                                                        addStations,
                                                                        allStations,
                                                                    } = value;
                                                                    if (
                                                                        isEmpty(allStations) &&
                                                                        isEmpty(addStations)
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '请配置指定场站',
                                                                        );
                                                                    }
                                                                    if (
                                                                        addStations.length >
                                                                            20000 ||
                                                                        allStations.length > 20000
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '一次性最多配置2万个站点',
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <SearchStationItem
                                                            title="活动范围"
                                                            disabled={isLock}
                                                            required
                                                            currentUser={currentUser}
                                                            hasStastics
                                                            requestInfo={
                                                                cpnId &&
                                                                !isEmpty(conponInfo) && {
                                                                    listApi: getStationScopeListApi,
                                                                    params: {
                                                                        scopeRelateId: cpnId,
                                                                        scopeRelateType:
                                                                            STATION_CONFIG_PAGE_TYPES.COUPON_1,
                                                                    },
                                                                    recordParams: {
                                                                        relateId: cpnId,
                                                                        scene: 'stc_coupon',
                                                                    },
                                                                }
                                                            }
                                                            isCopy={isCopy}
                                                        />
                                                    </FormItem>
                                                ) : null;
                                            }}
                                        </FormItem>
                                    </Fragment>
                                );
                            }
                            if (cpnOwner == COUPON_TYPES.BUY) {
                                const contributParty = getFieldValue('contributParty');
                                return (
                                    <Fragment>
                                        <FormItem
                                            label="出资方"
                                            name="contributParty"
                                            {...formItemLayout}
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Radio.Group disabled={isLock}>
                                                <Radio value="02">平台</Radio>
                                                <Radio value="03">第三方</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                        {(contributParty == 3 && (
                                            <FormItem
                                                label="第三方名称"
                                                name="thirdPartName"
                                                {...formItemLayout}
                                                rules={[{ required: true, message: '请填写' }]}
                                            >
                                                <Input
                                                    maxLength={32}
                                                    placeholder="请输入"
                                                    disabled={isLock}
                                                    autoComplete="off"
                                                />
                                            </FormItem>
                                        )) ||
                                            null}
                                    </Fragment>
                                );
                            }
                            let operItem = null;
                            if (!operId && capitalType == CAPITALTYPES.DIR) {
                                operItem = (
                                    <Fragment>
                                        <FormItem
                                            label={
                                                <span>
                                                    可核销运营商
                                                    <Tooltip title="创券后新增的运营商无法添加到可核销运营商中">
                                                        <InfoCircleOutlined
                                                            style={{ marginLeft: '6px' }}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            }
                                            {...formItemLayout}
                                            shouldUpdate={(prevValues, curValues) =>
                                                prevValues.dctCondBuild !== curValues.dctCondBuild
                                            }
                                            validateStatus={buildStatus}
                                            help={buildStatus === 'error' ? '请选择运营商' : ''}
                                            required
                                        >
                                            {() => {
                                                const dctCondBuilds = getFieldValue('dctCondBuild');
                                                if (dctCondBuilds) {
                                                    const builds = dctCondBuilds.split(',');
                                                    return (
                                                        <Fragment>
                                                            <Button
                                                                onClick={() => {
                                                                    toggleOperatorView(true);
                                                                }}
                                                            >
                                                                {isLock ? '查看' : '选择'}
                                                            </Button>
                                                            <div>
                                                                已选择
                                                                {builds.length}
                                                                个运营商
                                                            </div>
                                                        </Fragment>
                                                    );
                                                }
                                                return (
                                                    <Fragment>
                                                        <Button
                                                            onClick={() => {
                                                                toggleOperatorView(true);
                                                            }}
                                                        >
                                                            {isLock ? '查看' : '选择'}
                                                        </Button>
                                                        <div>已选择 0 个运营商</div>
                                                    </Fragment>
                                                );
                                            }}
                                        </FormItem>
                                        <FormItem
                                            label="出资方"
                                            name="contributParty"
                                            {...formItemLayout}
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Radio.Group
                                                disabled={isLock}
                                                onChange={(event) => {
                                                    const {
                                                        target: { value },
                                                    } = event;
                                                    if (value === '01') {
                                                        form.setFieldsValue({
                                                            expenseBearing: [
                                                                BEARING_TYPES.BUSINESS,
                                                            ],
                                                        });
                                                    } else {
                                                        form.setFieldsValue({
                                                            expenseBearing: [],
                                                        });
                                                    }
                                                }}
                                            >
                                                <Radio value="01">运营商</Radio>
                                                <Radio value="02">平台</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                    </Fragment>
                                );
                            }
                            let templateIdItem = null;
                            let chargeConponItem = null;
                            if (cpnOwner == COUPON_TYPES.OTHER) {
                                chargeConponItem = (
                                    <FormItem
                                        label={
                                            <span>
                                                是否充电券
                                                <Tooltip title="充电券发放后会显示在用户平台优惠券列表中，非充电券则显示在平台其他券列表中">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        name="chargeCouponFlag"
                                        {...formItemLayout}
                                        rules={[{ required: true, message: '请选择' }]}
                                        initialValue="0"
                                    >
                                        <Radio.Group disabled={isLock}>
                                            <Radio value={'0'}>否</Radio>
                                            <Radio value={'1'}>是</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                );
                                templateIdItem = (
                                    <FormItem
                                        label="邦道模板ID"
                                        name="templateId"
                                        {...formItemLayout}
                                        rules={[{ required: true, message: '请填写' }]}
                                    >
                                        <Input
                                            maxLength={32}
                                            placeholder="请输入"
                                            disabled={isLock}
                                            autoComplete="off"
                                        />
                                    </FormItem>
                                );
                            }
                            let fundAccountItem = null;
                            if (capitalType == CAPITALTYPES.CAPITAL) {
                                fundAccountItem = (
                                    <FormItem
                                        name="fundAccount"
                                        label="出资人账户"
                                        {...formItemLayout}
                                        rules={[{ required: true, message: '请填写' }]}
                                    >
                                        <Input
                                            disabled={isLock}
                                            placeholder="请填写"
                                            autoComplete="off"
                                        />
                                    </FormItem>
                                );
                            }
                            return (
                                <Fragment>
                                    {chargeConponItem}
                                    {operItem}
                                    {templateIdItem}
                                    <FormItem
                                        name="brandName"
                                        label={
                                            <span>
                                                显示品牌名称
                                                <Tooltip title="在新电途卡包显示">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        {...formItemLayout}
                                        rules={[
                                            () => ({
                                                validator(rule, value) {
                                                    if (
                                                        capitalType === CAPITALTYPES.DIR &&
                                                        !value
                                                    ) {
                                                        return Promise.reject('请填写');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <Input
                                            maxLength={12}
                                            disabled={isLock}
                                            placeholder="请填写"
                                            autoComplete="off"
                                        />
                                    </FormItem>

                                    {fundAccountItem}
                                </Fragment>
                            );
                        }}
                    </FormItem>
                    <FormItem label="创建数量" {...formItemLayout} required>
                        <FormItem
                            name="cpnNum"
                            rules={[{ required: true, message: '请填写' }]}
                            noStyle
                        >
                            <InputNumber
                                disabled={isLock}
                                formatter={(num) => Math.round(num)}
                                min={0}
                                max={999999}
                                placeholder="请填写"
                            />
                        </FormItem>
                        <span> 张 </span>
                    </FormItem>
                    <FormItem
                        name="dateTime"
                        label="发放有效期:"
                        {...formItemLayout}
                        rules={[
                            { required: true, message: '请选择发放有效期' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择发放开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择发放失效日期');
                                    }
                                    if (value[1]) {
                                        const nowTime = +new Date();
                                        const sendEndTime = +new Date(value[1]);

                                        if (sendEndTime < nowTime) {
                                            return Promise.reject('发放失效日期不能早于当前时间');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        // validateStatus={timeStatus}
                        // help={timeStatus === 'error' && '请填写生效时间'}
                    >
                        <RangePicker
                            disabled={isLock}
                            disabledTime={disabledRangeTime}
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: [
                                    moment('00:00:00', 'HH:mm:ss'),
                                    moment('23:59:59', 'HH:mm:ss'),
                                ],
                            }}
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </FormItem>
                    <FormItem
                        name="cpnAmt"
                        label="券面额"
                        {...formItemLayout}
                        rules={[
                            { required: true, message: '请填写' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('');
                                    }
                                    if (!/^\d*\.{0,1}\d{0,2}$/.test(value)) {
                                        return Promise.reject('券面额保留两位小数');
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            disabled={isLock}
                            precision={2}
                            step={0.01}
                            min={0}
                            max={999.99}
                            placeholder="请填写"
                        />
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.cpnOwner !== curValues.cpnOwner
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const cpnOwner = getFieldValue('cpnOwner');
                            if (cpnOwner == COUPON_TYPES.XDT) {
                                return (
                                    <>
                                        <FormItem label="日核销限制" {...formItemLayout} required>
                                            <Space>
                                                <FormItem
                                                    name="cpnVerificationTimes"
                                                    noStyle
                                                    initialValue={0}
                                                    rules={[
                                                        (_) => ({
                                                            validator(rule, value) {
                                                                if (isEmpty(value)) {
                                                                    return Promise.reject('');
                                                                }
                                                                if (Number(value) < 0) {
                                                                    return Promise.reject('请填写');
                                                                }

                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <InputNumber
                                                        disabled={isLock}
                                                        min={0}
                                                        max={999}
                                                        precision={0}
                                                        step={1}
                                                        placeholder="请填写"
                                                    />
                                                </FormItem>
                                                次 (0代表无限制)
                                            </Space>
                                        </FormItem>
                                        <FormItem
                                            label="会员专用"
                                            name="vipCouponFlag"
                                            rules={[{ required: true, message: '请选择' }]}
                                        >
                                            <Radio.Group disabled={isLock}>
                                                <Radio value="0">否</Radio>
                                                <Radio value="1">是</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                    </>
                                );
                            }
                            return null;
                        }}
                    </FormItem>
                    <FormItem label="最低消费金额" {...formItemLayout} required>
                        <span> 满 </span>
                        <FormItem
                            name="dctCondValue"
                            rules={[
                                { required: true, message: '请填写' },
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('');
                                        }
                                        if (!/^\d*\.{0,1}\d{0,2}$/.test(value)) {
                                            return Promise.reject('金额保留两位小数');
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            noStyle
                        >
                            <InputNumber
                                disabled={isLock}
                                min={0.01}
                                max={9999}
                                step={0.01}
                                placeholder="请输入"
                            />
                        </FormItem>
                        <span> 可用 </span>
                    </FormItem>
                    <FormItem
                        name="cpnTimeType"
                        label="有效期"
                        {...formItemLayout}
                        rules={[{ required: true, message: '请选择有效期' }]}
                    >
                        <Radio.Group>
                            <Fragment>
                                <Radio disabled={isLock} value="2" style={{ marginBottom: '5px' }}>
                                    <span> 领用后 </span>
                                    <FormItem
                                        noStyle
                                        shouldUpdate={(prevValues, curValues) =>
                                            prevValues.cpnTimeType !== curValues.cpnTimeType ||
                                            prevValues.cpnOwner !== curValues.cpnOwner
                                        }
                                    >
                                        {({ getFieldValue }) => {
                                            const cpnTimeType = getFieldValue('cpnTimeType');
                                            const cpnOwner = getFieldValue('cpnOwner');

                                            return (
                                                <Space>
                                                    {cpnOwner === COUPON_TYPES.XDT ? (
                                                        <Fragment>
                                                            <FormItem
                                                                name="bgnTimeDuration"
                                                                initialValue={0}
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (
                                                                                cpnTimeType == '2'
                                                                            ) {
                                                                                if (
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        '请填写有效天数',
                                                                                    );
                                                                                }
                                                                                if (value < 0) {
                                                                                    return Promise.reject(
                                                                                        '有效天数必须大于等于0',
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                                noStyle
                                                            >
                                                                <InputNumber
                                                                    min={0}
                                                                    max={99999}
                                                                    step={1}
                                                                    formatter={(num) =>
                                                                        Math.round(num)
                                                                    }
                                                                    placeholder="请输入"
                                                                    disabled={
                                                                        isLock || cpnTimeType != '2'
                                                                    }
                                                                />
                                                            </FormItem>
                                                            <span> 天后生效 </span>
                                                        </Fragment>
                                                    ) : null}

                                                    <FormItem
                                                        name="timeDuration"
                                                        initialValue={1}
                                                        rules={[
                                                            (_) => ({
                                                                validator(rule, value) {
                                                                    const bgnTimeDuration =
                                                                        getFieldValue(
                                                                            'bgnTimeDuration',
                                                                        );
                                                                    if (cpnTimeType == '2') {
                                                                        if (isEmpty(value)) {
                                                                            return Promise.reject(
                                                                                '请填写有效天数',
                                                                            );
                                                                        }
                                                                        if (
                                                                            Number(
                                                                                bgnTimeDuration,
                                                                            ) >= Number(value)
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '生效天数不能大于等于失效天数',
                                                                            );
                                                                        }
                                                                        if (value <= 0) {
                                                                            return Promise.reject(
                                                                                '有效天数必须大于0',
                                                                            );
                                                                        }
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                        noStyle
                                                    >
                                                        <InputNumber
                                                            min={1}
                                                            max={99999}
                                                            step={1}
                                                            formatter={(num) => Math.round(num)}
                                                            placeholder="请输入"
                                                            disabled={isLock || cpnTimeType != '2'}
                                                        />
                                                    </FormItem>
                                                    <span> 天后失效 </span>
                                                </Space>
                                            );
                                        }}
                                    </FormItem>
                                </Radio>
                                <Radio disabled={isLock} value="1">
                                    <FormItem
                                        noStyle
                                        shouldUpdate={(prevValues, curValues) =>
                                            prevValues.cpnTimeType !== curValues.cpnTimeType
                                        }
                                    >
                                        {({ getFieldValue }) => {
                                            const cpnTimeType = getFieldValue('cpnTimeType');
                                            return (
                                                <FormItem
                                                    noStyle
                                                    name="cpnTime"
                                                    rules={[
                                                        (_) => ({
                                                            validator(rule, value) {
                                                                if (cpnTimeType == '1') {
                                                                    if (!value[0]) {
                                                                        return Promise.reject(
                                                                            '请选择有效开始日期',
                                                                        );
                                                                    }
                                                                    if (!value[1]) {
                                                                        return Promise.reject(
                                                                            '请选择有效失效日期',
                                                                        );
                                                                    }
                                                                    if (value[1]) {
                                                                        const nowTime = +new Date();
                                                                        const sendEndTime =
                                                                            +new Date(value[1]);

                                                                        if (sendEndTime < nowTime) {
                                                                            return Promise.reject(
                                                                                '失效日期不能早于当前时间',
                                                                            );
                                                                        }
                                                                    }
                                                                }

                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <RangePicker
                                                        disabledTime={disabledRangeTime}
                                                        showTime={{
                                                            format: 'HH:mm:ss',
                                                            defaultValue: [
                                                                moment('00:00:00', 'HH:mm:ss'),
                                                                moment('23:59:59', 'HH:mm:ss'),
                                                            ],
                                                        }}
                                                        disabled={isLock || cpnTimeType != '1'}
                                                        format="YYYY-MM-DD HH:mm:ss"
                                                    />
                                                </FormItem>
                                            );
                                        }}
                                    </FormItem>
                                </Radio>
                            </Fragment>
                        </Radio.Group>
                    </FormItem>
                    <DateLimit
                        dateName={'dateList'}
                        form={form}
                        formItemLayout={{ wrapperCol: { span: 14 } }}
                        disabled={isLock}
                    />
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.cpnOwner !== curValues.cpnOwner
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const cpnOwner = getFieldValue('cpnOwner');
                            let rulesOptions = {};
                            if (cpnOwner !== COUPON_TYPES.OTHER) {
                                rulesOptions = {
                                    rules: [
                                        ({ _ }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue('useMarks');
                                                if (!list || list.length == 0) {
                                                    return Promise.reject('请输入规则!');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ],
                                    required: true,
                                };
                            }
                            return (
                                <Form.Item
                                    name="useMarks"
                                    {...formItemLayout}
                                    label="使用规则"
                                    {...rulesOptions}
                                >
                                    <Form.List name="useMarks">
                                        {(fields, { add, remove }) => (
                                            <Fragment>
                                                {fields.map((field, index) => (
                                                    <Row key={index}>
                                                        <Col flex="1">
                                                            <Form.Item
                                                                {...field}
                                                                validateTrigger={[
                                                                    'onChange',
                                                                    'onBlur',
                                                                ]}
                                                                key={field.key}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        whitespace: true,
                                                                        message: '请输入规则',
                                                                    },
                                                                    ({ _ }) => ({
                                                                        validator(rule, value) {
                                                                            if (
                                                                                value &&
                                                                                value.length > 50
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    '单条不得超过50个字符!',
                                                                                );
                                                                            }
                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <Input
                                                                    disabled={isLock}
                                                                    placeholder={`规则${index + 1}`}
                                                                    autoComplete="off"
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                        <Col>
                                                            <MinusCircleOutlined
                                                                className={
                                                                    styles['dynamic-delete-button']
                                                                }
                                                                style={{ margin: '0 8px' }}
                                                                onClick={() => {
                                                                    remove(field.name);
                                                                }}
                                                            />
                                                        </Col>
                                                    </Row>
                                                ))}
                                                {fields.length < 10 ? (
                                                    <Form.Item label="">
                                                        <Button
                                                            disabled={isLock}
                                                            type="dashed"
                                                            onClick={() => {
                                                                add();
                                                            }}
                                                            style={{ width: '60%' }}
                                                        >
                                                            <PlusOutlined />
                                                            添加规则
                                                        </Button>
                                                    </Form.Item>
                                                ) : null}
                                            </Fragment>
                                        )}
                                    </Form.List>
                                </Form.Item>
                            );
                        }}
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.capitalType !== curValues.capitalType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const capitalType = getFieldValue('capitalType');
                            if (capitalType == CAPITALTYPES.DIR) {
                                return (
                                    <FormItem
                                        name=""
                                        {...formItemLayout}
                                        label="单品券封面图片"
                                        // required
                                        // rules={[
                                        //     () => ({
                                        //         validator(rule, value) {
                                        //             const capitalTypeValue = getFieldValue(
                                        //                 'capitalType',
                                        //             );
                                        //             if (capitalTypeValue == CAPITALTYPES.DIR) {
                                        //                 if (!imageUrl) {
                                        //                     return Promise.reject('请上传封面');
                                        //                 }
                                        //             }

                                        //             return Promise.resolve();
                                        //         },
                                        //     }),
                                        // ]}
                                    >
                                        <FormItem noStyle>
                                            <Upload {...uploadProps} disabled={isLock}>
                                                <img
                                                    src={imageUrl || couponImg}
                                                    alt="avatar"
                                                    style={{ width: '100%' }}
                                                />

                                                {/* {imageUrl ? (
                                                    <img
                                                        src={imageUrl}
                                                        alt="avatar"
                                                        style={{ width: '100%' }}
                                                    />
                                                ) : (
                                                    <UploadButton uploading={uploading} />
                                                )} */}
                                            </Upload>
                                        </FormItem>
                                        <span className={styles['mark-text']}>
                                            格式支持png、jpg、jpeg,尺寸为800*600，大小不得超过2M
                                        </span>
                                    </FormItem>
                                );
                            }
                        }}
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.dateTime !== curValues.dateTime ||
                            prevValues.contributParty !== curValues.contributParty
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const dateTime = getFieldValue('dateTime');
                            const contributParty = getFieldValue('contributParty');
                            return (
                                <BudgetFormItem
                                    form={form}
                                    ref={budgetRef}
                                    effTime={dateTime && dateTime[0]}
                                    expTime={dateTime && dateTime[1]}
                                    belongType={contributParty === '01' ? 'oper' : ''}
                                    disabled={isLock}
                                    currentUser={currentUser}
                                    formItemLayout={{
                                        labelCol: {
                                            flex: '0 0 130px',
                                        },
                                        wrapperCol: {
                                            span: 12,
                                        },
                                    }}
                                ></BudgetFormItem>
                            );
                        }}
                    </FormItem>
                    <div className={commonStyles['form-submit']}>
                        {isLock ? (
                            <Fragment>
                                {/* <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    onClick={() => {
                                        changeLock(false);
                                    }}
                                >
                                    编辑
                                </Button> */}
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        ) : (
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn-left']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('save');
                                    }}
                                >
                                    保存
                                </Button>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('send');
                                    }}
                                >
                                    提交
                                </Button>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    取消
                                </Button>
                            </Fragment>
                        )}
                    </div>
                </Form>

                <OperatorModelLayout
                    show={showOperatorView}
                    {...props}
                    isLock={isLock}
                    selectOperators={initSelectOperators}
                    onConfirm={submitOperatorEvent}
                    onCancel={closeOperatorViewEvent}
                />
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, couponManageModel, loading }) => ({
    global,
    user,
    couponManageModel,
    detalisLoading: loading.effects['couponManageModel/getCouponInfo'],
    operatorLoading: loading.effects['couponManageModel/getOperationList'],
    // submitLoading: loading.effects['couponManageModel/getCouponMangeList'],
}))(CouponUpdatePage);
