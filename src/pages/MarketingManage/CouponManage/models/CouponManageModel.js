import {
    getCouponPutListApi,
    getCouponAdbPutListApi,
    getCouponUseListApi,
    getCouponAdbUseListApi,
    getCouponDetailApi,
    getCouponPutUseListApi,
    getCouponPutUseListApi2,
    getCouponPutUseInfoApi,
    getCouponPutUseInfoApi2,
} from '@/services/Marketing/MarketingCouponApi';
import { qryXdtOperatorInfosApi } from '@/services/OperationMng/OperationMngApi.js';

const couponManageModel = {
    namespace: 'couponManageModel',
    state: {
        conponInfo: null,
        operationList: [], // 运营商管理列表
        operationListTotal: 0, // 运营商管理列表记录数
        couponPutList: [], // 发放列表
        couponPutListTotal: 0, // 发放表数量
        couponUseList: [], // 发放列表
        couponUseListTotal: 0, // 发放表数量

        couponPutUseList: [], // 发放核销列表
        couponPutUseListTotal: 0, // 发放核销数量
        couponPutUseInfo: null,
    },
    effects: {
        *getCouponPutUseList({ options }, { call, put, select }) {
            try {
                let api = getCouponPutUseListApi;
                if (options.cpnType == '02') {
                    api = getCouponPutUseListApi2;
                }

                const {
                    data: { list = [], total = 0, records = [] },
                } = yield call(api, options);
                yield put({
                    type: 'updateCouponPutUseList',
                    couponPutUseList: (list?.length && list) || records,
                    total,
                });
            } catch (error) {}
        },
        *getCouponPutUseInfo({ options }, { call, put, select }) {
            try {
                let api = getCouponPutUseInfoApi;
                if (options.cpnType == '02') {
                    api = getCouponPutUseInfoApi2;
                }

                const { data = {} } = yield call(api, options);
                yield put({
                    type: 'updateCouponPutUseInfo',
                    info: data,
                });
            } catch (error) {}
        },
        *getCouponPutList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list = [], total = 0 },
                } = yield call(getCouponPutListApi, options);
                yield put({
                    type: 'updateCouponPutList',
                    couponPutList: list,
                    total,
                });
            } catch (error) {}
        },
        // 数据治理改造后调用的接口
        *getCouponAdbPutList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list = [], total = 0 },
                } = yield call(getCouponAdbPutListApi, options);
                yield put({
                    type: 'updateCouponPutList',
                    couponPutList: list,
                    total,
                });
            } catch (error) {}
        },
        *getCouponUseList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list = [], total = 0 },
                } = yield call(getCouponUseListApi, options);
                yield put({
                    type: 'updateCouponUseList',
                    couponUseList: list,
                    total,
                });
            } catch (error) {}
        },
        // 数据治理改造后调用的接口
        *getCouponAdbUseList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list = [], total = 0 },
                } = yield call(getCouponAdbUseListApi, options);
                yield put({
                    type: 'updateCouponUseList',
                    couponUseList: list,
                    total,
                });
            } catch (error) {}
        },
        *getCouponInfo({ options }, { call, put, select }) {
            try {
                const { data = {} } = yield call(getCouponDetailApi, options);
                yield put({
                    type: 'updateCouponInfo',
                    info: data,
                });
            } catch (error) {}
        },
        *getOperationList({ payload }, { call, put }) {
            try {
                const response = yield call(qryXdtOperatorInfosApi, payload);
                yield put({
                    type: 'updateOperationList',
                    payload: {
                        operationList: response.data.records,
                        operationListTotal: response.data.total,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
    },
    reducers: {
        updateCouponPutUseList(state, { couponPutUseList, total }) {
            return {
                ...state,
                couponPutUseList,
                couponPutUseListTotal: total,
            };
        },
        updateCouponPutUseInfo(state, { info }) {
            return {
                ...state,
                couponPutUseInfo: info,
            };
        },
        updateCouponPutList(state, { couponPutList, total }) {
            return {
                ...state,
                couponPutList,
                couponPutListTotal: total,
            };
        },
        updateCouponUseList(state, { couponUseList, total }) {
            return {
                ...state,
                couponUseList,
                couponUseListTotal: total,
            };
        },
        updateCouponInfo(state, { info }) {
            return {
                ...state,
                conponInfo: info,
            };
        },
        updateOperationList(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
    },
};
export default couponManageModel;
