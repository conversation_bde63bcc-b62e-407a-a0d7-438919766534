import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
    Space,
    message,
    Cascader,
} from 'antd';
import { connect, Link } from 'umi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { exportTableByParams, formatActivePage } from '@/utils/utils';
import moment from 'moment';
import usePageState from '@/hooks/usePageState.js';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import LinkActFormItem from './components/LinkActFormItem';
import AllStationSelect from '@/components/AllStationSelect';
import CacheAreaView from '@/components/CacheAreaView';

import {
    getCouponPutUseListApiPath,
    getCouponPutUseListApiPath2,
} from '@/services/Marketing/MarketingCouponApi';
import { getCityListApi } from '@/services/CommonApi';
import CouponSelectItem from '@/components/CouponSelectItem';
import { BASE_URL, MNG_ALY } from '@/config/global';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// const STATUS_TYPES = {
//     ALL: '00', // 全部
//     COST: '01', // 消费
//     BACK: '02', // 退回
// };

const STATUS_TYPES = {
    ALL: '00', // 全部
    NOUSE: '01', // 未核销
    USE: '03', // 已核销
    OVER: '04', // 已过期
    DELETE: '05', //已作废
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const {
        global,
        dispatch,
        location,
        listLoading,
        form,
        onSubmit,
        onReset,
        onExportForm,
        disabledContidion = [],
        searchCondition,
    } = props;
    const {
        query: { cpnNo, creTime },
    } = location;

    const { codeInfo } = global;
    const { contributParty: contributPartyList } = codeInfo;

    const [cityTreeData, changeCityTreeData] = useState([]);

    const stationRef = useRef();

    useEffect(() => {
        if (!contributPartyList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'contributParty',
            });
        }
        initCityTree();
    }, []);

    const onFinish = (values) => {
        const { putdates = [] } = values;
        const startDate = putdates[0];
        const endDate = putdates[1];
        if (
            60 < moment(endDate).diff(moment(startDate), 'day') &&
            moment(endDate).diff(moment(startDate), 'day') < 185
        ) {
            confirm({
                title: '查询提示',
                content: '查询时间超过60天，会导致查询压力较大，请点击下方导出到本地查看',
                okText: '导出至暂存区',
                onOk() {
                    onExportForm();
                },
            });
        } else {
            onSubmit(values);
        }
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const formatCityItem = (data) => {
        const list = [];
        for (const item of data) {
            list.push({
                key: item.areaCode,
                label: item.areaName,
                value: item.areaCode,
            });
        }
        return list;
    };

    const initCityTree = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = [];
            for (const item of areaList) {
                list.push({
                    key: item.areaCode,
                    label: item.areaName,
                    value: item.areaCode,
                    children: item.cityList ? formatCityItem(item.cityList) : [],
                });
            }
            changeCityTreeData(list);
            return;
        } catch (error) {
            console.log(88888, error);
            return Promise.reject(error);
        }
    };

    const contributPartyOptions = useMemo(() => {
        if (contributPartyList) {
            return contributPartyList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [contributPartyList]);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                ...(searchCondition || {}),
                putdates: [creTime ? moment(creTime) : moment().subtract(1, 'weeks'), moment()],
                cpnNo,
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onExportForm={onExportForm}
                onReset={resetForm}
                open={!!creTime}
                minSpan={40}
                exportName="导出至暂存区"
            >
                <Col span={8}>
                    <FormItem
                        label="券版本"
                        name="cpnType"
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'01'}
                    >
                        <Select
                            placeholder="请选择"
                            onChange={() => {
                                // 避免切换类型后不点查询，直接点分页，因为要调不同接口，页码需要受控，所以切换的同时直接调查询，重置页码
                                onSubmit();
                            }}
                        >
                            <Option value="01">优惠券1.0</Option>
                            <Option value="02">优惠券2.0</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem
                        label="发放日期:"
                        name="putdates"
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择结束日期');
                                    }
                                    if (value[0] && value[1]) {
                                        const startTime = +new Date(value[0]);
                                        const endTime = +new Date(value[1]);
                                        const dest = 185 * 1000 * 60 * 24 * 60;

                                        if (Math.abs(startTime - endTime) > dest) {
                                            return Promise.reject('选取范围最大不超过185天');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="用户手机号:" name="mobile">
                        <Input
                            placeholder="请填写"
                            autoComplete="off"
                            disabled={disabledContidion?.indexOf('mobile') >= 0}
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <LinkActFormItem form={form} name="actId" />
                </Col>
                <Col span={8}>
                    <FormItem label="核销日期:" name="usedates">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="券名称" name="cpnName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券组编号:" name="cpnNo">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单号:" name="orderNo">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="支付宝交易号:" name="reconciliationNo">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem form={form} />
                </Col>
                <Col span={8}>
                    <AllStationSelect form={form} ref={stationRef} label="场站名称" />
                </Col>

                <Col span={8}>
                    <FormItem label="城市:" name="city">
                        <Cascader options={cityTreeData} placeholder="请选择" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="出资方:" name="contributParty">
                        <Select placeholder="请选择" allowClear>
                            <Option value="01">运营商</Option>
                            <Option value="02">平台</Option>
                            <Option value="04">混合出资</Option>
                            <Option value="05">大客户</Option>
                        </Select>
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="支付宝模板ID:" name="alipayTemplateId">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="有效期" name="expdates">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                {/* <Col span={8}>
                    <FormItem label="邦道模板ID:" name="templateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col> */}
            </SearchOptionsBar>
        </Form>
    );
};

export const CouponManageListLayout = (props) => {
    const {
        dispatch,
        history,
        couponManageModel: { couponPutUseList, couponPutUseListTotal },

        listLoading,
        searchCondition,
        disabledContidion,
        cacheRef,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(
        () => () => {
            dispatch({
                type: 'couponManageModel/updateCouponPutUseList',
                couponPutUseList: [],
                total: 0,
            });
        },
        [],
    );

    useEffect(() => {
        if (searchCondition) {
            const currentConditions = form.getFieldsValue();
            let isEqual = true;
            Object.keys(searchCondition).map((ele) => {
                if (searchCondition?.[ele]?.length && !currentConditions?.[ele]) {
                    // 新增
                    isEqual = false;
                } else if (
                    currentConditions?.[ele] &&
                    currentConditions?.[ele] != searchCondition[ele]
                ) {
                    // 删除
                    isEqual = false;
                }
            });
            if (!isEqual) {
                form.setFieldsValue({ ...searchCondition });
                if (pageInfo.pageIndex == 1) {
                    searchData();
                } else {
                    changePageInfo({ pageIndex: 1 });
                }
            }
        }
    }, [searchCondition]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = (isDownload = false) => {
        const data = form.getFieldsValue();
        const params = {
            cpnType: data.cpnType,
            pageIndex: isDownload ? undefined : pageInfo.pageIndex,
            pageSize: isDownload ? undefined : pageInfo.pageSize,
            issueBeginDate: (data.putdates && data.putdates[0].format('YYYY-MM-DD')) || '',
            issueEndDate: (data.putdates && data.putdates[1].format('YYYY-MM-DD')) || '',
            mobile: data.mobile || '',
            actId: data.actId || '',
            thirdRelateId: data.thirdRelateId || '',
            cpnNo: data.cpnNo || '',
            orderNo: data.orderNo,
            reconciliationNo: data.reconciliationNo,
            operId: data.operId,
            stationId: data.stationId,

            contributParty: data.contributParty || '',
            alipayTemplateId: data.alipayTemplateId || '',

            usedBeginDate: (data.usedates && data.usedates[0].format('YYYY-MM-DD')) || '',
            usedEndDate: (data.usedates && data.usedates[1].format('YYYY-MM-DD')) || '',

            effDate: data.expdates?.[0].format('YYYY-MM-DD') || '',
            expDate: data.expdates?.[1].format('YYYY-MM-DD') || '',
            cpnName: data.cpnName,

            city: (data.city && data.city[data.city.length - 1]) || '',
        };

        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.useStatus = pageInfo.tabType;
        }

        if (isDownload) {
            let api_url = MNG_ALY;
            // let api = getCouponPutUseListApiPath;
            // if (params.cpnType == '02') {
            //     api = getCouponPutUseListApiPath2;
            // }
            // const columnsStrs = [];
            // for (const item of columns) {
            //     if (item.dataIndex) {
            //         columnsStrs.push({
            //             key: item.dataIndex,
            //             value: item.title,
            //         });
            //     }
            // }
            // exportTableByParams({
            //     methodUrl: api,
            //     options: params,
            //     columnsStr: columnsStrs,
            //     baseUrl: api_url,
            // });
            cacheRef?.current?.apply(params).then(() => {
                cacheRef?.current?.count();
            });
        } else {
            dispatch({
                type: 'couponManageModel/getCouponPutUseList',
                options: params,
            });
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = [
        {
            title: '发放时间',
            dataIndex: 'putTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券名称',
            width: 160,
            dataIndex: 'cpnName',
            render(text, record) {
                if (record.cpnId) {
                    return (
                        <Link
                            key={'01'}
                            to={
                                record.cpnType == '01'
                                    ? `/marketing/couponCenter/coupon/list/look/${record.cpnId}`
                                    : `/marketing/couponCenter/cpnManage/list/look/${record.cpnId}`
                            }
                            target="_blank"
                        >
                            {text}
                        </Link>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券状态',
            dataIndex: 'useStatusName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '关联活动',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                let path = formatActivePage({
                    type: String(record.actType),
                    actId: record.actId,
                    actVersion: record.cpnType,
                });
                if (path) {
                    return (
                        <Link to={path} target="_blank">
                            {text}
                        </Link>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠额度',
            width: 120,
            dataIndex: 'dctDesc',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销金额',
            width: 120,
            align: 'right',
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '有效期',
            dataIndex: 'cpnDate',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销时间',
            dataIndex: 'usedTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '券组编号',
            width: 120,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '券ID',
        //     width: 260,
        //     dataIndex: 'thirdRelateId',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '操作',
            width: 80,
            fixed: 'right',
            render(text, record) {
                return (
                    <Link
                        to={`/marketing/couponCenter/record/list/detail?id=${record.putDetId}&cpnType=${record.cpnType}`}
                        target="_blank"
                    >
                        详情
                    </Link>
                );
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={() => {
                    searchData(true);
                }}
            />
            <Tabs defaultActiveKey={STATUS_TYPES.ALL} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                <TabPane tab="未核销" key={STATUS_TYPES.NOUSE} />
                <TabPane tab="已核销" key={STATUS_TYPES.USE} />
                <TabPane tab="已过期" key={STATUS_TYPES.OVER} />
                {/* <TabPane tab="已作废" key={STATUS_TYPES.DELETE} /> */}
            </Tabs>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={couponPutUseList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponPutUseListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Card>
    );
};

const CouponManageListPage = (props) => {
    const {
        dispatch,
        history,
        couponManageModel: { couponPutUseList, couponPutUseListTotal },

        listLoading,
    } = props;
    const cacheRef = useRef();
    return (
        <PageHeaderWrapper extra={<CacheAreaView bizType="prizeVerifyExport" initRef={cacheRef} />}>
            <CouponManageListLayout {...props} cacheRef={cacheRef} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, couponManageModel, loading }) => ({
    global,
    couponManageModel,
    listLoading: loading.effects['couponManageModel/getCouponPutUseList'],
}))(CouponManageListPage);
