import { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect, useLocation } from 'umi';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Row,
    Select,
    Alert,
    DatePicker,
    Tabs,
    Modal,
    Space,
    Radio,
    Popconfirm,
    message,
} from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import usePageState from '@/hooks/usePageState.js';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { exportTableByParams } from '@/utils/utils';

import TablePro from '@/components/TablePro';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import styles from '@/assets/styles/common.less';
import {
    deleteModuleActTemplateApi,
    deleteModuleActTemplateMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { useImperativeHandle } from 'react';

import {
    MODULE_TYPES,
    MODULE_TYPE_CODES,
    MODULE_WEEK_TYPE_CODES,
    MODULE_ACT_TYPE_CODES,
} from './components/declare';
import ModuleEditModal from './components/ModuleEditModal';

const { TabPane } = Tabs;
const { Option } = Select;

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const inputOptions = {
    autoComplete: 'off',
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, tableLoading, exportFormEvent, tabType } = props;

    const resetEvent = () => {
        onReset();
    };

    return (
        <Form form={form} onFinish={onSubmit} initialValues={{}} scrollToFirstError>
            <SearchOptionsBar loading={tableLoading} onReset={resetEvent}>
                <Col span={8}>
                    <FormItem name="templateName" label="模板名称" {...formItemLayout}>
                        <Input placeholder="请填写" {...inputOptions} allowClear />
                    </FormItem>
                </Col>
                {(tabType == MODULE_TYPE_CODES.WEEK && (
                    <Col span={8}>
                        <FormItem name="templateSubType" label="周期类型" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                <Option value={MODULE_WEEK_TYPE_CODES.WEEK}>按周</Option>
                                <Option value={MODULE_WEEK_TYPE_CODES.DAY}>按日</Option>
                            </Select>
                        </FormItem>
                    </Col>
                )) ||
                    null}
                {(tabType == MODULE_TYPE_CODES.PRICE && (
                    <Col span={8}>
                        <FormItem name="templateActType" label="营销方式" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                <Option value={MODULE_ACT_TYPE_CODES.DISCOUNT}>打折</Option>
                                <Option value={MODULE_ACT_TYPE_CODES.REDUCTION}>立减</Option>
                                <Option value={MODULE_ACT_TYPE_CODES.DYNAMIC}>动态调价</Option>
                            </Select>
                        </FormItem>
                    </Col>
                )) ||
                    null}
            </SearchOptionsBar>
        </Form>
    );
};

const ModuleManagePage = (props) => {
    const {
        dispatch,
        tableLoading,
        businessModuleActiveModel: { moduleTemplateList, moduleTemplateTotal },
        global: { pageInit },
        currentUser,
    } = props;
    const { pathname } = useLocation();

    const [form] = Form.useForm();
    const editRef = useRef();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: MODULE_TYPES?.[0]?.value,
            checkStatus: '',
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'businessModuleActiveModel/updateProperty',
                params: {
                    moduleTemplateList: [],
                    moduleTemplateTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const searchData = (isDownload = false) => {
        const data = form.getFieldsValue();

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        let params = {
            ...data,
            operType: (currentUser?.operId?.length && '01') || '02',
            createSource: '01',
        };

        if (pageInfo?.tabType) {
            params.templateType = pageInfo?.tabType;
        }

        if (isDownload) {
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }

            exportTableByParams({
                options: params,
                columnsStr: columnsStrs,
            });
        } else {
            params.pageIndex = pageInfo.pageIndex;
            params.pageSize = pageInfo.pageSize;
            dispatch({
                type: 'businessModuleActiveModel/getModuleActTemplateList',
                options: params,
            });
        }
    };

    const handleSearch = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const editRecord = (id) => {
        // id为空表示新增
        editRef.current.show({ id });
    };

    const columns = useMemo(
        () => [
            {
                title: '模板名称',
                width: 80,
                dataIndex: 'templateName',
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            ...((pageInfo.tabType == MODULE_TYPE_CODES.WEEK && [
                {
                    title: '周期类型',
                    width: 120,
                    dataIndex: 'templateSubTypeName',
                    render(text, record) {
                        return (
                            <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                {text || '-'}
                            </span>
                        );
                    },
                },
            ]) ||
                []),
            ...((pageInfo.tabType == MODULE_TYPE_CODES.PRICE && [
                {
                    title: '活动类型',
                    width: 120,
                    dataIndex: 'templateBelongTypeName',
                    render(text, record) {
                        return (
                            <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                {text || '-'}
                            </span>
                        );
                    },
                },
                {
                    title: '营销方式',
                    width: 120,
                    dataIndex: 'templateActTypeName',
                    render(text, record) {
                        return (
                            <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                {text || '-'}
                            </span>
                        );
                    },
                },
            ]) ||
                []),
            {
                title: '模板内容',
                width: 240,
                dataIndex: 'templateDetail',
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            {
                title: '操作',
                width: 80,
                fixed: 'right',
                render: (text, record) => (
                    <Fragment>
                        <Space>
                            {((currentUser?.name == 'SYSADMIN' ||
                                currentUser?.name == record.createdBy) && (
                                <span
                                    className={styles['table-btn']}
                                    onClick={() => {
                                        editRecord(record.templateId);
                                    }}
                                >
                                    编辑
                                </span>
                            )) ||
                                null}
                            {((currentUser?.name == 'SYSADMIN' ||
                                currentUser?.name == record.createdBy) && (
                                <Popconfirm
                                    title="确定删除吗？"
                                    onConfirm={async () => {
                                        try {
                                            await deleteModuleActTemplateMngApi({
                                                templateId: record.templateId,
                                            });
                                            message.success('操作成功');
                                            searchData();
                                        } catch (error) {}
                                    }}
                                >
                                    <span className={styles['table-btn']}>删除</span>
                                </Popconfirm>
                            )) ||
                                null}
                        </Space>
                    </Fragment>
                ),
            },
        ],
        [pageInfo],
    );

    return (
        <>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={handleSearch}
                onReset={resetData}
                exportFormEvent={() => searchData(true)}
                tabType={pageInfo.tabType}
            />
            <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginBottom: '20px' }}>
                <Col>
                    <Button type="primary" onClick={() => editRecord()}>
                        <PlusOutlined />
                        新建模板
                    </Button>
                </Col>
            </Row>
            <Tabs activeKey={pageInfo?.tabType} onChange={changeTabTypeEvent}>
                {MODULE_TYPES.map((ele) => (
                    <TabPane tab={ele.label} key={ele.value} />
                ))}
            </Tabs>
            <TablePro
                name="modulelist"
                loading={tableLoading}
                scroll={{ x: '100%' }}
                rowKey={(record, index) => index}
                dataSource={moduleTemplateList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: moduleTemplateTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                filterHeader={false}
            />
            <ModuleEditModal
                {...props}
                initRef={editRef}
                onFinish={({ templateType }) => {
                    changePageInfo({ tabType: templateType, pageIndex: 1 });
                }}
            />
        </>
    );
};

export default connect(({ global, loading, businessModuleActiveModel, user }) => ({
    global,
    businessModuleActiveModel,
    currentUser: user.currentUser,
    tableLoading: loading.effects['businessModuleActiveModel/getModuleActTemplateList'],
}))(ModuleManagePage);
