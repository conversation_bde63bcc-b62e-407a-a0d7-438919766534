import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    stopMembershipPlanApi,
    deleteMembershipPlanApi,
} from '@/services/Marketing/MarketingMembershipApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { RIGHTS_TYPES } from './RightsConfig';
import { LeftOutlined } from '@ant-design/icons';
import { Link } from 'umi';

const { Option } = Select;
const { RangePicker } = DatePicker;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        global: { codeInfo = {} },
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
    } = props;

    const { equityCategory } = codeInfo;

    useEffect(() => {
        if (!equityCategory?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'equityCategory',
            });
        }
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const equityCategoryOptions = useMemo(() => {
        return equityCategory?.map((ele) => {
            if (
                ele.codeValue == RIGHTS_TYPES.COUPON ||
                ele.codeValue == RIGHTS_TYPES.ADD ||
                ele.codeValue == RIGHTS_TYPES.RED_BAG
            ) {
                return (
                    <Option value={ele.codeValue} key={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            }
        });
    }, [equityCategory]);

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} form={form}>
                <Col span={8}>
                    <FormItem label="发放日期:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="用户手机" name="mobile">
                        <Input placeholder="请输入" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券包模板ID" name="couponBagNo">
                        <Input placeholder="请输入" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="关联权益" name="equityNo">
                        <Select placeholder="请选择">{equityCategoryOptions}</Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const RightSendRecordList = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {} },
        couponModel: { couponBagSendList, couponBagSendCount },
        listLoading,
        route,
    } = props;

    const {
        location: { pathname, query: { no: couponBagNo } = {} },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        form.setFieldsValue({ couponBagNo });
    }, [couponBagNo]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    beginDate: data.dates?.[0]?.format('YYYY-MM-DD'),
                    endDate: data.dates?.[1]?.format('YYYY-MM-DD'),
                    dates: undefined,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'couponModel/getCpnBagRecordList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push('/userCenter/membership/rights/list/add');
    };
    // 跳转发放记录页面
    const sendRecordEvent = () => {
        history.push('/userCenter/membership/rights/list/add');
    };

    const editEvent = (item) => {
        history.push(`/userCenter/membership/rights/list/update/${item.equityId}/${item.equityNo}`);
    };

    const lookEvent = (item) => {
        if (item.equityNo === RIGHTS_TYPES.MEMBER_PROPR) {
            history.push(`/marketing/businessActive/moduleAct`);
        } else {
            history.push(
                `/userCenter/membership/rights/list/detail/${item.equityId}/${item.equityNo}`,
            );
        }
    };

    const columns = [
        {
            title: '券包模板ID ',
            width: '120px',
            dataIndex: 'couponBagNo',
            render(text, record, index) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包发放ID',
            width: 120,
            dataIndex: 'sendNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '关联权益',
            width: 160,
            dataIndex: 'equityName',
            render(text, record) {
                if (text?.length && record.equityNo?.length) {
                    return (
                        <Link
                            key={'03'}
                            to={`/userCenter/membership/rights/list/update/${record.equityId}/${record.equityNo}`}
                            target="_blank"
                        >
                            {text}
                        </Link>
                    );
                } else if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户手机',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '单包券数',
            width: 120,
            dataIndex: 'singleNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '单包价值（元）',
            width: 160,
            dataIndex: 'singleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发放日期',
            width: 200,
            dataIndex: 'sendTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <SearchLayout
                    {...props}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={couponBagSendList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: couponBagSendCount,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, couponModel, loading }) => ({
    global,
    couponModel,
    listLoading: loading.effects['couponModel/getCpnBagRecordList'],
}))(RightSendRecordList);
