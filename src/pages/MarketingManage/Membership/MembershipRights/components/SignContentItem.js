// 签到领券
import {
    Form,
    DatePicker,
    Button,
    Col,
    Select,
    Space,
    Modal,
    message,
    Popconfirm,
    Input,
} from 'antd';
import { Fragment, useState, forwardRef, useImperativeHandle } from 'react';
import TablePro from '@/components/TablePro';
import commonStyle from '@/assets/styles/common.less';
import style from '../../Membership.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { RIGHTS_TYPES } from '../RightsConfig';
import { useEffect, useRef, useMemo } from 'react';
const { RangePicker } = DatePicker;
import CitysSelect from '@/components/CitysSelect/index.js';
import EditSignModal from './EditSignModal';
import AddRightModal from './AddRightModal';
import SortRightModal from './SortRightModal';
import CityTransferModal from '@/components/CityTransferModal';
import moment from 'moment';
import {
    getVipPlanEquityCityApi,
    getVipPlanSelectListApi,
    getVipRelaActListApi,
} from '@/services/Marketing/MarketingMembershipApi';
import { PlusOutlined, OrderedListOutlined } from '@ant-design/icons';
import { Link } from 'umi';
import { isEmpty } from '@/utils/utils';
import { SIGNIN_ACT_ACT_ENUM } from '@/pages/MarketingManage/Signin/Activity/constatns';
import { getActivityList } from '@/services/Marketing/SigninActivityApi';
import usePageState from '@/hooks/usePageState';

const formItemLayout = {};
const { Option } = Select;

const FormItem = Form.Item;

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form name="rights-search" {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} form={form}>
                <Col span={8}>
                    <CitysSelect
                        label="投放区域"
                        name="cityCodeList"
                        placeholder="请选择"
                        formItemLayout={formItemLayout}
                        showArrow
                        provinceSelectable
                        rules={null}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="权益有效期:" name="dates" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="权益状态:" name="equityStatus" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {[
                                { codeName: '未开始', codeValue: '0' },
                                { codeName: '进行中', codeValue: '1' },
                                { codeName: '已结束', codeValue: '2' },
                            ].map((ele) => (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const SelectActModal = forwardRef((props, ref) => {
    const { onConfirm, actList } = props;
    const [showSelectModal, toggleSelectModal] = useState(false);

    const [searchResult, changeSearchResult] = useState();

    const [actForm] = Form.useForm();

    useImperativeHandle(ref, () => {
        return {
            open: (options) => {
                const { actId, index } = options;
                actForm.setFieldsValue({
                    actId,
                    index,
                });
                toggleSelectModal(true);
            },
        };
    });

    const handleSearch = (newValue) => {
        changeSearchResult(newValue);
    };

    const confirmModal = async () => {
        try {
            await actForm.validateFields();
            const formInfo = actForm.getFieldsValue();
            const { actId, index } = formInfo;
            onConfirm &&
                onConfirm({
                    index,
                    actId: actId || '',
                });
            toggleSelectModal(false);
        } catch (error) {}
    };
    const closeModal = () => {
        toggleSelectModal(false);
        changeSearchResult(null);
    };

    const filterSelectOptions = useMemo(() => {
        if (actList instanceof Array) {
            const filterActList = actList.filter((ele) => {
                if (!isEmpty(searchResult)) {
                    return (
                        ele?.actId?.indexOf(searchResult) >= 0 ||
                        ele?.actName?.indexOf(searchResult) >= 0
                    );
                }
                return true;
            });

            return filterActList.map((ele) => {
                const title = `${ele.actName}-${ele.actId}`;
                return (
                    <Option
                        // disabled={filterSelectList.includes(ele.actId)}
                        value={ele.actId}
                        key={ele.actId}
                    >
                        <span title={title}>{title}</span>
                    </Option>
                );
            });
        } else {
            return [];
        }
    }, [actList, searchResult]);

    return (
        <Modal
            title={'关联方案'}
            visible={showSelectModal}
            width={500}
            destroyOnClose
            onOk={confirmModal}
            onCancel={closeModal}
        >
            <Form form={actForm} name="actSelectForm">
                <FormItem noStyle name="index"></FormItem>
                <FormItem label="配置关联方案" name="actId">
                    <Select
                        showSearch
                        allowClear
                        placeholder="未设置"
                        onSearch={handleSearch}
                        filterOption={false}
                    >
                        {filterSelectOptions}
                    </Select>
                </FormItem>
            </Form>
        </Modal>
    );
});
// 签到活动弹窗
const SelectSignActModal = forwardRef((props, ref) => {
    const { onConfirm } = props;
    const [showSelectModal, toggleSelectModal] = useState(false);

    const [actForm] = Form.useForm();
    const [searchForm] = Form.useForm();

    useImperativeHandle(ref, () => {
        return {
            open: (options) => {
                const { relaId, index, relaSubTypeName, relaSubActName } = options;
                if (relaId) {
                    updateSelectedRows([
                        { actId: relaId, relaSubTypeName, actName: relaSubActName },
                    ]);
                } else {
                    updateSelectedRows([]);
                }
                actForm.setFieldsValue({
                    index,
                });
                toggleSelectModal(true);
                searchForm?.resetFields();
                if (pageInfo.pageIndex == 1) {
                    searchData();
                } else {
                    changePageInfo({ pageIndex: 1 });
                }
            },
        };
    });

    const confirmModal = async () => {
        try {
            if (!selectRows?.length) {
                message.error('请选择签到方案');
                return;
            }
            const formInfo = actForm.getFieldsValue();
            const { index } = formInfo;

            const { actId, actSubTypeName: relaSubTypeName, actName } = selectRows[0];
            onConfirm &&
                onConfirm({
                    index,
                    relaId: actId || '',
                    relaSubTypeName,
                    relaSubActName: actName,
                });
            toggleSelectModal(false);
        } catch (error) {}
    };
    const closeModal = () => {
        toggleSelectModal(false);
    };

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [actList, changeActList] = useState([]);
    const [actTotal, changeTotal] = useState(0);
    const [listLoading, updateLoading] = useState(false);
    const searchData = async () => {
        try {
            updateLoading(true);
            const params = await searchForm.getFieldsValue();
            params.pageIndex = pageInfo.pageIndex || undefined;
            params.pageSize = pageInfo.pageSize;
            params.actState = 2;
            if (Array.isArray(params.actSubType) && params.actSubType?.length > 1) {
                delete params.actSubType;
            }
            const {
                data: { records, total },
            } = await getActivityList(params);
            changeActList(records);
            changeTotal(total);
            return records;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateLoading(false);
        }
    };
    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const columns = [
        {
            title: '签到活动ID',
            width: 120,
            dataIndex: 'actId',
        },
        {
            title: '活动名称',
            dataIndex: 'actName',
            width: 160,
        },
        {
            title: '签到活动类型',
            dataIndex: 'actSubTypeName',
            width: 160,
        },
        {
            title: '签到周期',
            width: 120,
            dataIndex: 'actCycle',
        },
        {
            title: '更新时间',
            width: 200,
            dataIndex: 'dataOperTime',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creEmp',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'actStateName',
        },
    ];

    const [selectRows, updateSelectedRows] = useState([]);
    const rowSelection = {
        selectedRowKeys: selectRows?.map((ele) => ele.actId),
        onChange: (selectedRowKeys, selectedRows) => {
            updateSelectedRows([...selectedRows]);
        },
        type: 'radio',
    };

    return (
        <Modal
            title={'选择关联签到活动'}
            visible={showSelectModal}
            width={880}
            destroyOnClose
            onOk={confirmModal}
            onCancel={closeModal}
        >
            <Form form={searchForm} onFinish={() => searchData()}>
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={() => {
                        searchForm?.resetFields();
                        searchData();
                    }}
                    open
                >
                    <Col span={8}>
                        <Form.Item label="签到活动名称" name="actName">
                            <Input
                                maxLength={50}
                                allowClear
                                placeholder="请输入"
                                autoComplete="off"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="活动ID" name="actId">
                            <Input
                                maxLength={50}
                                allowClear
                                placeholder="请输入"
                                autoComplete="off"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="活动类型" name="actSubType">
                            <Select placeholder="请选择活动类型" allowClear mode="multiple">
                                <Select.Option value={SIGNIN_ACT_ACT_ENUM.LIANXU}>
                                    连续签到
                                </Select.Option>
                                <Select.Option value={SIGNIN_ACT_ACT_ENUM.LEIJI}>
                                    累积签到
                                </Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>

            <Button type="primary" style={{ marginBottom: '20px' }}>
                <Link to={`/marketing/signin/activity/list`} target="_blank">
                    创建活动
                </Link>
            </Button>

            <Form form={actForm} name="actSelectForm">
                <FormItem noStyle name="index"></FormItem>
                <TablePro
                    rowSelection={(actList?.length && rowSelection) || undefined}
                    name="rights"
                    scroll={{ x: 'max-content', y: 450 }}
                    rowKey={(record, index) => record.actId}
                    dataSource={actList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: actTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Form>
        </Modal>
    );
});

const SignContentItem = (props, ref) => {
    const {
        label = '权益内容',
        name,
        snName, // 排序字段
        form,
        parentName, //父级name
        disabled,
        addEvent,
        equityNo,
        equityId,
        history,
        membershipModel: { membershipRightsInfo },
        isLock,
        ...otherOptions
    } = props;

    const [searchForm] = Form.useForm();

    const [rightsList, changeRightsList] = useState([]);
    const [signList, changeSignList] = useState([]);

    const [actList, changeActList] = useState([]);
    const [signActList, changeSignActList] = useState([]);

    useImperativeHandle(ref, () => {
        return {
            getActList: () => {
                return actList;
            },
        };
    });

    useEffect(() => {
        getVipPlanSelectList();
    }, []);

    const getVipPlanSelectList = async () => {
        try {
            const {
                data: { vip_plan_list },
            } = await getVipPlanSelectListApi({ equityNo, actSubType: '2702' });
            if (vip_plan_list instanceof Array) {
                changeActList(vip_plan_list);
            }
            return vip_plan_list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 控制列表是否需要排序功能
    const needSort = ['QY0003'].includes(equityNo);

    const [curEditItem, changeCurEditItem] = useState(undefined);
    const [filterOptions, changeFilterOptions] = useState({});

    const originList = Form.useWatch(name);

    const editModalRef = useRef();
    const cityModalRef = useRef();
    const addModalRef = useRef();
    const sortModalRef = useRef();

    const selectActRef = useRef();
    const selectSignActRef = useRef();

    const filterList = useMemo(() => {
        return rightsList
            .filter((ele) => {
                // 如果没有筛选，直接返回true用于展示
                if (Object.values(filterOptions)?.length == 0) {
                    return true;
                }
                let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。
                for (const key in filterOptions) {
                    if (Object.hasOwnProperty.call(ele, key)) {
                        if (key != 'bgnTime' && key != 'endTime' && key != 'cityCodeList') {
                            //过滤其他
                            const element = filterOptions[key];
                            if (ele[key] != element) {
                                passFilter = false;
                                break;
                            }
                        }
                    }
                }
                if (
                    filterOptions.cityCodeList &&
                    filterOptions.cityCodeList instanceof Array &&
                    ele.cityCodeList instanceof Array &&
                    passFilter
                ) {
                    //过滤区域
                    const listCityKeys = ele.cityCodeList.map((ele) => ele.areaCode);
                    let mergeCity = [...new Set([...filterOptions.cityCodeList, ...listCityKeys])];
                    if (
                        mergeCity.length ==
                        listCityKeys?.length + filterOptions.cityCodeList.length
                    ) {
                        passFilter = false;
                    }
                }
                if (filterOptions.bgnTime && filterOptions.endTime && passFilter) {
                    //过滤日期
                    const isBetween =
                        ele.bgnTime?.length &&
                        ele.endTime?.length &&
                        (moment(filterOptions.bgnTime).isBetween(ele.bgnTime, ele.endTime) ||
                            moment(filterOptions.endTime).isBetween(ele.bgnTime, ele.endTime));

                    if (!isBetween) {
                        passFilter = false;
                    }
                }
                return passFilter;
            })
            .map((ele, index) => {
                ele.index = ele.index ? ele.index : index + 1;
                return ele;
            });
    }, [rightsList, filterOptions]);

    const canEditPrice = equityNo === RIGHTS_TYPES.ADD;

    useEffect(() => {
        if (originList instanceof Array) {
            changeRightsList(originList);
        }
    }, [originList]);

    useEffect(() => {
        sortModalRef.current?.init({ snList: membershipRightsInfo?.[snName] });
    }, [membershipRightsInfo]);

    const touchAddEvent = () => {
        // addEvent && addEvent();
        // 统计不可用的券包和城市
        const disabledCityKeys = [];
        // const disabledCpnBagIds = [];
        // 基础券包权益、天天领红包权益：每个城市只能选择一个券包；已经配置过的城市，不可选。
        rightsList.map((ele) => {
            if (ele.equityStatus == '2') {
                // 已结束的券包可重新开放城市
                return;
            }
            // ele.cityCodeList?.map?.((cityEle) => {
            //     disabledCityKeys.push(cityEle.areaCode);
            // });
            // disabledCpnBagIds.push(ele.couponBagId);
        });

        addModalRef.current.show({
            disabledCityKeys:
                ((equityNo == 'QY0002' || equityNo == 'QY0004') && disabledCityKeys) || [],
            // disabledCpnBagIds,
            rightType: equityNo,
        });
    };

    const editEvent = (item, index) => {
        const otherCityCodes = [];
        if (item.equityStatus == '1') {
            // 已添加过的场站，可新增不可删除
            // const cityCodeList =
            //     membershipRightsInfo?.equityBagBoList?.find(
            //         (ele) => ele.couponBagId == item.couponBagId,
            //     )?.cityCodeList || [];
            // otherCityCodes.push(...(cityCodeList.map((ele) => ele.areaCode) || []));
        }
        item.sn = undefined;
        editModalRef.current.show({
            value: item,
            disabledCityKeys: otherCityCodes,
            disabled: item.equityStatus == '2',
        });
        changeCurEditItem(item);
    };

    const delEvent = (item) => {
        // equitBagId
        rightsList.splice(
            rightsList.findIndex((ele, index) => index + 1 === item.index),
            1,
        );
        changeRightsList([...rightsList]);
        form.setFieldsValue({ [name]: rightsList });

        if (needSort) {
            const newList = sortModalRef.current.deleteRightItem(item);
            form.setFieldsValue({ [snName]: newList });
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        searchData();
    };
    const searchData = () => {
        const params = searchForm.getFieldsValue();
        const options = {};
        if (params.cityCodeList?.length) {
            options.cityCodeList = params.cityCodeList;
        }
        if (params.equityStatus) {
            options.equityStatus = params.equityStatus;
        }
        if (params.dates) {
            options.bgnTime = params.dates?.[0]?.format('YYYY-MM-DD') || '';
            options.endTime = params.dates?.[1]?.format('YYYY-MM-DD') || '';
        }
        changeFilterOptions(options);
    };

    const columns = useMemo(() => {
        return [
            {
                title: '序号 ',
                width: 80,
                dataIndex: 'index',
                render(text, record, index) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '关联方案',
                width: 240,
                dataIndex: 'actName',
                render(text, record, index) {
                    const actId = record?.actId;
                    let resultText = '';

                    if (record.actName) {
                        resultText = `${record?.actName}-${record?.actId}`;
                        return (
                            <Link
                                to={`/userCenter/membership/plan/list/detail-vip/${record?.actId}`}
                                target="_blank"
                            >
                                {resultText}
                            </Link>
                        );
                        // return <span title={resultText}>{resultText}</span>;
                    }

                    if (actId) {
                        if (record.delFlag === '1') {
                            return `方案已删除-${actId}`;
                        }
                        const actInfo = actList.find((ele) => ele.actId === actId);
                        if (actInfo) {
                            resultText = `${actInfo?.actName}-${actInfo?.actId}`;
                        }
                    }
                    if (!resultText && record.equityStatus === '2') {
                        return;
                    }

                    return (
                        <span
                            className={style['table-btn']}
                            style={{ marginLeft: 0, color: '#ff9901' }}
                            onClick={() => {
                                getVipPlanSelectList();
                                selectActRef.current?.open({
                                    actId,
                                    index,
                                });
                            }}
                        >
                            {resultText || '未设置'}
                        </span>
                    );
                },
            },
            {
                title: '关联签到方案',
                width: 240,
                dataIndex: 'signActName',
                render(text, record, index) {
                    const relaId = record?.relaId;
                    let resultText = '';

                    if (record.relaName) {
                        resultText = `${record?.relaName}-${record?.relaId}`;
                        return (
                            <Link
                                to={`/marketing/signin/activity/list/detail/${record?.relaId}`}
                                target="_blank"
                            >
                                {resultText}
                            </Link>
                        );
                        // return <span title={resultText}>{resultText}</span>;
                    }

                    if (relaId) {
                        if (record.delFlag === '1') {
                            return `方案已删除-${relaId}`;
                        } else if (record?.relaSubActName) {
                            resultText = `${record?.relaSubActName}-${record?.relaId}`;
                        }
                    }
                    if (!resultText && record.equityStatus === '2') {
                        return;
                    }

                    return (
                        <span
                            className={style['table-btn']}
                            style={{ marginLeft: 0, color: '#ff9901' }}
                            onClick={() => {
                                selectSignActRef.current?.open({
                                    relaId,
                                    index,
                                    relaSubTypeName: record.relaSubTypeName,
                                    relaSubActName: record.relaSubActName,
                                });
                            }}
                        >
                            {resultText || '未设置'}
                        </span>
                    );
                },
            },
            {
                title: '活动类型',
                width: 120,
                dataIndex: 'relaSubTypeName',
                render(text, record) {
                    if (!text?.length) {
                        text = '-';
                    }
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '投放区域',
                width: 200,
                render(text, record, index) {
                    const strs = [];
                    const codes = [];
                    record?.cityCodeList?.map((ele) => {
                        strs.push(ele.areaName);
                        codes.push(ele.areaCode);
                    });
                    const showStr = strs.slice(0, 2);
                    text = `${showStr.join(' | ')}${(strs.length > 2 && '...') || ''}`;
                    return (
                        (strs.length && (
                            <span
                                className={commonStyle['table-btn']}
                                onClick={() => {
                                    // 统计不可用的券包和城市
                                    const disabledCityKeys = [];
                                    record.sn = undefined;
                                    cityModalRef?.current?.show({
                                        defaultKeys: codes,
                                        disabledKeys: disabledCityKeys,
                                        disabled: isLock || record.equityStatus == '2', // 查看和已结束都不允许再选择区域
                                    });
                                    changeCurEditItem(record);
                                }}
                            >
                                <div
                                    style={{
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        display: '-webkit-box',
                                        WebkitBoxOrient: 'vertical',
                                        WebkitLineClamp: '1',
                                    }}
                                    title={strs?.join('、') || undefined}
                                >
                                    {text}
                                </div>
                            </span>
                        )) || <span>-</span>
                    );
                },
            },

            {
                title: '开始时间',
                width: 220,
                fixed: 'right',
                dataIndex: 'bgnTime',
                render(text, record, index) {
                    if (!isLock && record?.equityStatus < 1) {
                        const bgnTime =
                            record?.bgnTime && moment(record?.bgnTime, 'YYYY-MM-DD HH:mm:ss');
                        return (
                            <div className={style['card-col']}>
                                <DatePicker
                                    bordered={false}
                                    showTime
                                    showNow={false}
                                    value={bgnTime}
                                    onOk={(e) => {
                                        const editIndex = rightsList.findIndex(
                                            (ele, index) => index + 1 === record.index,
                                        );
                                        const editItem = rightsList[editIndex];
                                        editItem.bgnTime = e.format('YYYY-MM-DD HH:mm:ss');

                                        const endTime =
                                            record?.endTime &&
                                            moment(record?.endTime, 'YYYY-MM-DD HH:mm:ss');
                                        if (e > endTime) {
                                            editItem.endTime = undefined;
                                        }
                                        changeRightsList([...rightsList]);
                                    }}
                                    disabledDate={(current) => {
                                        if (current) {
                                            const currentTime = moment();
                                            // 以当前时间做参考
                                            return current < currentTime;
                                        }
                                        return false;
                                    }}
                                    allowClear={false}
                                    placeholder="未设置"
                                />
                            </div>
                        );
                    }
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '结束时间',
                width: 220,
                fixed: 'right',
                dataIndex: 'endTime',
                render(text, record, index) {
                    if (!isLock && record?.equityStatus != '2') {
                        const bgnTime =
                            record?.bgnTime && moment(record?.bgnTime, 'YYYY-MM-DD HH:mm:ss');
                        const endTime =
                            record?.endTime && moment(record?.endTime, 'YYYY-MM-DD HH:mm:ss');
                        return (
                            <div className={style['card-col']}>
                                <DatePicker
                                    bordered={false}
                                    showTime
                                    showNow={false}
                                    value={endTime}
                                    onOk={(e) => {
                                        const editIndex = rightsList.findIndex(
                                            (ele, index) => index + 1 === record.index,
                                        );
                                        const editItem = rightsList[editIndex];
                                        editItem.endTime = e.format('YYYY-MM-DD HH:mm:ss');
                                        changeRightsList([...rightsList]);
                                    }}
                                    disabledDate={(current) => {
                                        if (current) {
                                            const currentTime = moment();
                                            // 只能晚于开始时间或者当前时间中的最晚时间
                                            if (bgnTime > currentTime) {
                                                // 如果开始时间晚于当前时间，以开始时间做参考
                                                return current < bgnTime;
                                            }
                                            // 以当前时间做参考
                                            return current < currentTime;
                                        }
                                        return false;
                                    }}
                                    allowClear={false}
                                    placeholder="未设置"
                                    disabled={!bgnTime}
                                />
                            </div>
                        );
                    }
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '权益状态',
                fixed: 'right',
                width: 120,
                dataIndex: 'equityStatusName',
                render(text, record) {
                    if (!text?.length) {
                        text = '-';
                    }
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '操作',
                fixed: 'right',
                width: 140,
                render: (text, record, index) => {
                    const editBtn = (
                        <span
                            className={commonStyle['table-btn']}
                            onClick={() => {
                                editEvent(record, index);
                            }}
                        >
                            编辑
                        </span>
                    );

                    const delBtn = (
                        <Popconfirm
                            title="要从列表中删除此权益？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => delEvent(record)}
                        >
                            <span className={commonStyle['table-btn']} style={{ color: '#f50' }}>
                                删除
                            </span>
                        </Popconfirm>
                    );

                    let btns = [];
                    // equityStatus：0 未开始 1 进行中 2 已结束
                    if (isLock) {
                    } else if (record.equityStatus === '0') {
                        //未开始 或 未提交
                        btns.push(editBtn);
                        btns.push(delBtn);
                    } else if (record.equityStatus == '1') {
                        btns.push(editBtn);
                    }

                    return <Space>{btns}</Space>;
                },
            },
        ];
    });

    const cityRequest =
        equityNo == 'QY0002'
            ? undefined
            : async (params = {}) => {
                  try {
                      const {
                          data: { areaList },
                      } = await getVipPlanEquityCityApi(params);

                      return areaList;
                  } catch (error) {
                      return [];
                  }
              };

    return (
        <Fragment>
            <FormItem name={snName} noStyle />
            <FormItem label={label} name={name} {...otherOptions}>
                <SearchLayout
                    {...props}
                    form={searchForm}
                    onSubmit={() => {
                        searchData();
                    }}
                    onReset={resetData}
                />
                {((!isLock || needSort) && (
                    <Space className={commonStyle['btn-bar']}>
                        {(!isLock && (
                            <Button type="primary" onClick={touchAddEvent}>
                                <PlusOutlined />
                                新增权益
                            </Button>
                        )) ||
                            null}
                        {(needSort && (
                            <Button
                                onClick={() => {
                                    const oldSnList = form.getFieldValue(snName) || [];
                                    if (!oldSnList?.length) {
                                        message.error('无可排序内容，请先新增权益');
                                        return;
                                    }
                                    sortModalRef.current.show({
                                        oldSnList: [...oldSnList],
                                    });
                                }}
                            >
                                <OrderedListOutlined />
                                排序
                            </Button>
                        )) ||
                            null}
                    </Space>
                )) ||
                    null}
                <TablePro
                    name="rights"
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={filterList}
                    columns={columns}
                />
            </FormItem>

            <EditSignModal
                {...props}
                canEditPrice={canEditPrice}
                onFinish={(values) => {
                    const newItem = { ...curEditItem, ...values };
                    const editIndex = rightsList.findIndex(
                        (ele, index) => index + 1 === curEditItem.index,
                    );
                    rightsList.splice(editIndex, 1, newItem);

                    changeRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: rightsList });
                    // 编辑券包项处理排序
                    if (needSort) {
                        const newList = sortModalRef.current.editRightItem(curEditItem, newItem);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                ref={editModalRef}
                cityRequest={cityRequest}
            />

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    // 编辑城市处理排序
                    const newItem = { ...curEditItem, cityCodeList: citys };
                    rightsList.splice(
                        rightsList.findIndex((ele, index) => index + 1 === curEditItem.index),
                        1,
                        newItem,
                    );
                    changeRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: rightsList });
                    if (needSort) {
                        const newList = sortModalRef.current.editRightItem(curEditItem, newItem);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                request={cityRequest}
            />

            <AddRightModal
                {...props}
                ref={addModalRef}
                onFinish={(value) => {
                    // 插入的直接叫未开始
                    rightsList.splice(0, 0, {
                        ...value,
                        equityStatus: '0',
                        equityStatusName: '未开始',
                    });

                    rightsList.map((ele, index) => {
                        ele.index = index + 1;
                        return ele;
                    });
                    changeRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: rightsList });
                    if (needSort) {
                        const newList = sortModalRef.current.addRightItem(value);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                cityRequest={cityRequest}
            />

            <SortRightModal
                {...props}
                ref={sortModalRef}
                onFinish={(newSnList) => {
                    form.setFieldsValue({ [snName]: newSnList });
                }}
                disabled={isLock}
            />
            <SelectActModal
                ref={selectActRef}
                actList={actList}
                onConfirm={(info) => {
                    const { actId, index } = info;
                    if (index >= 0) {
                        const editItem = rightsList[index];
                        editItem.actId = actId;

                        changeRightsList([...rightsList]);
                        form.setFieldsValue({ [name]: rightsList });
                    }
                }}
            ></SelectActModal>
            <SelectSignActModal
                ref={selectSignActRef}
                onConfirm={(info) => {
                    const { relaId, relaSubTypeName, relaSubActName, index } = info;
                    if (index >= 0) {
                        const editItem = rightsList[index];
                        editItem.relaId = relaId;
                        editItem.relaSubTypeName = relaSubTypeName;
                        editItem.relaSubActName = relaSubActName;
                        console.log(editItem);
                        changeRightsList([...rightsList]);
                        form.setFieldsValue({ [name]: rightsList });
                    }
                }}
            ></SelectSignActModal>
        </Fragment>
    );
};

export default forwardRef(SignContentItem);
