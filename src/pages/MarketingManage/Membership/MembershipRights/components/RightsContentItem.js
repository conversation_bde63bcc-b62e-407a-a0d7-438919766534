import { Form, DatePicker, Button, Col, Select, Space, Modal, message, Popconfirm } from 'antd';
import { Fragment, useState, forwardRef, useImperativeHandle } from 'react';
import TablePro from '@/components/TablePro';
import commonStyle from '@/assets/styles/common.less';
import style from '../../Membership.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { RIGHTS_TYPES } from '../RightsConfig';
import { useEffect, useRef, useMemo } from 'react';
const { RangePicker } = DatePicker;
import CitysSelect from '@/components/CitysSelect/index.js';
import EditCouponBagModal from './EditCouponBagModal';
import AddRightModal from './AddRightModal';
import SortRightModal from './SortRightModal';
import CityTransferModal from '@/components/CityTransferModal';
import moment from 'moment';
import {
    getVipPlanEquityCityApi,
    getVipPlanSelectListApi,
} from '@/services/Marketing/MarketingMembershipApi';
import { PlusOutlined, OrderedListOutlined } from '@ant-design/icons';
import { Link } from 'umi';
import { isEmpty } from '@/utils/utils';

const formItemLayout = {};
const { Option } = Select;

const FormItem = Form.Item;

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form name="rights-search" {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} form={form}>
                <Col span={8}>
                    <CitysSelect
                        label="投放区域"
                        name="cityCodeList"
                        placeholder="请选择"
                        formItemLayout={formItemLayout}
                        showArrow
                        provinceSelectable
                        rules={null}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="权益有效期:" name="dates" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="权益状态:" name="equityStatus" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {[
                                { codeName: '未开始', codeValue: '0' },
                                { codeName: '进行中', codeValue: '1' },
                                { codeName: '已结束', codeValue: '2' },
                            ].map((ele) => (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券包状态:" name="couponBagStatus" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {[
                                { codeName: '可发放', codeValue: '1' },
                                { codeName: '已终止', codeValue: '2' },
                                { codeName: '草稿', codeValue: '0' },
                            ].map((ele) => (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const SelectActModal = forwardRef((props, ref) => {
    const { onConfirm, actList } = props;
    const [showSelectModal, toggleSelectModal] = useState(false);
    // const [couponBagId, changeCouponBagId] = useState(null);
    // const [actId, changeActId] = useState(null);
    const [title, changeTitle] = useState('');

    const [searchResult, changeSearchResult] = useState();

    const [actForm] = Form.useForm();

    useImperativeHandle(ref, () => {
        return {
            open: (options) => {
                const { actId, couponBagId, couponBagName, index } = options;
                // changeActId(editActId);
                // changeCouponBagId(editCpnBagId);
                changeTitle(couponBagName || '');
                actForm.setFieldsValue({
                    actId,
                    couponBagId,
                    index,
                });
                toggleSelectModal(true);
            },
        };
    });

    const handleSearch = (newValue) => {
        changeSearchResult(newValue);
    };

    const confirmModal = async () => {
        try {
            await actForm.validateFields();
            const formInfo = actForm.getFieldsValue();
            const { actId, couponBagId, index } = formInfo;
            onConfirm &&
                onConfirm({
                    couponBagId,
                    actId: actId || '',
                    index,
                });
            toggleSelectModal(false);
        } catch (error) {}
    };
    const closeModal = () => {
        toggleSelectModal(false);
        changeTitle('');
        changeSearchResult(null);
    };

    const filterSelectOptions = useMemo(() => {
        if (actList instanceof Array) {
            const filterActList = actList.filter((ele) => {
                if (!isEmpty(searchResult)) {
                    return (
                        ele?.actId?.indexOf(searchResult) >= 0 ||
                        ele?.actName?.indexOf(searchResult) >= 0
                    );
                }
                return true;
            });

            return filterActList.map((ele) => {
                const title = `${ele.actName}-${ele.actId}`;
                return (
                    <Option
                        // disabled={filterSelectList.includes(ele.actId)}
                        value={ele.actId}
                        key={ele.actId}
                    >
                        <span title={title}>{title}</span>
                    </Option>
                );
            });
        } else {
            return [];
        }
    }, [actList, searchResult]);

    return (
        <Modal
            title={title || '关联方案'}
            visible={showSelectModal}
            width={500}
            destroyOnClose
            onOk={confirmModal}
            onCancel={closeModal}
        >
            <Form form={actForm} name="actSelectForm">
                <FormItem noStyle name="index"></FormItem>
                <FormItem noStyle name="couponBagId"></FormItem>
                <FormItem label="配置关联方案" name="actId">
                    <Select
                        showSearch
                        allowClear
                        placeholder="未设置"
                        onSearch={handleSearch}
                        filterOption={false}
                    >
                        {filterSelectOptions}
                    </Select>
                </FormItem>
            </Form>
        </Modal>
    );
});

const RightsContentItem = (props, ref) => {
    const {
        label = '权益内容',
        name,
        snName, // 排序字段
        form,
        parentName, //父级name
        disabled,
        addEvent,
        equityNo,
        equityId,
        history,
        membershipModel: { membershipRightsInfo },
        isLock,
        ...otherOptions
    } = props;

    const [searchForm] = Form.useForm();

    const [rightsList, changeRightsList] = useState([]);

    const [actList, changeActList] = useState([]);

    useImperativeHandle(ref, () => {
        return {
            getActList: () => {
                return actList;
            },
        };
    });

    useEffect(() => {
        getVipPlanSelectList();
    }, []);

    const getVipPlanSelectList = async () => {
        try {
            const {
                data: { vip_plan_list },
            } = await getVipPlanSelectListApi({ equityNo, actSubType: '2702' });
            if (vip_plan_list instanceof Array) {
                changeActList(vip_plan_list);
            }
            return vip_plan_list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 控制列表是否需要排序功能
    const needSort = ['QY0003'].includes(equityNo);

    const [curEditItem, changeCurEditItem] = useState(undefined);
    const [filterOptions, changeFilterOptions] = useState({});

    const originList = Form.useWatch(name);

    const editModalRef = useRef();
    const cityModalRef = useRef();
    const addModalRef = useRef();
    const sortModalRef = useRef();

    const selectActRef = useRef();

    const filterList = useMemo(() => {
        return rightsList
            .filter((ele) => {
                // 如果没有筛选，直接返回true用于展示
                if (Object.values(filterOptions)?.length == 0) {
                    return true;
                }
                let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。
                for (const key in filterOptions) {
                    if (Object.hasOwnProperty.call(ele, key)) {
                        if (key != 'bgnTime' && key != 'endTime' && key != 'cityCodeList') {
                            //过滤其他
                            const element = filterOptions[key];
                            if (ele[key] != element) {
                                passFilter = false;
                                break;
                            }
                        }
                    }
                }
                if (
                    filterOptions.cityCodeList &&
                    filterOptions.cityCodeList instanceof Array &&
                    ele.cityCodeList instanceof Array &&
                    passFilter
                ) {
                    //过滤区域
                    const listCityKeys = ele.cityCodeList.map((ele) => ele.areaCode);
                    let mergeCity = [...new Set([...filterOptions.cityCodeList, ...listCityKeys])];
                    if (
                        mergeCity.length ==
                        listCityKeys?.length + filterOptions.cityCodeList.length
                    ) {
                        passFilter = false;
                    }
                }
                if (filterOptions.bgnTime && filterOptions.endTime && passFilter) {
                    //过滤日期
                    const isBetween =
                        ele.bgnTime?.length &&
                        ele.endTime?.length &&
                        (moment(filterOptions.bgnTime).isBetween(ele.bgnTime, ele.endTime) ||
                            moment(filterOptions.endTime).isBetween(ele.bgnTime, ele.endTime));

                    if (!isBetween) {
                        passFilter = false;
                    }
                }
                return passFilter;
            })
            .map((ele, index) => {
                ele.index = ele.index ? ele.index : index + 1;
                return ele;
            });
    }, [rightsList, filterOptions]);

    const canEditPrice = equityNo === RIGHTS_TYPES.ADD;

    useEffect(() => {
        if (originList instanceof Array) {
            changeAndSortRightsList(originList);
        }
    }, [originList]);

    useEffect(() => {
        sortModalRef.current?.init({ snList: membershipRightsInfo?.[snName] });
    }, [membershipRightsInfo]);

    const touchAddEvent = (isCpnPackage) => {
        // addEvent && addEvent();
        // 统计不可用的券包和城市
        const disabledCityKeys = [];
        const disabledCpnBagIds = [];
        // 基础券包权益、天天领红包权益：每个城市只能选择一个券包；已经配置过的城市，不可选。
        rightsList.map((ele) => {
            if (ele.equityStatus == '2') {
                // 已结束的券包可重新开放城市
                return;
            }
            // ele.cityCodeList?.map?.((cityEle) => {
            //     disabledCityKeys.push(cityEle.areaCode);
            // });
            disabledCpnBagIds.push(ele.couponBagId);
        });

        addModalRef.current.show({
            disabledCityKeys:
                ((equityNo == 'QY0002' || equityNo == 'QY0004') && disabledCityKeys) || [],
            disabledCpnBagIds,
            rightType: equityNo,
            isCpnPackage,
        });
    };

    // 跳转发放记录页面
    const sendRecordEvent = (item) => {
        history.push(`/userCenter/membership/rights/list/send-list?no=${item.couponBagNo}`);
    };

    const editEvent = (item, index) => {
        const otherCityCodes = [];
        const otherBags = [];
        rightsList.map((ele, otherIndex) => {
            if (otherIndex != index && ele.equityStatus != '2') {
                otherBags.push(ele.couponBagId);
                if (equityNo == 'QY0002' || equityNo == 'QY0004') {
                    // 基础券包权益、天天领红包权益：每个城市只能选择一个券包；已经配置过的城市，不可选。
                    // ele?.cityCodeList?.map((cityEle) => {
                    //     otherCityCodes.push(cityEle.areaCode);
                    // });
                }
            }
        });
        if (item.equityStatus == '1') {
            // 已添加过的场站，可新增不可删除
            const cityCodeList =
                membershipRightsInfo?.equityBagBoList?.find(
                    (ele) => ele.couponBagId == item.couponBagId,
                )?.cityCodeList || [];
            // otherCityCodes.push(...(cityCodeList.map((ele) => ele.areaCode) || []));
        }
        item.sn = undefined;
        editModalRef.current.show({
            value: item,
            disabledCityKeys: otherCityCodes,
            disabledBagIds: otherBags,
            disabled: item.equityStatus == '2',
        });
        changeCurEditItem(item);
    };

    const delEvent = (item) => {
        rightsList.splice(
            rightsList.findIndex((ele, index) => index + 1 === item.index),
            1,
        );
        const resultList = changeAndSortRightsList(rightsList);

        form.setFieldsValue({ [name]: resultList });

        if (needSort) {
            const newList = sortModalRef.current.deleteRightItem(item);
            form.setFieldsValue({ [snName]: newList });
        }
    };

    const changeAndSortRightsList = (list) => {
        const resultList = [
            ...list?.map((ele, index) => ({
                ...ele,
                index: index + 1,
            })),
        ];
        changeRightsList(resultList);
        return resultList;
    };

    const resetData = () => {
        searchForm.resetFields();
        searchData();
    };
    const searchData = () => {
        const params = searchForm.getFieldsValue();
        const options = {};
        if (params.cityCodeList?.length) {
            options.cityCodeList = params.cityCodeList;
        }
        if (params.equityStatus) {
            options.equityStatus = params.equityStatus;
        }
        if (params.couponBagStatus) {
            options.couponBagStatus = params.couponBagStatus;
        }
        if (params.dates) {
            options.bgnTime = params.dates?.[0]?.format('YYYY-MM-DD') || '';
            options.endTime = params.dates?.[1]?.format('YYYY-MM-DD') || '';
        }
        changeFilterOptions(options);
    };

    const columns = useMemo(() => {
        return [
            {
                title: '序号 ',
                width: 80,
                dataIndex: 'index',
                render(text, record, index) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '关联方案',
                width: 240,
                dataIndex: 'actName',
                render(text, record, index) {
                    const actId = record?.actId;
                    let resultText = '';

                    if (record.actName) {
                        resultText = `${record?.actName}-${record?.actId}`;
                        return (
                            <Link
                                to={`/userCenter/membership/plan/list/detail-vip/${record?.actId}`}
                                target="_blank"
                            >
                                {resultText}
                            </Link>
                        );
                        // return <span title={resultText}>{resultText}</span>;
                    }

                    if (actId) {
                        if (record.delFlag === '1') {
                            return `方案已删除-${actId}`;
                        }
                        const actInfo = actList.find((ele) => ele.actId === actId);
                        resultText = `${actInfo?.actName}-${actInfo?.actId}`;
                    }
                    if (!resultText && record.equityStatus === '2') {
                        return;
                    }

                    return (
                        <span
                            className={style['table-btn']}
                            style={{ marginLeft: 0, color: '#ff9901' }}
                            onClick={() => {
                                getVipPlanSelectList();
                                selectActRef.current?.open({
                                    actId,
                                    couponBagId: record?.couponBagId,
                                    couponBagName: record?.couponBagName,
                                    index,
                                });
                            }}
                        >
                            {resultText || '未设置'}
                        </span>
                    );

                    // let filterSelectList = filterList
                    //     .filter((ele) => {
                    //         return record.couponBagId !== ele.couponBagId;
                    //     })
                    //     .map((ele) => ele.actId);

                    // let filterSelectOptions = [];
                    // if (actList instanceof Array) {
                    //     filterSelectOptions = actList.map((ele) => {
                    //         const title = `${ele.actName}-${ele.actId}`;
                    //         return (
                    //             <Option
                    //                 // disabled={filterSelectList.includes(ele.actId)}
                    //                 value={ele.actId}
                    //                 key={ele.actId}
                    //             >
                    //                 <span title={title}>{title}</span>
                    //             </Option>
                    //         );
                    //     });
                    // }
                    // return (
                    //     <div className={style['card-col']}>
                    //         <Select
                    //             disabled={
                    //                 !(!isLock && (record?.equityStatus < 1 || !record?.actName))
                    //             }
                    //             value={actId}
                    //             onChange={(e) => {
                    //                 const editIndex = rightsList.findIndex(
                    // (ele) => ele.couponBagId === record.couponBagId,
                    // );
                    //                 const editItem = rightsList[editIndex];
                    //                 editItem.actId = e;

                    //                 changeRightsList([...rightsList]);
                    //             }}
                    //             allowClear={false}
                    //             placeholder="未设置"
                    //         >
                    //             {filterSelectOptions}
                    //         </Select>
                    //     </div>
                    // );

                    // return <span title={text}>{text || ''}</span>;
                },
            },
            {
                title: '投放区域',
                width: 200,
                render(text, record, index) {
                    const strs = [];
                    const codes = [];
                    record?.cityCodeList?.map((ele) => {
                        strs.push(ele.areaName);
                        codes.push(ele.areaCode);
                    });
                    const showStr = strs.slice(0, 2);
                    text = `${showStr.join(' | ')}${(strs.length > 2 && '...') || ''}`;
                    return (
                        (strs.length && (
                            <span
                                className={commonStyle['table-btn']}
                                onClick={() => {
                                    // 统计不可用的券包和城市
                                    const disabledCityKeys = [];
                                    if (equityNo == 'QY0002' || equityNo == 'QY0004') {
                                        // 基础券包权益、天天领红包权益：每个城市只能选择一个券包；已经配置过的城市，不可选。
                                        rightsList.map((ele) => {
                                            if (
                                                ele.couponBagId != record.couponBagId &&
                                                ele.equityStatus != 2
                                            ) {
                                                // 已结束的券包要放开城市选择
                                                // ele.cityCodeList?.map?.((cityEle) => {
                                                //     disabledCityKeys.push(cityEle.areaCode);
                                                // });
                                            }
                                        });
                                    }
                                    if (record.equityStatus == '1' || record.equityStatus == '2') {
                                        // 已添加过的场站，可新增不可删除。根据进行中、已结束的券包id来找原数据
                                        const cityCodeList =
                                            membershipRightsInfo?.equityBagBoList?.find(
                                                (ele) => ele.couponBagId == record.couponBagId,
                                            )?.cityCodeList || [];
                                        // disabledCityKeys.push(
                                        //     ...(cityCodeList.map((ele) => ele.areaCode) || []),
                                        // );
                                    }
                                    record.sn = undefined;
                                    cityModalRef?.current?.show({
                                        defaultKeys: codes,
                                        disabledKeys: disabledCityKeys,
                                        disabled: isLock || record.equityStatus == '2', // 查看和已结束都不允许再选择区域
                                    });
                                    changeCurEditItem(record);
                                }}
                            >
                                <div
                                    style={{
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        display: '-webkit-box',
                                        WebkitBoxOrient: 'vertical',
                                        WebkitLineClamp: '1',
                                    }}
                                    title={strs?.join('、') || undefined}
                                >
                                    {text}
                                </div>
                            </span>
                        )) || <span>-</span>
                    );
                },
            },

            {
                title: '券包名称',
                width: 200,
                dataIndex: 'couponBagName',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '券包模板ID',
                width: 200,
                dataIndex: 'couponBagNo',
                render(text, record) {
                    if (record?.couponBagId) {
                        return (
                            <Link
                                key={'01'}
                                to={`/userCenter/membership/rights/list/coupon-bags-list/detail/${record.couponBagId}`}
                                target="_blank"
                            >
                                {text}
                            </Link>
                        );
                    }
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '券包数量',
                width: 120,
                dataIndex: 'couponBagNum',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '单包券数',
                width: 120,
                dataIndex: 'singleNum',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '单包价值（元）',
                width: 160,
                dataIndex: 'singleAmt',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            ...(canEditPrice
                ? [
                      {
                          title: '售价（元）',
                          width: 160,
                          dataIndex: 'sellAmt',
                          render(text, record, index) {
                              if (!text) {
                                  return (
                                      <a
                                          onClick={() => {
                                              editEvent(record, index);
                                          }}
                                      >
                                          未设置
                                      </a>
                                  );
                              }
                              return <span title={text}>{text}</span>;
                          },
                      },
                  ]
                : []),

            {
                title: '券包状态',
                width: 120,
                dataIndex: 'couponBagStatusName',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },

            {
                title: '开始时间',
                width: 220,
                fixed: 'right',
                dataIndex: 'bgnTime',
                render(text, record, index) {
                    if (!isLock && record?.equityStatus < 1) {
                        const bgnTime =
                            record?.bgnTime && moment(record?.bgnTime, 'YYYY-MM-DD HH:mm:ss');
                        return (
                            <div className={style['card-col']}>
                                <DatePicker
                                    bordered={false}
                                    showTime
                                    showNow={false}
                                    value={bgnTime}
                                    onOk={(e) => {
                                        const editIndex = rightsList.findIndex(
                                            (ele, index) => index + 1 === record.index,
                                        );
                                        const editItem = rightsList[editIndex];
                                        editItem.bgnTime = e.format('YYYY-MM-DD HH:mm:ss');

                                        const endTime =
                                            record?.endTime &&
                                            moment(record?.endTime, 'YYYY-MM-DD HH:mm:ss');
                                        if (e > endTime) {
                                            editItem.endTime = undefined;
                                        }
                                        changeAndSortRightsList([...rightsList]);
                                    }}
                                    disabledDate={(current) => {
                                        if (current) {
                                            const currentTime = moment();
                                            // 以当前时间做参考
                                            return current < currentTime;
                                        }
                                        return false;
                                    }}
                                    allowClear={false}
                                    placeholder="未设置"
                                />
                            </div>
                        );
                    }
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '结束时间',
                width: 220,
                fixed: 'right',
                dataIndex: 'endTime',
                render(text, record, index) {
                    if (!isLock && record?.equityStatus != '2') {
                        const bgnTime =
                            record?.bgnTime && moment(record?.bgnTime, 'YYYY-MM-DD HH:mm:ss');
                        const endTime =
                            record?.endTime && moment(record?.endTime, 'YYYY-MM-DD HH:mm:ss');
                        return (
                            <div className={style['card-col']}>
                                <DatePicker
                                    bordered={false}
                                    showTime
                                    showNow={false}
                                    value={endTime}
                                    onOk={(e) => {
                                        const editIndex = rightsList.findIndex(
                                            (ele, index) => index + 1 === record.index,
                                        );
                                        const editItem = rightsList[editIndex];
                                        editItem.endTime = e.format('YYYY-MM-DD HH:mm:ss');
                                        changeAndSortRightsList([...rightsList]);
                                    }}
                                    disabledDate={(current) => {
                                        if (current) {
                                            const currentTime = moment();
                                            // 只能晚于开始时间或者当前时间中的最晚时间
                                            if (bgnTime > currentTime) {
                                                // 如果开始时间晚于当前时间，以开始时间做参考
                                                return current < bgnTime;
                                            }
                                            // 以当前时间做参考
                                            return current < currentTime;
                                        }
                                        return false;
                                    }}
                                    allowClear={false}
                                    placeholder="未设置"
                                    disabled={!bgnTime}
                                />
                            </div>
                        );
                    }
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '权益状态',
                fixed: 'right',
                width: 120,
                dataIndex: 'equityStatusName',
                render(text, record) {
                    if (!text?.length) {
                        text = '-';
                    }
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '操作',
                fixed: 'right',
                width: 140,
                render: (text, record, index) => {
                    const editBtn = (
                        <span
                            className={commonStyle['table-btn']}
                            onClick={() => {
                                editEvent(record, index);
                            }}
                        >
                            编辑
                        </span>
                    );

                    const lookBtn = (
                        <span
                            className={commonStyle['table-btn']}
                            onClick={() => sendRecordEvent(record)}
                            style={{ color: '#ff9901' }}
                        >
                            发放记录
                        </span>
                    );

                    const delBtn = (
                        <Popconfirm
                            title="要从列表中删除此券包？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => delEvent(record)}
                        >
                            <span className={commonStyle['table-btn']} style={{ color: '#f50' }}>
                                删除
                            </span>
                        </Popconfirm>
                    );

                    let btns = [];
                    // equityStatus：0 未开始 1 进行中 2 已结束
                    if (isLock) {
                        btns.push(lookBtn);
                    } else if (record.equityStatus === '0') {
                        //未开始 或 未提交
                        btns.push(editBtn);
                        btns.push(delBtn);
                    } else if (record.equityStatus == '1') {
                        btns.push(editBtn);
                        btns.push(lookBtn);
                    } else {
                        btns.push(lookBtn);
                    }

                    return <Space>{btns}</Space>;
                },
            },
        ];
    });

    const cityRequest =
        equityNo == 'QY0002'
            ? undefined
            : async (params = {}) => {
                  try {
                      const {
                          data: { areaList },
                      } = await getVipPlanEquityCityApi(params);

                      return areaList;
                  } catch (error) {
                      return [];
                  }
              };

    return (
        <Fragment>
            <FormItem name={snName} noStyle />
            <FormItem label={label} name={name} {...otherOptions}>
                <SearchLayout
                    {...props}
                    form={searchForm}
                    onSubmit={() => {
                        searchData();
                    }}
                    onReset={resetData}
                />
                {((!isLock || needSort) && (
                    <Space className={commonStyle['btn-bar']}>
                        {(!isLock &&
                            (equityNo === RIGHTS_TYPES.COUPON ? (
                                <Popconfirm
                                    title="是否配置券包"
                                    okText="是"
                                    cancelText="否"
                                    onCancel={() => touchAddEvent(0)}
                                    onConfirm={() => touchAddEvent(1)}
                                >
                                    <Button type="primary">
                                        <PlusOutlined />
                                        新增权益
                                    </Button>
                                </Popconfirm>
                            ) : (
                                <Button onClick={() => touchAddEvent(1)} type="primary">
                                    <PlusOutlined />
                                    新增权益
                                </Button>
                            ))) ||
                            null}
                        {(needSort && (
                            <Button
                                onClick={() => {
                                    const oldSnList = form.getFieldValue(snName) || [];
                                    if (!oldSnList?.length) {
                                        message.error('无可排序内容，请先新增权益');
                                        return;
                                    }
                                    sortModalRef.current.show({
                                        oldSnList: [...oldSnList],
                                    });
                                }}
                            >
                                <OrderedListOutlined />
                                排序
                            </Button>
                        )) ||
                            null}
                    </Space>
                )) ||
                    null}
                <TablePro
                    name="rights"
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={filterList}
                    columns={columns}
                />
            </FormItem>

            <EditCouponBagModal
                {...props}
                canEditPrice={canEditPrice}
                onFinish={(values) => {
                    const newItem = { ...curEditItem, ...values };
                    const editIndex = rightsList.findIndex(
                        (ele, index) => index + 1 === curEditItem.index,
                    );
                    rightsList.splice(editIndex, 1, newItem);
                    changeAndSortRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: rightsList });
                    // 编辑券包项处理排序
                    if (needSort) {
                        const newList = sortModalRef.current.editRightItem(curEditItem, newItem);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                ref={editModalRef}
                cityRequest={cityRequest}
            />

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    // 编辑城市处理排序
                    const newItem = { ...curEditItem, cityCodeList: citys };
                    rightsList.splice(
                        rightsList.findIndex((ele, index) => index + 1 === curEditItem.index),
                        1,
                        newItem,
                    );
                    changeAndSortRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: rightsList });
                    if (needSort) {
                        const newList = sortModalRef.current.editRightItem(curEditItem, newItem);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                request={cityRequest}
            />

            <AddRightModal
                {...props}
                ref={addModalRef}
                onFinish={(value) => {
                    // 插入的直接叫未开始
                    rightsList.splice(0, 0, {
                        ...value,
                        equityStatus: '0',
                        equityStatusName: '未开始',
                    });
                    rightsList.map((ele, index) => {
                        ele.index = index + 1;
                        return ele;
                    });
                    const resultList = changeAndSortRightsList([...rightsList]);
                    form.setFieldsValue({ [name]: resultList });
                    if (needSort) {
                        const newList = sortModalRef.current.addRightItem(value);
                        form.setFieldsValue({ [snName]: newList });
                    }
                }}
                cityRequest={cityRequest}
            />

            <SortRightModal
                {...props}
                ref={sortModalRef}
                onFinish={(newSnList) => {
                    form.setFieldsValue({ [snName]: newSnList });
                }}
                disabled={isLock}
            />
            <SelectActModal
                ref={selectActRef}
                actList={actList}
                onConfirm={(info) => {
                    const { couponBagId, actId, index } = info;
                    if (index >= 0) {
                        const editItem = rightsList[index];
                        editItem.actId = actId;

                        const resultList = changeAndSortRightsList([...rightsList]);
                        form.setFieldsValue({ [name]: resultList });
                    }
                }}
            ></SelectActModal>
        </Fragment>
    );
};

export default forwardRef(RightsContentItem);
