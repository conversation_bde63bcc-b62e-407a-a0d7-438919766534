import { InfoCircleOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
    InputNumber,
    Form,
    Space,
    Divider,
    Button,
    Row,
    Col,
    Tabs,
    Card,
    Modal,
    Typography,
    Tooltip,
} from 'antd';
import { Fragment, useState, useMemo, useRef, useImperativeHandle } from 'react';
import { connect } from 'umi';

import styles from '../../Membership.less';
import { getStationScopeListApi, saveStationScopeInfoApi } from '@/services/CommonApi';
import { STATION_CONFIG_PAGE_TYPES } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import { getVipPlanEquityCityApi } from '@/services/Marketing/MarketingMembershipApi';

import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import CityTransferModal from '@/components/CityTransferModal';
import { RIGHTS_TYPES } from '../RightsConfig';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { confirm } = Modal;
const { Text } = Typography;

export const STATION_PAGE_TYPES = {
    VIP: '00', // 会员权益
    PLATFORM_ACTIVE: '01', // 平台活动
};

// 活动信息
export const RightsStationLayout = (props) => {
    const {
        id,
        cityName = 'cityList',
        form,
        isLock,
        formItemLayout,
        pageType = STATION_PAGE_TYPES.VIP,
        initRef,
        equityNo,
    } = props;

    const cityModalRef = useRef();
    const [editIndex, setEditIndex] = useState();

    const pageInfo = useMemo(() => {
        const info = {
            label: undefined,
            tab1: undefined,
            tab2: undefined,
            scene: undefined,
            scopeRelateType: undefined,
            cityRequest: undefined,
        };
        switch (pageType) {
            case STATION_PAGE_TYPES.VIP:
                info.label = equityNo === RIGHTS_TYPES.MEMBER_DAY ? '活动场站' : '特价场站';
                info.tab1 = '一般规则';
                info.tab2 = '定向规则';
                info.scene = 'stc_member';
                if (equityNo === RIGHTS_TYPES.MEMBER_DAY) {
                    info.scopeRelateType = STATION_CONFIG_PAGE_TYPES.MEMBER_DAY;
                } else {
                    info.scopeRelateType = STATION_CONFIG_PAGE_TYPES.MEMBER_PRICE_STATION;
                }
                info.cityRequest = async (params = {}) => {
                    try {
                        const {
                            data: { areaList },
                        } = await getVipPlanEquityCityApi(params);

                        return areaList;
                    } catch (error) {
                        return [];
                    }
                };
                break;
            case STATION_PAGE_TYPES.PLATFORM_ACTIVE:
                info.label = '活动场站';
                info.tab1 = '会员站配置';
                info.tab2 = '自定义配置';
                info.scene = 'stc_platform';
                info.scopeRelateType = STATION_CONFIG_PAGE_TYPES.TURN;
                break;
            default:
                break;
        }
        return info;
    }, [pageType]);

    useImperativeHandle(initRef, () => ({
        getSumbitKeys: async () => {
            // 获取提交需要的keys值，内部实现校验和提交
            const errSet = [];
            const resItem = {};
            const values = form.getFieldsValue();

            // 活动推广页，只要两个tab填完整了其中的一个，就支持提交，所以在rules基础上继续判断
            if (values.ruleType == '2') {
                // 当前选中自定义配置，判断会员站配置的内容有没有完整
                const ruleList = values?.[cityName];
                ruleList.map((ele, index) => {
                    const radio = ele['recommendDisLimit'];
                    const city = ele['city'];
                    const tempErr = [];
                    if (radio === null || radio === undefined || !city?.length) {
                        if (ruleList?.length == 1) {
                            // 如果仅配置了一条，区域和比例填了一项则另一项必填
                            if (radio && !city?.length) {
                                tempErr.push('支持区域未选择');
                            } else if ((radio === null || radio === undefined) && city?.length) {
                                tempErr.push('优惠比例未填写');
                            }
                        } else if (ruleList?.length > 1) {
                            // 如果配置了多条，每项数据都必填
                            if (!city?.length) {
                                tempErr.push('支持区域未选择');
                            }
                            if (radio === null || radio === undefined) {
                                tempErr.push('优惠比例未填写');
                            }
                        }
                    }
                    if (tempErr?.length) {
                        errSet.push(`序号${index + 1}：${tempErr.join('、')}`);
                    }
                });
                if (errSet.length) {
                    // const newKeys = Array.from(new Set(errSet));
                    confirm({
                        title: `“${pageInfo.tab1}”配置信息请补充完整：`,
                        content: (
                            <Text style={{ whiteSpace: 'pre' }}>{`${errSet.join('；\n')}。`}</Text>
                        ),
                        okText: '我知道了',
                        cancelText: '取消',
                    });
                    return Promise.reject('');
                }
            }
            if (values[cityName]) {
                const disCityList = values?.[cityName]?.map((ele) => {
                    const cityList = ele.city?.map((cityChild) => cityChild.areaCode) || [];
                    return {
                        limit: ele.recommendDisLimit,
                        cityList: cityList.filter((city) => city),
                    };
                });
                resItem.params = disCityList;
            }
            if (pageType == STATION_PAGE_TYPES.PLATFORM_ACTIVE || values.ruleType == '2') {
                if (values.stationConfig) {
                    const stationOptions = {};
                    const {
                        addStations = [],
                        allStations = [],
                        defaultStations = [],
                        delStations = [],
                    } = values.stationConfig || {};

                    stationOptions.addStationIds = addStations.map((ele) => ele.stationId);
                    stationOptions.submitStationIds = allStations.map((ele) => ele.stationId);
                    stationOptions.delStationIds = delStations.map((ele) => ele.stationId);
                    if (
                        stationOptions.addStationIds?.length ||
                        stationOptions.submitStationIds?.length ||
                        stationOptions.delStationIds?.length
                    ) {
                        // 场站列表有值才调场站配置接口
                        const {
                            data: { stationScopeKey },
                        } = await saveStationScopeInfoApi(stationOptions);

                        resItem.stationScopeKey = stationScopeKey;
                    } else if (pageType == STATION_PAGE_TYPES.VIP) {
                    }
                }
            }
            return Promise.resolve(resItem);
        },
    }));

    const renderRow = ({ field, index, actType, add, remove }) => {
        const style = {
            // height: '42px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
        };
        const dividerStyle = {
            height: style.height,
            width: '2px',
            margin: 0,
        };

        return (
            <>
                <Row>
                    <Col flex={'10%'} style={style}>
                        {index + 1}
                    </Col>
                    <Divider type="vertical" style={dividerStyle} />
                    <Col flex={'30%'} style={style}>
                        <Form.Item noStyle shouldUpdate={() => true}>
                            {({ getFieldValue }) => {
                                const ruleList = getFieldValue(cityName);
                                const currentCities = [];
                                ruleList?.map((ele = {}, curIndex) => {
                                    if (curIndex == index) {
                                        ele.city?.map((cityEle) => {
                                            if (cityEle) {
                                                currentCities.push(cityEle.areaCode);
                                            }
                                        });
                                    }
                                });
                                const otherKeys = [];
                                ruleList?.map((ele = {}, otherIndex) => {
                                    if (otherIndex !== index) {
                                        ele.city?.map((cityEle) => {
                                            if (cityEle) {
                                                otherKeys.push(cityEle.areaCode);
                                            }
                                        });
                                    }
                                });
                                return (
                                    <FormItem
                                        name={[index, 'city']}
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    const curTabIndex = getFieldValue('ruleType');
                                                    if (!value?.length) {
                                                        const ruleList = getFieldValue(cityName);
                                                        if (curTabIndex == '1') {
                                                            // 判断是否在当前tab上，如否，交给submit方法判断
                                                            if (ruleList?.length == 1) {
                                                                // 如果仅配置了一条，区域和比例填了一项则另一项必填
                                                                const radio =
                                                                    ruleList?.[index]?.[
                                                                        'recommendDisLimit'
                                                                    ];
                                                                if (radio) {
                                                                    return Promise.reject(
                                                                        '请选择城市',
                                                                    );
                                                                }
                                                            }
                                                            if (
                                                                pageType ==
                                                                STATION_PAGE_TYPES.PLATFORM_ACTIVE
                                                            ) {
                                                                const stationConfig =
                                                                    getFieldValue('stationConfig');
                                                                if (
                                                                    stationConfig?.allStations
                                                                        ?.length
                                                                ) {
                                                                    // 如果另一个配置了数据，就不强校验了
                                                                    return Promise.resolve();
                                                                }
                                                                return Promise.reject('请选择城市');
                                                            }
                                                            if (ruleList?.length > 1) {
                                                                // 如果配置了多条，每项数据都必填
                                                                return Promise.reject('请选择城市');
                                                            }
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        style={{ margin: '12px 0' }}
                                    >
                                        {(currentCities?.length && (
                                            <Space size="small">
                                                {(!isLock && (
                                                    <Button
                                                        type="link"
                                                        onClick={() => {
                                                            cityModalRef?.current?.show({
                                                                defaultKeys: currentCities,
                                                                disabledKeys: otherKeys,
                                                            });
                                                            setEditIndex(index);
                                                        }}
                                                    >
                                                        编辑
                                                    </Button>
                                                )) ||
                                                    null}
                                                <Button
                                                    type="link"
                                                    onClick={() =>
                                                        cityModalRef?.current?.show({
                                                            defaultKeys: currentCities,
                                                            disabled: true,
                                                        })
                                                    }
                                                >
                                                    查看
                                                </Button>
                                            </Space>
                                        )) ||
                                            (!isLock && (
                                                <Button
                                                    type="link"
                                                    onClick={() => {
                                                        cityModalRef?.current?.show({
                                                            defaultKeys: currentCities,
                                                            disabledKeys: otherKeys,
                                                        });
                                                        setEditIndex(index);
                                                    }}
                                                >
                                                    添加
                                                </Button>
                                            )) ||
                                            '-'}
                                    </FormItem>
                                );
                            }}
                        </Form.Item>
                    </Col>
                    <Divider type="vertical" style={dividerStyle} />
                    <Col flex={'20%'} style={style}>
                        <Form.Item noStyle>
                            <Space>
                                <div style={style}>{'>'}</div>
                                <FormItem
                                    name={[field.name, 'recommendDisLimit']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const curTabIndex = getFieldValue('ruleType');
                                                if (value === null || value === undefined) {
                                                    const ruleList = getFieldValue(cityName);
                                                    if (curTabIndex == '1') {
                                                        // 判断是否在当前tab上，如否，交给submit方法判断
                                                        if (ruleList?.length == 1) {
                                                            // 如果仅配置了一条，区域和比例填了一项则另一项必填
                                                            const city =
                                                                ruleList?.[index]?.['city'];
                                                            if (city?.length) {
                                                                return Promise.reject(
                                                                    '请配置优惠比例',
                                                                );
                                                            }
                                                        }
                                                        if (
                                                            pageType ==
                                                            STATION_PAGE_TYPES.PLATFORM_ACTIVE
                                                        ) {
                                                            const stationConfig =
                                                                getFieldValue('stationConfig');
                                                            if (
                                                                stationConfig?.allStations?.length
                                                            ) {
                                                                // 如果另一个配置了数据，就不强校验了
                                                                return Promise.resolve();
                                                            }
                                                            return Promise.reject('请配置优惠比例');
                                                        }
                                                        if (ruleList?.length > 1) {
                                                            // 如果配置了多条，每项数据都必填
                                                            return Promise.reject('请配置优惠比例');
                                                        }
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    style={{ margin: '12px 0' }}
                                >
                                    <InputNumber
                                        style={{
                                            width: '150px',
                                        }}
                                        min={0}
                                        disabled={isLock || actType == 0}
                                        placeholder="请输入"
                                    />
                                </FormItem>
                            </Space>
                        </Form.Item>
                    </Col>
                    <Divider type="vertical" style={dividerStyle} />
                    {(!isLock && actType != 0 && (
                        <Col>
                            <div
                                style={{
                                    ...style,
                                    justifyContent: undefined,
                                    height: '100%',
                                }}
                            >
                                {(index == 0 && (
                                    <PlusCircleOutlined
                                        className={styles['dynamic-delete-button']}
                                        style={{
                                            margin: '0 8px',
                                            color: '#0A89FF',
                                            fontSize: '24px',
                                        }}
                                        onClick={() => {
                                            add();
                                        }}
                                    />
                                )) || (
                                    <MinusCircleOutlined
                                        className={styles['dynamic-delete-button']}
                                        style={{
                                            margin: '0 8px',
                                            color: '#f50000',
                                            fontSize: '24px',
                                        }}
                                        onClick={() => {
                                            remove(field.name);
                                        }}
                                    />
                                )}
                            </div>
                        </Col>
                    )) ||
                        null}
                </Row>
                <Divider style={{ margin: 0 }} />
            </>
        );
    };

    return (
        <Fragment>
            <FormItem name="ruleType" noStyle initialValue="1" />
            <FormItem label={pageInfo.label} {...formItemLayout} required>
                <Tabs
                    type="card"
                    onChange={(e) => {
                        form.setFieldsValue({ ruleType: e });
                    }}
                >
                    <TabPane tab={pageInfo.tab1} key="1">
                        <Card
                            size="small"
                            title={
                                <Row>
                                    <Col flex="10%">
                                        <div style={{ textAlign: 'center' }}>序号</div>
                                    </Col>
                                    <Col flex="30%">
                                        <div style={{ textAlign: 'center' }}>支持区域</div>
                                    </Col>
                                    <Col flex="20%">
                                        <div style={{ textAlign: 'center' }}>
                                            优惠比例
                                            <Tooltip title="会员优惠金额/原价=优惠比例">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </div>
                                    </Col>
                                    <Col flex="auto"></Col>
                                </Row>
                            }
                            type="inner"
                            bodyStyle={{ padding: 0 }}
                            headStyle={{ padding: 0 }}
                        >
                            <Form.List name={cityName} initialValue={[{}]}>
                                {(fields, { add, remove }) => (
                                    <Fragment>
                                        {fields.map((field, index) => (
                                            <Fragment key={index}>
                                                <Form.Item
                                                    shouldUpdate={(prevValues, curValues) => true}
                                                    noStyle
                                                >
                                                    {({ getFieldValue }) => {
                                                        const actType = getFieldValue('actType');
                                                        // 限制
                                                        return renderRow({
                                                            field,
                                                            index,
                                                            actType,
                                                            add,
                                                            remove,
                                                        });
                                                    }}
                                                </Form.Item>
                                            </Fragment>
                                        ))}
                                    </Fragment>
                                )}
                            </Form.List>
                        </Card>
                    </TabPane>
                    <TabPane tab={pageInfo.tab2} key="2">
                        <FormItem
                            // label="活动范围"
                            // required
                            name={'stationConfig'}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        const curTabIndex = getFieldValue('ruleType');
                                        // 判断是否在当前tab上，如否，交给submit方法判断

                                        if (curTabIndex == '2') {
                                            const { addStations, allStations } = value || {};
                                            if (
                                                isEmpty(value) ||
                                                (isEmpty(allStations) && isEmpty(addStations))
                                            ) {
                                                if (
                                                    pageType == STATION_PAGE_TYPES.PLATFORM_ACTIVE
                                                ) {
                                                    const ruleList = getFieldValue(cityName) || [];
                                                    for (const rule of ruleList) {
                                                        if (
                                                            rule.city?.length > 0 &&
                                                            !(
                                                                rule.recommendDisLimit === null ||
                                                                rule.recommendDisLimit === undefined
                                                            )
                                                        ) {
                                                            // 如果另一个配置了数据，就不强校验了
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                }
                                                return Promise.reject('请配置指定场站');
                                            }
                                            if (
                                                addStations.length > 20000 ||
                                                allStations.length > 20000
                                            ) {
                                                return Promise.reject('一次性最多配置2万个站点');
                                            }
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <SearchStationItem
                                disabled={isLock}
                                requestInfo={
                                    id && {
                                        listApi: getStationScopeListApi,
                                        params: {
                                            scopeRelateId: id,
                                            scopeRelateType: pageInfo.scopeRelateType,
                                        },
                                        recordParams: {
                                            relateId: id,
                                            scene: pageInfo.scene,
                                        },
                                    }
                                }
                            ></SearchStationItem>
                        </FormItem>
                    </TabPane>
                </Tabs>
            </FormItem>

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    const temp_channelList = form.getFieldValue(cityName) || [];
                    temp_channelList[editIndex] = { ...temp_channelList[editIndex], city: citys };
                    form.setFieldsValue({ [cityName]: temp_channelList });
                }}
                request={pageInfo?.cityRequest}
            />
        </Fragment>
    );
};

export default connect(({ global, membershipModel, couponModel }) => ({
    global,
    membershipModel,
    couponModel,
}))(RightsStationLayout);
