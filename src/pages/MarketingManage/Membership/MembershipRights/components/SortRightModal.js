import { Modal, Form, Col, Space, Button, InputNumber, Typography } from 'antd';
import React, { useState, useImperativeHandle, useMemo, useEffect, forwardRef } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import CitysSelect from '@/components/CitysSelect/index.js';
import { copyObjectCommon } from '@/utils/utils';

const { confirm } = Modal;
const { Text } = Typography;

const formItemLayout = {};

// 排序弹窗
const SearchLayout = (props) => {
    const { listLoading, onSubmit, onReset } = props;

    const [form] = Form.useForm();

    const onFinish = (values) => {
        onSubmit(values['cityCodeList']);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form name="rights-search" {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} form={form}>
                <Col span={16}>
                    <CitysSelect
                        label="投放区域"
                        name="cityCodeList"
                        placeholder="请选择"
                        formItemLayout={formItemLayout}
                        showArrow
                        provinceSelectable
                        rules={null}
                    />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

/**
 * 排序规则
 * ·以城市维度互斥排序值，增加/编辑券包时考虑区域归集
 * ·删除券包：删除券包id下的排序列表项
 * ·增加券包：分别找到各城市的最大排序值，自动+1存储
 * ·编辑券包：先判断城市+券包是否已变更，不处理未变的情况，如果变更，从原排序列表删除旧城市，当做增加处理
 */
const SortRightModal = (props, ref) => {
    const { onFinish, disabled } = props;

    const [modalVisible, toggleModalVisible] = useState(false); // 导入弹窗状态
    const [equityBagSnBoList, updateEquityBagSnBoList] = useState([]);
    const [oldEquityBagSnBoList, updateOldEquityBagSnBoList] = useState([]); // 记录弹窗前的列表，如果点取消，还原回去
    const [sortForm] = Form.useForm(); // 仅仅需要用到form的校验样式
    useImperativeHandle(ref, () => ({
        init: ({ snList = [] } = {}) => {
            // 需要初始化，才能在当前当中操作新增、删除等内部方法
            if (Array.isArray(snList)) {
                updateEquityBagSnBoList([...snList]);
            }
        },
        show: ({ oldSnList }) => {
            updateOldEquityBagSnBoList(copyObjectCommon(oldSnList));
            toggleModalVisible(true);
        },
        // 新增权益后，按城市维度拆解成sn对象
        addRightItem: (newRight = {}) => {
            newRight?.cityCodeList?.forEach((ele) => {
                equityBagSnBoList.push({ ...newRight, city: ele.areaCode, cityName: ele.areaName });
            });
            insertDefaultSN();

            updateEquityBagSnBoList([...equityBagSnBoList]);
            return equityBagSnBoList;
        },
        // 编辑权益，需要传编辑前后的对象
        editRightItem: (preItem = {}, currentItem = {}) => {
            const couponBagId = preItem.couponBagId;
            if (couponBagId) {
                // 记录新的关联城市列表，用于后面判断是否在原有列表已存在
                // 根据券包id找到所有之前的城市
                const oldCitys = [];
                equityBagSnBoList?.forEach((cityEle) => {
                    if (cityEle?.couponBagId == preItem.couponBagId) {
                        if (
                            cityEle?.couponBagId == currentItem.couponBagId &&
                            currentItem.cityCodeList?.some(
                                (curCode) => curCode.areaCode == cityEle.city,
                            )
                        ) {
                            // 过滤掉完全没变化的城市信息，目前只判断券包和城市
                            // 要更新售价和单包价值
                            cityEle.sellAmt = currentItem.sellAmt;
                            cityEle.singleAmt = currentItem.singleAmt;
                        } else {
                            oldCitys.push(cityEle);
                        }
                    }
                });
                // 拿到旧数据后，结合新数据，先做减法
                const newCitys = currentItem.cityCodeList;
                oldCitys.forEach((cityEle) => {
                    if (
                        !newCitys.some((curCode) => curCode.areaCode == cityEle.city) ||
                        cityEle.couponBagId != currentItem.couponBagId
                    ) {
                        // 新券包已不包含当前城市，或者券包已经变更
                        const targetIndex = equityBagSnBoList.findIndex(
                            (ele) =>
                                ele.couponBagId == cityEle.couponBagId && ele.city == cityEle.city,
                        );
                        equityBagSnBoList.splice(targetIndex, 1);
                    }
                });
                // 后按正常逻辑做加法
                newCitys.map((ele) => {
                    const targetIndex = equityBagSnBoList.findIndex(
                        (oldEle) =>
                            oldEle.city == ele.areaCode &&
                            oldEle.couponBagId == currentItem.couponBagId,
                    );

                    if (targetIndex < 0) {
                        // 绕过旧的数据
                        equityBagSnBoList.push({
                            ...currentItem,
                            city: ele.areaCode,
                            cityName: ele.areaName,
                        });
                    }
                });
                insertDefaultSN();
                updateEquityBagSnBoList([...equityBagSnBoList]);
            } else {
                insertDefaultSN();
            }
            return equityBagSnBoList;
        },
        // 删除权益
        deleteRightItem: (rightItem) => {
            const couponBagId = rightItem?.couponBagId;
            if (couponBagId) {
                const oldSnItems = equityBagSnBoList.filter(
                    (ele) => ele.couponBagId == couponBagId,
                );
                for (const item of oldSnItems) {
                    equityBagSnBoList.splice(equityBagSnBoList.indexOf(item), 1);
                }
            }
            updateEquityBagSnBoList([...equityBagSnBoList]);
            return equityBagSnBoList;
        },
        onClose,
    }));

    // 插入默认排序
    const insertDefaultSN = () => {
        // 预设sn排序下标
        const list = getNewestFormatList();
        // 最后再检查一次，切记从后往前排，避免前面的排序被更改
        list?.forEach((ele) => {
            const targetList = ele?.cityList || [];
            // 切记从后往前排，避免前面的排序被更改
            for (let index = targetList.length - 1; index >= 0; index--) {
                const cityEle = targetList[index];
                if (
                    !cityEle.sn ||
                    targetList.filter((otherEle) => otherEle.sn == cityEle.sn)?.length > 1
                ) {
                    // 未参与过排序，或者排序有冲突
                    let maxSn = 0;
                    targetList.map((snEle) => {
                        if (snEle.sn?.length) {
                            maxSn = Math.max(parseInt(snEle.sn), maxSn);
                        }
                    });
                    cityEle.sn = `${++maxSn}`;
                }
            }
        });
    };

    const submitEvent = () => {
        // 以弹窗形式反馈冲突
        const errArr = [];
        citySnList?.forEach((ele) => {
            const citys = ele.cityList;
            const tempErrs = [];
            citys?.forEach((cityEle) => {
                if (!cityEle.sn?.length) {
                    tempErrs.push('有前端排序未填写');
                } else if (
                    citys.some(
                        (otherEle) =>
                            otherEle.couponBagNo != cityEle.couponBagNo &&
                            otherEle.sn == cityEle.sn,
                    )
                ) {
                    tempErrs.push('排序冲突');
                }
            });
            if (tempErrs.length) {
                let err = [...new Set(tempErrs)];
                errArr.push(`${ele.cityName}：${err.join('、')}`);
            }
        });

        if (errArr?.length) {
            confirm({
                title: `排序更改失败`,
                content: <Text style={{ whiteSpace: 'pre' }}>{`${errArr.join('；\n')}。`}</Text>,
                okText: '我知道了',
                cancelText: '取消',
            });
        }

        sortForm.validateFields().then(() => {
            if (!errArr?.length) {
                onFinish?.(equityBagSnBoList);
                onClose();
            }
        });
    };

    const columns = [
        {
            title: '前端排序',
            width: 100,
            dataIndex: 'sn',
            render(text, record, index) {
                if (disabled) {
                    return <span title={text}>{text}</span>;
                }
                return (
                    <Form.Item
                        name={[index, 'sn']}
                        rules={[
                            () => ({
                                validator(rule, value) {
                                    // 同一城市&不同券包的排序不可重复
                                    const double = equityBagSnBoList.find(
                                        (ele) =>
                                            ele.equityStatus != 2 &&
                                            ele.city == record.city &&
                                            ele.couponBagId != record.couponBagId &&
                                            ele.sn == value,
                                    );
                                    if (double) {
                                        return Promise.reject('排序重复');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        style={{ margin: 0 }}
                    >
                        <InputNumber
                            onChange={(e) => {
                                const oldItem = equityBagSnBoList.find(
                                    (ele) =>
                                        ele.city == record.city &&
                                        ele.couponBagId == record.couponBagId,
                                );
                                oldItem.sn = `${e || 1}`;
                                updateEquityBagSnBoList([...equityBagSnBoList]);
                            }}
                        />
                    </Form.Item>
                );
            },
        },
        {
            title: '投放区域',
            width: 100,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包名',
            width: 200,
            dataIndex: 'couponBagName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包模板ID',
            width: 130,
            dataIndex: 'couponBagNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '售价（元）',
            width: 120,
            dataIndex: 'sellAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '单包价值（元）',
            width: 160,
            dataIndex: 'singleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const [filterCitys, updateFilterCitys] = useState([]);
    // 以城市维度，把原排序数组切割为二维数组
    const getNewestFormatList = () => {
        const list = [];
        equityBagSnBoList?.forEach((ele) => {
            if (ele.equityStatus == 2) {
                // 已结束的券包不参与排名
                return;
            }
            const target = list.find((cityEle) => cityEle.city == ele.city) || {};
            const targetList = target?.cityList || [];
            if (!target.city) {
                target.city = ele.city;
                target.cityName = ele.cityName;
            }
            targetList.push(ele);
            target.cityList = targetList;
            if (list.indexOf(target) < 0) {
                list.push(target);
            }
        });
        return list;
    };
    const citySnList = useMemo(() => {
        const list = getNewestFormatList();
        return list;
    }, [equityBagSnBoList]);

    const sortList = useMemo(() => {
        const list = [];
        citySnList?.forEach((ele) => {
            const citys = ele.cityList;
            citys?.forEach((cityEle) => {
                if (filterCitys?.length) {
                    if (filterCitys.includes(cityEle.city)) {
                        list.push(cityEle);
                    }
                } else {
                    list.push(cityEle);
                }
            });
        });
        return list;
    }, [citySnList, filterCitys]);

    useEffect(() => {
        sortForm.setFieldsValue({ ...(sortList || []) });
    }, [sortList]);

    const onSearch = (citys) => {
        updateFilterCitys([...citys]);
    };

    const cancelEvent = () => {
        onClose();
        updateEquityBagSnBoList([...oldEquityBagSnBoList]);
    };

    const onClose = () => {
        toggleModalVisible(false);
        resetData();
    };

    const resetData = () => {
        updateFilterCitys([]);
    };

    return (
        <Modal
            title={'排序'}
            width={880}
            visible={modalVisible}
            onCancel={cancelEvent}
            maskClosable={false}
            okText={'确定更改'}
            footer={null}
            destroyOnClose
        >
            <SearchLayout {...props} onSubmit={onSearch} onReset={resetData} />

            <Form form={sortForm} name="sort">
                <TablePro
                    name="sort"
                    scroll={{ x: 'max-content' }}
                    style={{
                        maxHeight: `${document.body.clientHeight - 400}px`,
                        overflowY: 'auto',
                    }}
                    rowKey={(record) => record}
                    dataSource={sortList}
                    columns={columns}
                    pagination={false}
                    noSort
                    offsetHeader={0}
                />
            </Form>

            {(!disabled && (
                <div style={{ textAlign: 'right' }}>
                    <br />
                    <Space>
                        <Button onClick={cancelEvent}>取消</Button>
                        <Button type="primary" onClick={submitEvent}>
                            确定更改
                        </Button>
                    </Space>
                </div>
            )) ||
                null}
        </Modal>
    );
};

export default forwardRef(SortRightModal);
