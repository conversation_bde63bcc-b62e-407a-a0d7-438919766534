import { Modal, Transfer, Tree, Spin, Input, Form, message } from 'antd';
import React, { useState, useImperativeHandle, useMemo, useEffect, forwardRef } from 'react';
import { getCityListApi } from '@/services/CommonApi';
import { useRef } from 'react';
const { Search } = Input;
import { connect } from 'umi';
import { CouponBagSelectLayout } from '@/components/CouponBagSelectItem/index';
import { CityTransfer } from '@/components/CityTransferModal';
import { RIGHTS_TYPES } from '../RightsConfig';

// 带弹窗的穿梭框
const AddRightModal = (props, ref) => {
    const {
        onFinish, // 点击确定的回调，带上目标列表的城市key集合
        cityRequest: request,
    } = props;

    const [modalVisible, toggleModalVisible] = useState(false); // 导入弹窗状态
    const cityRef = useRef();
    const bagRef = useRef();
    const [defaultCityKeys, updateDefaultCityKeys] = useState([]);
    const [disabledCityKeys, updateDisabledCityKeys] = useState([]);
    const [disabledCpnBagIds, updateDisabledCpnBagIds] = useState([]);
    const [disabled, updateDisabled] = useState(false);

    // 临时记录的全部城市树，用于确认是筛出目标值
    const [tempCityTreeData, setTempCityTreeData] = useState([]);

    const [step, setStep] = useState(0);
    const [cpnPackage, setCpnPackage] = useState(1);

    useImperativeHandle(ref, () => ({
        /** show方法参数说明
         * @param {defaultKeys} ：当前在目的列表的城市key集合
         * @param {disabledKeys} ：不可被操作的城市key集合，查看详情，或者只增不减，都利用此字段
         */
        show: ({
            disabledCityKeys: disabledKeys = [],
            disabledCpnBagIds: disabledIds = [],
            disabled: _disabled = false,
            isCpnPackage, // 是否配置券包
            rightType,
        } = {}) => {
            updateDisabledCityKeys([...disabledKeys]);
            updateDisabledCpnBagIds([...disabledIds]);
            updateDisabled(_disabled);
            toggleModalVisible(true);
            setStep(1);
            setCpnPackage(isCpnPackage);
        },
        onClose,
    }));

    useEffect(() => {
        if (step == 1) {
            setTimeout(() => {
                cityRef?.current?.initCities();
            }, 200);
        } else if (step == 2) {
        }
    }, [step]);

    const onNext = () => {
        if (step == 1) {
            if (!cityRef?.current?.targetKeys?.length) {
                message.error('请选择城市');
                return;
            }
            // onFinish?.(res);
            // onClose();
            updateDefaultCityKeys([...(cityRef?.current?.targetKeys || [])]);
            setTempCityTreeData(cityRef?.current?.cityTreeData);
            if (!cpnPackage) {
                const res = [];
                for (const key of cityRef?.current?.targetKeys || []) {
                    let has = false; // 判断当前key是否被消费掉
                    for (const item of cityRef?.current?.cityTreeData || []) {
                        for (const cityItem of item?.children || []) {
                            if (cityItem.key == key) {
                                res.push({ areaCode: cityItem.key, areaName: cityItem.title });
                                has = true;
                                break;
                            }
                        }
                        if (has) {
                            break;
                        }
                    }
                }
                const cpnBagItem = { cpnPackage, cityCodeList: res };
                onFinish?.(cpnBagItem);

                onClose();
            } else {
                setStep(2);
            }
        } else if (step == 2) {
            if (!bagRef.current.selectList?.length) {
                message.error('请选择券包');
                return;
            }
            const res = [];
            for (const key of defaultCityKeys || []) {
                let has = false; // 判断当前key是否被消费掉
                for (const item of tempCityTreeData || []) {
                    for (const cityItem of item?.children || []) {
                        if (cityItem.key == key) {
                            res.push({ areaCode: cityItem.key, areaName: cityItem.title });
                            has = true;
                            break;
                        }
                    }
                    if (has) {
                        break;
                    }
                }
            }

            const cpnBagItem = { ...(bagRef.current.selectList[0] || {}), cityCodeList: res };
            onFinish?.(cpnBagItem);

            onClose();
        }
    };

    const onClose = () => {
        updateDefaultCityKeys([]);
        updateDisabledCityKeys([]);
        toggleModalVisible(false);
        updateDisabled(false);
        cityRef?.current?.destoryCities();
    };

    const onCancel = () => {
        if (step == 1) {
            onClose();
        } else if (step == 2) {
            setStep(1);
        }
    };

    return (
        <Modal
            title={step == 1 ? '选择区域' : '选择券包'}
            width={step == 1 ? 600 : 880}
            visible={modalVisible}
            onCancel={onClose}
            maskClosable={false}
            onOk={onNext}
            okText={(step == '1' && cpnPackage && '下一步') || '确认'}
            cancelText={(step == '1' && '取消') || '上一步'}
            cancelButtonProps={{
                onClick: onCancel,
            }}
        >
            {(step == 1 && (
                <CityTransfer
                    ref={cityRef}
                    disabled={disabled}
                    defaultCityKeys={defaultCityKeys}
                    disabledCityKeys={disabledCityKeys}
                    request={request}
                />
            )) ||
                null}

            {(step == '2' && (
                <CouponBagSelectLayout disabledIds={disabledCpnBagIds} {...props} ref={bagRef} />
            )) ||
                null}
        </Modal>
    );
};

export default forwardRef(AddRightModal);
