import React, {
    Fragment,
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useMemo,
    useRef,
} from 'react';
import { connect } from 'umi';
import {
    Button,
    InputNumber,
    Select,
    Form,
    Modal,
    message,
    Space,
    Input,
    Row,
    Col,
    DatePicker,
} from 'antd';

import { EditOutlined } from '@ant-design/icons';
import EditBtnFormItem from '@/components/EditBtnFormItem/index';
import CityTransferModal from '@/components/CityTransferModal';
import moment from 'moment';
import { RIGHTS_TYPES } from '../RightsConfig';

const { Option } = Select;

const FormItem = Form.Item;

const searchformItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    wrapperCol: {},
};

const EditCouponBagModal = (props, ref) => {
    const {
        canEditPrice,
        onFinish, // 选择回调
        cityRequest,
        equityNo,
    } = props;

    const [visible, updateVisible] = useState(false);
    const [defaultValue, setDefaultValue] = useState();
    const [disabledCityKeys, setDisabledCitys] = useState([]);
    const [extInParams, setExtInParams] = useState({});
    useImperativeHandle(ref, () => ({
        show: ({
            value = {},
            extInParams: ext = {}, //额外参数
            disabledCityKeys: cityKeys = [],
        }) => {
            updateVisible(true);
            setDefaultValue({ ...value });
            setExtInParams({ ...ext });
            setDisabledCitys([...cityKeys]);
        },
    }));

    const [form] = Form.useForm();
    const cityModalRef = useRef();
    const bagModalRef = useRef();

    useEffect(() => {
        if (defaultValue) {
            let params = {
                ...defaultValue,
            };
            if (defaultValue.bgnTime?.length) {
                params.bgnTime = moment(defaultValue.bgnTime);
            }
            if (defaultValue.endTime?.length) {
                params.endTime = moment(defaultValue.endTime);
            }
            form.setFieldsValue(params);
        } else {
            form.resetFields();
        }
    }, [defaultValue]);

    const closeEvent = () => {
        form.resetFields();
        updateVisible(false);
    };

    const finishEvent = () => {
        form.validateFields().then((values) => {
            const params = { ...values };
            const bgnTime = values.bgnTime?.format?.('YYYY-MM-DD HH:mm:ss');
            if (bgnTime) {
                params.bgnTime = bgnTime;
            }
            const endTime = values.endTime?.format('YYYY-MM-DD HH:mm:ss');
            if (endTime) {
                params.endTime = endTime;
            }
            onFinish && onFinish(params);
            closeEvent();
        });
    };

    const disabledBgnDate = (current) => {
        // Can not select days before today and today
        return current && current < moment();
    };

    const disabledEndDate = (current, bgnTime) => {
        // Can not select days before today and today
        if (current) {
            const currentTime = moment();
            // 只能晚于开始时间或者当前时间中的最晚时间
            if (bgnTime > currentTime) {
                // 如果开始时间晚于当前时间，以开始时间做参考
                return current < bgnTime;
            }
            // 以当前时间做参考
            return current < currentTime;
        }
        return false;
    };

    return (
        <Modal
            title={'修改权益'}
            destroyOnClose
            width={700}
            visible={visible}
            onCancel={() => closeEvent()}
            footer={null}
            maskClosable={false}
        >
            <Form
                form={form}
                onFinish={finishEvent}
                initialValues={{ ...extInParams }}
                {...searchformItemLayout}
                scrollToFirstError
            >
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const couponBagNo = getFieldValue('couponBagNo');
                        const bgnTime = getFieldValue('bgnTime');
                        const endTime = getFieldValue('endTime');
                        const cityCodeList = getFieldValue('cityCodeList');
                        const equityStatus = getFieldValue('equityStatus');

                        const strs = [];
                        const codes = [];
                        cityCodeList?.map((ele) => {
                            strs.push(ele.areaName);
                            codes.push(ele.areaCode);
                        });
                        const showStr = strs.slice(0, 2);
                        const cityText = `${showStr.join(' | ')}${
                            (strs.length > 2 && '...') || ''
                        }`;
                        return (
                            <Fragment>
                                {/* 要持有住原有数据，防止编辑后数据丢失 */}
                                <FormItem name="city" noStyle />
                                <FormItem name="cityCodes" noStyle />
                                <FormItem name="cityName" noStyle />
                                <FormItem name="couponBagName" noStyle />
                                <FormItem name="couponBagNo" noStyle />
                                <FormItem name="couponBagNum" noStyle />
                                <FormItem name="couponBagStatus" noStyle />
                                <FormItem name="couponBagStatusName" noStyle />
                                <FormItem name="equityStatus" noStyle />
                                <FormItem name="equityStatusName" noStyle />
                                <FormItem name="singleNum" noStyle />

                                <EditBtnFormItem
                                    label="投放区域"
                                    clickEvent={() => {
                                        cityModalRef?.current?.show({
                                            defaultKeys: codes,
                                            disabledKeys: disabledCityKeys,
                                        });
                                    }}
                                    defaultValue={
                                        cityText?.length && (
                                            <div title={strs.join('、')}>{cityText}</div>
                                        )
                                    }
                                    name="cityCodeList"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value?.length) {
                                                    return Promise.reject('请选择投放区域');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <FormItem noStyle name="cityCodeList" />
                                </EditBtnFormItem>

                                <EditBtnFormItem
                                    label="开始时间"
                                    defaultValue={
                                        (bgnTime &&
                                            moment(bgnTime).format('YYYY-MM-DD HH:mm:ss')) ||
                                        undefined
                                    }
                                    disabled={equityStatus >= 1}
                                >
                                    <FormItem noStyle name="bgnTime">
                                        <DatePicker
                                            disabled={false}
                                            disabledDate={
                                                equityNo == RIGHTS_TYPES.SECKILL
                                                    ? undefined
                                                    : disabledBgnDate
                                            }
                                            showTime={{
                                                format: 'HH:mm:ss',
                                                defaultValue: moment('00:00:00', 'HH:mm:ss'),
                                            }}
                                            format="YYYY-MM-DD HH:mm:ss"
                                            showNow={false}
                                            onChange={(e) => {
                                                // 比较当前时间是否晚于结束时间，如果晚，直接清空结束时间内容
                                                if (!e || (endTime && e > endTime)) {
                                                    form.setFieldsValue({ endTime: undefined });
                                                }
                                            }}
                                        />
                                    </FormItem>
                                </EditBtnFormItem>

                                <EditBtnFormItem
                                    label="结束时间"
                                    defaultValue={
                                        (endTime &&
                                            moment(endTime).format('YYYY-MM-DD HH:mm:ss')) ||
                                        undefined
                                    }
                                    disabled={!bgnTime}
                                >
                                    <FormItem noStyle name="endTime">
                                        <DatePicker
                                            disabled={false}
                                            disabledDate={(time) => disabledEndDate(time, bgnTime)}
                                            showTime={{
                                                format: 'HH:mm:ss',
                                                defaultValue: moment('23:59:59', 'HH:mm:ss'),
                                            }}
                                            format="YYYY-MM-DD HH:mm:ss"
                                            showNow={false}
                                        />
                                    </FormItem>
                                </EditBtnFormItem>
                            </Fragment>
                        );
                    }}
                </FormItem>

                <br></br>
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button type="primary" htmlType="submit">
                        提交
                    </Button>
                    <Button onClick={() => closeEvent()}>取消</Button>
                </Space>
            </Form>

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    form.setFieldsValue({ cityCodeList: citys });
                }}
                request={cityRequest}
            />
        </Modal>
    );
};

export default forwardRef(EditCouponBagModal);
