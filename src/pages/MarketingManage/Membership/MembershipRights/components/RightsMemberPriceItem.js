import {
    Form,
    Button,
    Col,
    Select,
    Space,
    Modal,
    message,
    Popconfirm,
    InputNumber,
    Table,
} from 'antd';
import React, { Fragment, useState, useMemo, useEffect, useRef, useContext } from 'react';
import TablePro from '@/components/TablePro';
import commonStyle from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';

import CitysSelect from '@/components/CitysSelect/index.js';
import CityTransferModal from '@/components/CityTransferModal';
import {
    getVipPlanEquityCityApi,
    getVipLimitLogListApi,
} from '@/services/Marketing/MarketingMembershipApi';
import { PlusOutlined, OrderedListOutlined } from '@ant-design/icons';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import usePageState from '@/hooks/usePageState.js';

const formItemLayout = {};
const { Option } = Select;

const FormItem = Form.Item;

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} name="member-price" component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    disabled,
    ...restProps
}) => {
    const form = useContext(EditableContext);

    useEffect(() => {
        if (record && !isEmpty(record[dataIndex])) {
            form.setFieldsValue({ [dataIndex]: record[dataIndex] });
        }
    }, []);

    const save = async () => {
        try {
            const values = await form.validateFields();
            handleSave({
                ...record,
                ...values,
            });
        } catch (errInfo) {
            const curValues = form.getFieldsValue();
            console.log('Save failed:', errInfo, curValues);
            handleSave({
                ...record,
                ...curValues,
            });
        }
    };
    let childNode = children;
    if (editable) {
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            const newLimitTimes = getFieldValue('newLimitTimes');
                            const newLimitAmount = getFieldValue('newLimitAmount');
                            if (!newLimitTimes && !newLimitAmount) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请填写${title}`);
                                }
                            }
                            if (Number(value) > 0) {
                                return Promise.resolve();
                            } else {
                                if (value === 0) {
                                    if (title === '每次限制额度(元)') {
                                        return Promise.reject(`${title}须为大于0的数`);
                                    } else {
                                        return Promise.reject(`${title}须为大于0的正整数`);
                                    }
                                } else {
                                    return Promise.resolve();
                                }
                            }
                        },
                    }),
                ]}
            >
                <InputNumber
                    step={1}
                    min={0}
                    precision={title === '每次限制额度(元)' ? 2 : 0}
                    disabled={disabled}
                    onPressEnter={save}
                    onBlur={save}
                    placeholder="请输入"
                />
            </Form.Item>
        );
    }
    return <td {...restProps}>{childNode}</td>;
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            name="rights-search"
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                cityCodeList: props?.location?.query?.dc ? [props?.location?.query?.dc] : undefined,
            }}
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} form={form}>
                <Col span={8}>
                    <CitysSelect
                        label="会员城市"
                        name="cityCodeList"
                        placeholder="请选择"
                        formItemLayout={formItemLayout}
                        showArrow
                        provinceSelectable
                        rules={null}
                    />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const RecordListView = (props) => {
    const { limitId } = props;
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, { global: {} });
    const [listLoading, updateListLoading] = useState(false);
    const [tableList, updateTableList] = useState([]);
    const [tableListTotal, updateTableListTotal] = useState(0);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const searchData = async () => {
        try {
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                limitId,
            };

            updateListLoading(true);
            const {
                data: { list, total },
            } = await getVipLimitLogListApi(params);
            updateTableList(list || []);
            updateTableListTotal(total);
            return list;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const recordColumns = [
        {
            title: '每期限制次数(次)',
            dataIndex: 'limitTimes',
            render(text, record) {
                return <span title={text}>{text || '--'}</span>;
            },
        },
        {
            title: '每次限制额度(元)',
            dataIndex: 'limitAmount',
            render(text, record) {
                return <span title={text}>{text || '--'}</span>;
            },
        },
        {
            title: '修改时间',
            dataIndex: 'updateTime',
        },
        {
            title: '修改人',
            dataIndex: 'updateOper',
        },
    ];

    return (
        <Table
            loading={listLoading}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            dataSource={tableList}
            columns={recordColumns}
            onChange={onTableChange}
            pagination={{
                current: pageInfo.pageIndex,
                total: tableListTotal,
                pageSize: pageInfo.pageSize,
                showTotal: (total) => `共 ${total} 条`,
            }}
        />
    );
};

const RightsMemberPriceItem = (props) => {
    const { value, onChange, disabled, ...otherOptions } = props;

    const [searchForm] = Form.useForm();

    const [rightsList, changeRightsList] = useState([]);

    const [showRecordView, toggleRecordView] = useState(false);

    const [lookLimitId, updateLookLimitId] = useState(null);

    const [filterOptions, changeFilterOptions] = useState({
        cityCodeList: props?.location?.query?.dc ? [props?.location?.query?.dc] : undefined,
    });

    const cityModalRef = useRef();

    const filterList = useMemo(() => {
        return rightsList
            .filter((ele) => {
                // 如果没有筛选，直接返回true用于展示
                if (Object.values(filterOptions)?.length == 0) {
                    return true;
                }
                let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。

                if (filterOptions.cityCodeList && filterOptions.cityCodeList instanceof Array) {
                    //过滤区域
                    if (!filterOptions.cityCodeList.includes(ele.cityCode)) {
                        passFilter = false;
                    }
                }

                return passFilter;
            })
            .map((ele, index) => {
                return { ...ele, index: index + 1 };
            });
    }, [rightsList, filterOptions]);

    useEffect(() => {
        if (value instanceof Array) {
            changeRightsList(value);
        }
    }, [value]);

    const updateFormValue = (newRighstList) => {
        onChange && onChange(newRighstList);
    };

    const touchAddEvent = () => {
        const codes = [];
        const disabledCityKeys = [];
        rightsList.forEach((element) => {
            codes.push(element.cityCode);
            if (true) {
                disabledCityKeys.push(element.cityCode);
            }
        });
        cityModalRef?.current?.show({
            defaultKeys: codes,
            disabledKeys: disabledCityKeys,
        });
    };

    // 跳转发放记录页面
    const lookRecordEvent = async (item) => {
        updateLookLimitId(item.limitId);
        toggleRecordView(true);
    };
    // 跳转发放记录页面
    const closeRecordEvent = () => {
        toggleRecordView(false);

        updateLookLimitId(undefined);
    };

    const delEvent = (item) => {
        const newRighstList = copyObjectCommon(rightsList);
        const itemIndex = newRighstList.findIndex((ele) => item.cityCode === ele.cityCode);
        if (itemIndex >= 0) {
            newRighstList.splice(itemIndex, 1);
            changeRightsList(newRighstList);
            updateFormValue(newRighstList);
        }
    };

    const updateItemEvent = (item, params) => {
        const newRighstList = copyObjectCommon(rightsList);
        const itemIndex = newRighstList.findIndex((ele) => item.cityCode === ele.cityCode);
        if (itemIndex >= 0) {
            newRighstList[itemIndex] = { ...newRighstList[itemIndex], ...params };
            changeRightsList(newRighstList);
            updateFormValue(newRighstList);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        searchData();
    };
    const searchData = () => {
        const params = searchForm.getFieldsValue();
        const options = {};
        if (params.cityCodeList?.length) {
            options.cityCodeList = params.cityCodeList;
        }

        changeFilterOptions(options);
    };

    const columns = useMemo(() => {
        return [
            {
                title: '序号 ',
                width: 80,
                dataIndex: 'index',
                render(text, record, index) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '会员城市',
                dataIndex: 'cityName',
                width: 200,
                render(text, record, index) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '每期限制次数(次)',
                dataIndex: 'newLimitTimes',
                width: 200,
                editable: true,
                disabled,
                onCell: (record) => ({
                    record,
                    editable: true,
                    disabled,
                    dataIndex: 'newLimitTimes',
                    title: '每期限制次数(次)',
                    handleSave,
                }),
            },
            {
                title: '每次限制额度(元)',
                dataIndex: 'newLimitAmount',
                width: 200,
                editable: true,
                disabled,
                onCell: (record) => ({
                    record,
                    editable: true,
                    disabled,
                    dataIndex: 'newLimitAmount',
                    title: '每次限制额度(元)',
                    handleSave,
                }),
            },
            {
                title: '状态',
                width: 120,
                dataIndex: 'status',
                render(text, record) {
                    let statusName = '';
                    const status = record.newStatus || record.status;
                    if (status === '1') {
                        statusName = '启用';
                    } else if (status === '0') {
                        statusName = '禁用';
                    }
                    return <span title={statusName}>{statusName || '-'}</span>;
                },
            },
            {
                title: '修改时间',
                width: 200,
                dataIndex: 'updateTime',
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },

            {
                title: '编辑',
                fixed: 'right',
                width: 140,
                render: (text, record, index) => {
                    const lookBtn = (
                        <span
                            className={commonStyle['table-btn']}
                            onClick={() => lookRecordEvent(record)}
                        >
                            查看记录
                        </span>
                    );

                    const delBtn = (
                        <Popconfirm
                            title="要从列表中删除此城市？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => delEvent(record)}
                        >
                            <span className={commonStyle['table-btn']} style={{ color: '#f50' }}>
                                删除
                            </span>
                        </Popconfirm>
                    );

                    const startBtn = (
                        <Popconfirm
                            title="要启用此城市？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => {
                                updateItemEvent(record, { newStatus: '1' });
                            }}
                        >
                            <span className={commonStyle['table-btn']}>启用</span>
                        </Popconfirm>
                    );

                    const stopBtn = (
                        <Popconfirm
                            title="要禁用此城市？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => {
                                updateItemEvent(record, { newStatus: '0' });
                            }}
                        >
                            <span className={commonStyle['table-btn']} style={{ color: '#f50' }}>
                                禁用
                            </span>
                        </Popconfirm>
                    );

                    let btns = [];
                    // equityStatus：0 未开始 1 进行中 2 已结束
                    // if (disabled) {
                    //     btns.push(lookBtn);
                    // } else if (record.equityStatus === '0') {
                    //     //未开始 或 未提交

                    //     btns.push(delBtn);
                    // } else if (record.equityStatus == '1') {
                    //     btns.push(lookBtn);
                    // } else {
                    //     btns.push(lookBtn);
                    // }

                    const status = record.newStatus || record.status;

                    if (status) {
                        btns.push(lookBtn);
                        if (status === '0') {
                            btns.push(startBtn);
                        } else {
                            btns.push(stopBtn);
                        }
                    } else {
                        btns.push(delBtn);
                    }

                    return <Space>{btns}</Space>;
                },
            },
        ];
    });

    const cityRequest = async (params = {}) => {
        try {
            params.vipCityState = '1';
            const {
                data: { areaList },
            } = await getVipPlanEquityCityApi(params);

            // 如果可选城市列表里面没有已选城市，则本地拼接上已选城市的对象
            const tempProvince = [];
            rightsList.forEach((element) => {
                if (
                    !areaList.some((ele) =>
                        ele.cityList?.some((subEle) => subEle?.areaCode == element.cityCode),
                    )
                ) {
                    tempProvince.push({ areaCode: element.cityCode, areaName: element.cityName });
                }
            });
            if (tempProvince?.length) {
                areaList.push({ areaCode: '-1', cityList: tempProvince });
            }
            return areaList;
        } catch (error) {
            return [];
        }
    };

    const handleSave = (row) => {
        const newData = [...rightsList];
        const index = newData.findIndex((item) => row.cityCode === item.cityCode);
        const item = newData[index];
        newData.splice(index, 1, {
            ...item,
            ...row,
        });
        changeRightsList(newData);
        updateFormValue(newData);
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    return (
        <Fragment>
            <SearchLayout
                {...props}
                form={searchForm}
                onSubmit={() => {
                    searchData();
                }}
                onReset={resetData}
            />
            {(!disabled && (
                <Space className={commonStyle['btn-bar']}>
                    {(!disabled && (
                        <Button type="primary" onClick={touchAddEvent}>
                            <PlusOutlined />
                            新增
                        </Button>
                    )) ||
                        null}
                </Space>
            )) ||
                null}
            <TablePro
                name="rights"
                components={components}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.cityCode}
                dataSource={filterList}
                columns={columns}
            />

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    // 编辑城市处理排序
                    let selectCity = citys.map((ele) => {
                        let findInfo = rightsList.find((item) => {
                            return item.cityCode === ele.areaCode;
                        });
                        return {
                            ...findInfo,
                            cityCode: ele.areaCode,
                            cityName: ele.areaName,
                        };
                    });

                    changeRightsList(selectCity);
                    updateFormValue(selectCity);
                }}
                request={cityRequest}
            />
            <Modal
                title="历史记录"
                visible={showRecordView}
                width={1000}
                destroyOnClose
                onCancel={closeRecordEvent}
                footer={
                    <Space align="center" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" onClick={closeRecordEvent}>
                            关闭
                        </Button>
                    </Space>
                }
            >
                <RecordListView limitId={lookLimitId}></RecordListView>
            </Modal>
        </Fragment>
    );
};

export default RightsMemberPriceItem;
