import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    stopMembershipPlanApi,
    deleteMembershipPlanApi,
} from '@/services/Marketing/MarketingMembershipApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { RIGHTS_TYPES } from './RightsConfig';

const { Option } = Select;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        global: { codeInfo = {} },
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
    } = props;

    const { equityType: equityCategory } = codeInfo;

    useEffect(() => {
        if (!equityCategory?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'equityType',
            });
        }
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const equityCategoryOptions = useMemo(() => {
        return equityCategory?.map((ele) => {
            return (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            );
        });
    }, [equityCategory]);

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <FormItem label="权益类型" name="equityType">
                        <Select placeholder="请选择">{equityCategoryOptions}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="权益类目" name="equityName">
                        <Input placeholder="请填写权益项目" autoComplete="off" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const MembershipRightsListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {} },
        membershipModel: { membershipRightsList, membershipRightsListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({ pageSize: 20 }, props);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'membershipModel/getMembershipRightsList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const gotoCougonBagsEvent = () => {
        history.push('/userCenter/membership/rights/list/coupon-bags-list');
    };

    const editEvent = (item) => {
        if (item.equityNo == RIGHTS_TYPES.LEVEL) {
            history.push(`/marketing/businessActive/module-basic-price`);
        } else {
            history.push(
                `/userCenter/membership/rights/list/update/${item.equityId}/${item.equityNo}`,
            );
        }
    };

    const lookEvent = (item) => {
        if (item.equityNo === RIGHTS_TYPES.MEMBER_PROPR) {
            history.push(`/marketing/businessActive/module-basic-price`);
        } else {
            history.push(
                `/userCenter/membership/rights/list/detail/${item.equityId}/${item.equityNo}`,
            );
        }
    };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return (
                    <span title={pageInfo.pageIndex + index}>
                        {(pageInfo.pageIndex - 1) * pageInfo.pageSize + index + 1}
                    </span>
                );
            },
        },
        {
            title: '权益类目ID',
            width: 140,
            dataIndex: 'equityNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '权益类目',
            width: 160,
            dataIndex: 'equityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '适用会员方案类型',
            width: 160,
            dataIndex: 'actSubTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '权益类型',
            width: 160,
            dataIndex: 'equityTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '权益说明',
            width: 220,
            dataIndex: 'equityMark',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '修改时间',
            width: 200,
            dataIndex: 'dataOperTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (text, record) => {
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );

                const lookBtn = (
                    <span className={styles['table-btn']} onClick={() => lookEvent(record)}>
                        查看
                    </span>
                );

                let btns = [];

                if (record.equityNo != RIGHTS_TYPES.MEMBER_SERVICE) {
                    // 专属客服无查看和编辑按钮
                    if (record.equityNo != RIGHTS_TYPES.LEVEL) {
                        // 等级专项只有编辑按钮
                        btns.push(lookBtn);
                    }
                    btns.push(editBtn);
                }

                return <Space>{btns}</Space>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    {...props}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoCougonBagsEvent}>
                        券包列表
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={membershipRightsList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={false}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, membershipModel, loading }) => ({
    global,
    membershipModel,
    listLoading: loading.effects['membershipModel/getMembershipRightsList'],
}))(MembershipRightsListPage);
