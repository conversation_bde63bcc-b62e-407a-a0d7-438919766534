import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Col,
    Input,
    InputNumber,
    Select,
    Form,
    Row,
    Checkbox,
    Modal,
    DatePicker,
    Tooltip,
    message,
    Space,
    Radio,
    Switch,
    Upload,
    Divider,
    TreeSelect,
    Descriptions,
} from 'antd';

const { SHOW_CHILD, SHOW_PARENT, SHOW_ALL } = TreeSelect;

import {
    LeftOutlined,
    MinusCircleOutlined,
    LoadingOutlined,
    PlusOutlined,
    InfoCircleOutlined,
} from '@ant-design/icons';
import { filetoDataURL, urltoImage, imagetoCanvas, dataURLtoFile } from '@/utils/utils';

import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { saveMembershipPlanApi } from '@/services/Marketing/MarketingMembershipApi';
import { addCpnApi } from '@/services/Marketing/MarketingTurnoverApi';
import { ACTSUBTYPES } from '@/config/declare';
import TablePro from '@/components/TablePro';
import { STATUS_TYPES } from '@/config/declare';
import commonStyles from '@/assets/styles/common.less';
import styles from '../Membership.less';
import UpLoadImg from '@/components/UpLoadImg/index.js';
import CouponTable from '@/components/CouponComponents/CouponTable';
import CitysSelect from '@/components/CitysSelect/index.js';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';

import { RANGE_TYPES } from '@/config/declare';
import { getCityListApi } from '@/services/CommonApi';
import { isEmpty } from '@/utils/utils';

const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { confirm } = Modal;

const { Option } = Select;

const FormItem = Form.Item;

// 跳转方式
const LINK_TYPES = {
    HOME: '01', // 首页
    CARD: '02', // 我的卡包
    OTHER: '03', // 自定义
    GIFT: '04', // 默认礼包领取页面
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

export const InfoFormLayout = (props) => {
    const { form, membershipPlanInfo, isLock } = props;

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    return (
        <Fragment>
            <FormItem
                label="方案名称:"
                name="actName"
                {...formItemFixedWidthLayout}
                rules={[{ required: true, message: '请填写活动名称' }]}
            >
                <Input disabled={isLock} placeholder="限24个字" autoComplete="off" maxLength={24} />
            </FormItem>

            <FormItem
                label="副标题"
                name="actMarks"
                {...formItemFixedWidthLayout}
                rules={[{ required: true, message: '请填写副标题' }]}
            >
                <Input disabled={isLock} placeholder="限12个字" autoComplete="off" maxLength={12} />
            </FormItem>

            <FormItem
                label="方案有效期"
                {...formItemFixedWidthLayout}
                required
                name="dateTime"
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value) {
                                return Promise.reject('请选择活动有效期');
                            }
                            if (!value[0]) {
                                return Promise.reject('请选择活动开始日期');
                            }
                            if (!value[1]) {
                                return Promise.reject('请选择活动失效日期');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <RangePicker
                    showTime={{
                        format: 'HH:mm',
                        defaultValue: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')],
                    }}
                    // disabledTime={disabledRangeTime}
                    disabled={isLock}
                    format="YYYY-MM-DD HH:mm"
                />
            </FormItem>

            <FormItem label="单月价" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="monthAmt"
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={0}
                            max={9999}
                            disabled={isLock}
                            style={{ width: '100%' }}
                            placeholder="输入金额"
                        />
                    </FormItem>
                    <span>元</span>
                </Space>
            </FormItem>

            <FormItem label="季卡价" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="seasonAmt"
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={0}
                            max={9999}
                            disabled={isLock}
                            style={{ width: '100%' }}
                            placeholder="输入金额"
                        />
                    </FormItem>
                    <span>元</span>
                </Space>
            </FormItem>

            <FormItem label="年卡价" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="yearAmt"
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={0}
                            max={9999}
                            disabled={isLock}
                            style={{ width: '100%' }}
                            placeholder="输入金额"
                        />
                    </FormItem>
                    <span>元</span>
                </Space>
            </FormItem>

            <FormItem label="连续价" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="continuityAmt"
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={0}
                            max={9999}
                            disabled={isLock}
                            style={{ width: '100%' }}
                            placeholder="输入金额"
                        />
                    </FormItem>
                    <span>元</span>
                </Space>
            </FormItem>
        </Fragment>
    );
};

const PrizeLayout = (props) => {
    const {
        dispatch,
        membershipModel: { membershipPlanInfo = {} },
        global: { codeInfo },
        form,
        listLoading,
        isLock,
        isCopy,
        route,
        fileKey,
        couponModel,
        global,
        onRefresh,
    } = props;

    const { addPrizeList } = membershipPlanInfo;
    const [addPrizes, changeAddPrizesList] = useState([]); // 邀请人列表

    useEffect(() => {
        if (addPrizeList && addPrizeList[fileKey]) {
            const { addPrizes } = addPrizeList[fileKey];
            const inviteds = addPrizes.map((ele) => ({
                cpnNo: ele.cpnNo,
                actTaskState: '1',
                cpnAddType: '02',
                ...ele,
            }));
            changeAddPrizesList([...inviteds]);
        }
    }, [addPrizeList, isLock]);

    useEffect(() => {
        const addPrizeList = form.getFieldValue('addPrizeList');
        const list = [...addPrizeList];

        if (addPrizes) {
            list[fileKey].addPrizes = addPrizes;
        }

        form.setFieldsValue({ addPrizeList: list });
    }, [addPrizes]);

    return (
        <Fragment>
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const prizeList = getFieldValue('prizeList');
                    // 拼出其他列已选的优惠券列表，在当前任务不允许继续选中
                    const addPrizeList = getFieldValue('addPrizeList');
                    let otherCpnList = [...(prizeList || [])];
                    for (let i = 2; i >= 0; i--) {
                        if (i == fileKey) continue;
                        const task = addPrizeList[i];
                        const addPrizes = task?.addPrizes;
                        if (addPrizes?.length) {
                            otherCpnList = [...addPrizes, ...otherCpnList];
                        }
                    }

                    return (
                        <Fragment>
                            <FormItem label="加量包售价" {...formItemFixedWidthLayout} required>
                                <Space>
                                    <FormItem
                                        name={[fileKey, 'vipPrizePrice']}
                                        noStyle
                                        rules={[{ required: true, message: '请输入售价' }]}
                                    >
                                        <InputNumber
                                            min={0}
                                            disabled={isLock}
                                            style={{ width: '100%' }}
                                        />
                                    </FormItem>
                                    <span>元</span>
                                </Space>
                            </FormItem>

                            <CouponTable
                                actId={membershipPlanInfo?.actId}
                                dispatch={dispatch}
                                global={global}
                                couponModel={couponModel}
                                form={form}
                                needCheck
                                actState={membershipPlanInfo?.actState}
                                editabled={route.path.indexOf('/look') < 0}
                                putCpnList={addPrizes || []}
                                formItemLayout={formItemLayout}
                                isCopy={isCopy}
                                title={'配置奖品'}
                                name="addPrizes"
                                filePath={['addPrizeList', fileKey]}
                                onRefresh={onRefresh}
                                extInParams={{ cpnOwner: '03', vipCouponFlag: '1' }}
                                disabledIds={otherCpnList.map((ele) => ele.cpnId)}
                                putColTitle="每人领取数量"
                            />

                            <FormItem label="单期购买限制" {...formItemFixedWidthLayout} required>
                                <Space>
                                    <FormItem
                                        name={[fileKey, 'recevieNumLimit']}
                                        noStyle
                                        rules={[{ required: true, message: '请输入次数' }]}
                                    >
                                        <InputNumber
                                            min={0}
                                            disabled={isLock}
                                            style={{ width: '100%' }}
                                            precision={0}
                                        />
                                    </FormItem>
                                    <span>次</span>
                                </Space>
                            </FormItem>
                        </Fragment>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

export const ExperienceFormLayout = (props) => {
    const {
        dispatch,
        form,
        membershipPlanInfo,
        isLock,
        global: { codeInfo = {}, custLabelTypeList },
    } = props;
    useEffect(() => {
        if (!(custLabelTypeList instanceof Array) || custLabelTypeList?.length === 0) {
            dispatch({
                type: 'global/initCustLabelTypeList',
            });
        }
    }, []);
    return (
        <Fragment>
            <h1 className={commonStyles['form-title']}>
                <Space>
                    体验会员配置
                    <FormItem name="vipOpenFlag" noStyle valuePropName="checked">
                        <Switch></Switch>
                    </FormItem>
                </Space>
            </h1>

            <Form.Item
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.vipOpenFlag !== curValues.vipOpenFlag
                }
                noStyle
            >
                {({ getFieldValue, validateFields }) => {
                    const vipOpenFlag = getFieldValue('vipOpenFlag');
                    let link = `/pagesPaymember/paymember/probationCheck`;

                    let alipayLink = `alipays://platformapi/startapp?appId=2019092067648076&page=${link}`;

                    return (
                        vipOpenFlag && (
                            <Fragment>
                                <FormItem
                                    label="体验会员周期"
                                    {...formItemFixedWidthLayout}
                                    required
                                >
                                    <Space>
                                        <FormItem
                                            name="actCycle"
                                            rules={[
                                                { required: true, message: '请输入天数' },
                                                () => ({
                                                    validator(rule, value) {
                                                        if (!isEmpty(value) && Number(value) < 1) {
                                                            return Promise.reject(
                                                                '周期最少配置1天',
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            noStyle
                                        >
                                            <InputNumber
                                                precision={0}
                                                step={1}
                                                min={0}
                                                disabled={isLock}
                                                style={{ width: '100%' }}
                                                placeholder="请输入天数"
                                                onChange={() => {
                                                    validateFields(['inviteTerm']);
                                                }}
                                            />
                                        </FormItem>
                                        <span>天</span>
                                    </Space>
                                </FormItem>
                                <FormItem label="日优惠上限" {...formItemFixedWidthLayout} required>
                                    <Space>
                                        <FormItem
                                            name="discountLimitAmtDay"
                                            rules={[
                                                { required: true, message: '请输入金额' },
                                                () => ({
                                                    validator(rule, value) {
                                                        if (
                                                            !isEmpty(value) &&
                                                            Number(value) < 0.01
                                                        ) {
                                                            return Promise.reject(
                                                                '日优惠上限最少配置0.01',
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            noStyle
                                        >
                                            <InputNumber
                                                precision={2}
                                                step={0.01}
                                                min={0}
                                                max={999.99}
                                                disabled={isLock}
                                                style={{ width: '100%' }}
                                                placeholder="请输入金额"
                                            />
                                        </FormItem>
                                        <span>元</span>
                                    </Space>
                                </FormItem>
                                <FormItem label="领取周期" {...formItemFixedWidthLayout} required>
                                    <Space>
                                        <FormItem
                                            name="inviteTerm"
                                            rules={[
                                                { required: true, message: '请输入天数' },
                                                () => ({
                                                    validator(rule, value) {
                                                        const actCycle = getFieldValue('actCycle');

                                                        if (!isEmpty(value)) {
                                                            if (!isEmpty(actCycle)) {
                                                                if (
                                                                    Number(actCycle) > Number(value)
                                                                ) {
                                                                    return Promise.reject(
                                                                        '领取周期不能小于会员周期',
                                                                    );
                                                                }
                                                            }
                                                            if (Number(value) < 1) {
                                                                return Promise.reject(
                                                                    '周期最少配置1天',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            noStyle
                                        >
                                            <InputNumber
                                                precision={0}
                                                step={1}
                                                min={0}
                                                disabled={isLock}
                                                style={{ width: '100%' }}
                                                placeholder="请输入天数"
                                                onChange={() => {
                                                    validateFields(['actCycle']);
                                                }}
                                            />
                                        </FormItem>
                                        <span>天</span>
                                    </Space>
                                </FormItem>

                                <CheckBoxGroup
                                    label="活动人群"
                                    name="vipActiveCrowd"
                                    form={form}
                                    selectList={custLabelTypeList}
                                    required
                                    rules={[{ required: true, message: '请选择活动人群' }]}
                                    valueType="select"
                                    {...formItemFixedWidthLayout}
                                />

                                <FormItem noStyle>
                                    <Descriptions title="领取链接" column={1}>
                                        <Descriptions.Item label="小程序外部支付宝链接">
                                            {alipayLink}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="小程序内部链接">
                                            {link}
                                        </Descriptions.Item>
                                    </Descriptions>
                                </FormItem>
                            </Fragment>
                        )
                    );
                }}
            </Form.Item>
        </Fragment>
    );
};

export const ConfigureFormLayout = (props) => {
    const { form, membershipPlanInfo, isLock } = props;

    // 日期多选项
    const dataOptions = [
        {
            label: '周一',
            value: '1',
        },
        {
            label: '周二',
            value: '2',
        },
        {
            label: '周三',
            value: '3',
        },
        {
            label: '周四',
            value: '4',
        },
        {
            label: '周五',
            value: '5',
        },
        {
            label: '周六',
            value: '6',
        },
        {
            label: '周日',
            value: '7',
        },
    ];

    return (
        <Fragment>
            <FormItem
                label="会员日日期"
                name="vipDayFlag"
                {...formItemFixedWidthLayout}
                rules={[{ required: true, message: '请选择' }]}
            >
                <Radio.Group disabled={isLock}>
                    <Radio value="1">不限制</Radio>
                    <Radio value="0">限制</Radio>
                </Radio.Group>
            </FormItem>

            <Form.Item
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.vipDayFlag !== curValues.vipDayFlag
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const vipDayFlag = getFieldValue('vipDayFlag');
                    // 限制
                    return (
                        (vipDayFlag == 0 && (
                            <Form.Item
                                label="会员日可用日期"
                                name="vipDay"
                                {...formItemLayout}
                                labelCol={{
                                    flex: '0 0 130px',
                                }}
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择可用日期',
                                    },
                                ]}
                            >
                                <Checkbox.Group disabled={isLock} options={dataOptions} />
                            </Form.Item>
                        )) ||
                        null
                    );
                }}
            </Form.Item>

            <FormItem
                label={
                    <Tooltip title="会员优惠金额/原价=优惠比例">
                        会员日活动场站
                        <InfoCircleOutlined
                            style={{
                                marginLeft: '6px',
                            }}
                        />
                    </Tooltip>
                }
                {...formItemFixedWidthLayout}
                labelCol={{
                    flex: '0 0 140px',
                }}
                required
            >
                <Space style={{ whiteSpace: 'nowrap' }} size="large">
                    <Space>
                        <span>{'优惠比例 >'}</span>
                        <FormItem
                            name="vipDayDiscountLimit"
                            noStyle
                            rules={[{ required: true, message: '请填写会员日折扣' }]}
                        >
                            <InputNumber
                                precision={2}
                                step={0.01}
                                min={0}
                                max={100}
                                disabled={isLock}
                                style={{ width: '100%' }}
                            />
                        </FormItem>
                        <span>%</span>
                    </Space>
                    {/* <Space>
                        <span>{'立减 >'}</span>
                        <FormItem
                            name="vipDayReductionLimit"
                            noStyle
                            rules={[{ required: true, message: '请填写会员日立减金额' }]}
                        >
                            <InputNumber
                                precision={2}
                                step={0.01}
                                min={0}
                                max={99999}
                                disabled={isLock}
                                style={{ width: '100%' }}
                            />
                        </FormItem>
                        <span>元</span>
                    </Space> */}
                </Space>
            </FormItem>
            <CitysSelect
                label="会员开通城市"
                name="vipCityList"
                placeholder="请选择"
                formItemLayout={formItemFixedWidthLayout}
                disabled={isLock}
                showArrow
                defaultCityIds={membershipPlanInfo?.vipCityList}
                provinceSelectable
                onChange={(citylist) => {
                    //切换会员城市时讲没有选择的会员日城市去掉
                    const vipDayList = form.getFieldValue('vipDayCityList');
                    let list = [];
                    if (vipDayList instanceof Array) {
                        for (const item of vipDayList) {
                            if (citylist.includes(item)) {
                                list.push(item);
                            }
                        }
                    }

                    form.setFieldsValue({
                        vipDayCityList: list,
                    });
                }}
            />
            <Form.Item
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.vipCityList !== curValues.vipCityList
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const vipCityList = getFieldValue('vipCityList');
                    // 限制
                    return (
                        <CitysSelect
                            label="会员日城市"
                            name="vipDayCityList"
                            placeholder="请选择"
                            formItemLayout={formItemFixedWidthLayout}
                            disabled={isLock}
                            showArrow
                            defaultCityIds={membershipPlanInfo?.vipDayCityList}
                            provinceSelectable
                            canSelectCityIds={vipCityList}
                            rules={[]}
                        />
                    );
                }}
            </Form.Item>

            {/* <FormItem
                label="会员群"
                {...formItemLayout}
                wrapperCol={{
                    span: 24,
                }}
            >
                <Form.List name="vipGroupList">
                    {(fields, { add, remove }) => (
                        <Fragment>
                            {fields.map((field, index) => (
                                <Fragment key={index}>
                                    <Row gutter={{ md: 4, lg: 12, xl: 24 }}>
                                        <Col flex="200px">
                                            <FormItem
                                                name={[field.name, 'vipGroup']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '输入群名称',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    disabled={isLock}
                                                    placeholder="输入群名称，限24个字"
                                                    autoComplete="off"
                                                    maxLength={24}
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col flex="200px">
                                            <FormItem
                                                name={[field.name, 'vipGroupLink']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '输入群链接',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    disabled={isLock}
                                                    placeholder="输入群链接"
                                                    autoComplete="off"
                                                    maxLength={200}
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col flex="1">
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) =>
                                                    prevValues.vipGroupList !=
                                                    curValues.vipGroupList
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const curSuitStation =
                                                        getFieldValue('vipGroupList');
                                                    // 已选该运营商并配置了站点
                                                    const disabledIds = curSuitStation?.map(
                                                        (item, key) => {
                                                            if (index != key && item) {
                                                                return item.city;
                                                            }
                                                            return undefined;
                                                        },
                                                    );
                                                    return (
                                                        <CitysSelect
                                                            label=""
                                                            name={[field.name, 'city']}
                                                            validateTrigger={['onChange']}
                                                            placeholder="请选择"
                                                            disabled={isLock}
                                                            disabledIds={disabledIds}
                                                            defaultCityIds={
                                                                (membershipPlanInfo &&
                                                                    curSuitStation &&
                                                                    curSuitStation[index]?.city) ||
                                                                []
                                                            }
                                                            showArrow
                                                            allowClear
                                                            provinceSelectable
                                                        />
                                                    );
                                                }}
                                            </FormItem>
                                        </Col>
                                        {(!isLock && (
                                            <Col>
                                                <MinusCircleOutlined
                                                    className={styles['dynamic-delete-button']}
                                                    style={{ margin: '0 8px' }}
                                                    onClick={() => {
                                                        remove(field.name);
                                                    }}
                                                />
                                            </Col>
                                        )) ||
                                            null}
                                    </Row>
                                </Fragment>
                            ))}
                            {(!isLock && (
                                <FormItem label="">
                                    <Button
                                        type="dashed"
                                        onClick={() => {
                                            add();
                                        }}
                                        style={{ width: '200px' }}
                                    >
                                        <PlusOutlined />
                                        添加会员群
                                    </Button>
                                </FormItem>
                            )) ||
                                null}
                        </Fragment>
                    )}
                </Form.List>
            </FormItem> */}
        </Fragment>
    );
};

const ActiveLayout = (props) => {
    const { dispatch, global } = props;
    const { codeInfo } = global;

    const { marketChannelType } = codeInfo;

    useEffect(() => {
        if (!marketChannelType) {
            dispatch({
                type: 'global/initCode',
                code: 'marketChannelType',
            });
        }
    }, []);

    const marketChannelTypeOptions = useMemo(() => {
        if (marketChannelType instanceof Array) {
            return marketChannelType.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [marketChannelType]);
    return (
        <Fragment>
            <FormItem label="推广渠道:" name="marketChannel" {...formItemFixedWidthLayout}>
                <Select placeholder="请选择">{marketChannelTypeOptions}</Select>
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.marketChannel !== curValues.marketChannel
                }
            >
                {({ getFieldValue }) => {
                    const marketChannel = getFieldValue('marketChannel');
                    let link = `/pagesPaymember/paymember/pay`;
                    if (marketChannel) {
                        link = `/pagesPaymember/paymember/pay?marketChannel=${marketChannel}`;
                    }
                    let alipayLink = `alipays://platformapi/startapp?appId=2019092067648076&page=${link}`;
                    return (
                        <Fragment>
                            <FormItem label="支付宝推广链接:">{alipayLink}</FormItem>
                            <FormItem label="小程序内部链接:">{link}</FormItem>
                        </Fragment>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

const MembershipPlanEditPage = (props) => {
    const {
        dispatch,
        history,
        route,
        match,
        membershipModel: { membershipPlanInfo },
        couponModel,
        global,
    } = props;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [actId, changeActId] = useState(match.params.id || null);

    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy') >= 0) {
            return true;
        }
        return false;
    }, [route]); // 是否可编辑

    const isLock = useMemo(() => {
        if (isCopy) {
            return false;
        }
        if (route.path.indexOf('/detail') >= 0) {
            return true;
        }
        if (route.path.indexOf('/update') >= 0 && membershipPlanInfo?.actState > 2) {
            return true;
        }
        return false;
    }, [route, membershipPlanInfo, isCopy]); // 是否可编辑

    const openSomeThing = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return false;
        }
        return !membershipPlanInfo || membershipPlanInfo?.actState == 2 || !isLock;
    }, [membershipPlanInfo, isLock]); // 是否可部分编辑

    // 初始化
    useEffect(() => {
        loadData();
        return () => {
            dispatch({
                type: 'membershipModel/updateMembershipProperty',
                params: { membershipPlanInfo: undefined },
            });
        };
    }, []);

    const loadData = () => {
        if (actId) {
            dispatch({
                type: 'membershipModel/getMembershipPlanDetail',
                options: { actId },
            });
        } else {
            // initAddInfo();
        }
    };

    // 初始化详情
    useEffect(() => {
        if (membershipPlanInfo) {
            const params = {
                ...membershipPlanInfo,
                vipOpenFlag: membershipPlanInfo.vipOpenFlag === '1' ? true : false,
            };

            if (membershipPlanInfo.vipActiveCrowd) {
                params.vipActiveCrowd = !isEmpty(membershipPlanInfo.vipActiveCrowd)
                    ? membershipPlanInfo.vipActiveCrowd?.split(',')
                    : [];
            }

            if (membershipPlanInfo.effTime && membershipPlanInfo.expTime) {
                params.dateTime = [
                    moment(membershipPlanInfo.effTime),
                    moment(membershipPlanInfo.expTime),
                ];
                params.effTime = undefined;
            }
            if (membershipPlanInfo.vipDay?.indexOf(',') > -1) {
                params.vipDay = membershipPlanInfo.vipDay.split(',');
            }
            if (isEmpty(membershipPlanInfo.vipDayCityList)) {
                params.vipDayCityList = [];
            }

            if (membershipPlanInfo.addPrizeList) {
                const list = membershipPlanInfo.addPrizeList.map((ele) => {
                    const options = { ...ele };
                    return options;
                });
                if (!list?.length) {
                    list.push({ addPrizeSn: '1' });
                }
                const two = list.findIndex((ele) => ele.addPrizeSn == '2');
                if (two >= 0) {
                    list[two].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '2' });
                }
                const three = list.findIndex((ele) => ele.addPrizeSn == '3');
                if (three >= 0) {
                    list[three].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '3' });
                }
                params.addPrizeList = list;
            }

            if (membershipPlanInfo.vipGroupList) {
                const list = membershipPlanInfo.vipGroupList.map((ele) => {
                    const options = { ...ele, city: ele?.city?.split(',') || [] };
                    return options;
                });

                params.vipGroupList = list;
            }

            form.setFieldsValue(params);
        }
    }, [membershipPlanInfo]);

    /**
     * 保存
     * type  save/send
     */
    const saveGiftEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        form.validateFields()
            .then(async (values) => {
                const params = {
                    ...values,
                    effTime:
                        (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                    expTime:
                        (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                    dateTime: undefined,
                    marketChannel: values.marketChannel,
                };
                if (values.prizeList) {
                    params.prizeListStr = JSON.stringify(values.prizeList);
                    params.prizeList = undefined;
                }
                if (values.addPrizeList) {
                    const list = [];
                    for (const item of values.addPrizeList) {
                        const subList = [];
                        let tempItem = { ...item };
                        if (item.addPrizes instanceof Array && item.addPrizes.length > 0) {
                            if (
                                item.addPrizeSn == 3 &&
                                (!values.addPrizeList[1].addPrizes ||
                                    values.addPrizeList[1].addPrizes.length == 0)
                            ) {
                                message.error('加量包2未填写');
                                return;
                            }
                            for (const ele of item.addPrizes) {
                                subList.push({ ...ele });
                            }
                            tempItem.addPrizes = JSON.stringify(subList);
                            list.push(tempItem);
                        }
                    }
                    params.addPrizeListStr = JSON.stringify(list);
                    params.addPrizeList = undefined;
                }
                if (values.vipGroupList) {
                    params.vipGroupListStr = JSON.stringify(values.vipGroupList);
                    params.vipGroupList = undefined;
                }
                if (values.vipCityList) {
                    params.vipCityListStr = JSON.stringify(values.vipCityList);
                    params.vipCityList = undefined;
                }
                if (values.vipDayCityList) {
                    params.vipDayCityListStr = JSON.stringify(values.vipDayCityList);
                    params.vipDayCityList = undefined;
                }
                if (values.vipDayFlag == 0 && values.vipDay?.length && values.vipDay.join) {
                    params.vipDay = values.vipDay.join(',');
                }

                params.vipOpenFlag = values.vipOpenFlag ? '1' : '0';

                if (values.vipOpenFlag) {
                    params.actCycle = values.actCycle;
                    params.inviteTerm = values.inviteTerm;

                    params.discountLimitAmtDay = values.discountLimitAmtDay;

                    params.vipActiveCrowd = values.vipActiveCrowd.join(',');
                }

                if (type == 'save') {
                    params.actState = '0';
                } else if (type == 'submit') {
                    params.actState = '1';
                }
                if (!isCopy && actId) {
                    params.actId = actId;
                }
                if (isCopy) {
                    delete params.actState;
                }

                changeSubmitLoading(true);
                const {
                    data: { actId: id },
                } = await saveMembershipPlanApi(params);
                changeSubmitLoading(false);
                changeActId(id);

                if (type == 'save') {
                    message.success('保存成功');
                } else if (type == 'submit') {
                    message.success('提交成功');
                    goBack();
                }
            })
            .catch((e) => {
                console.log(e);
                changeSubmitLoading(false);
            });
    };

    const goBack = () => {
        history.replace('/userCenter/membership/plan/list');
    };

    const putCpnList = useMemo(() => {
        const inviteds = membershipPlanInfo?.prizeList?.map((ele) => ({
            cpnNo: ele.cpnNo,
            actTaskState: '1',
            cpnAddType: '02',
            ...ele,
        }));
        return inviteds || [];
    }, [membershipPlanInfo]);

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    initialValues={{
                        contentLink: LINK_TYPES.GIFT,
                    }}
                    scrollToFirstError
                >
                    <h1 className={commonStyles['form-title']}>活动信息</h1>
                    <InfoFormLayout
                        form={form}
                        membershipPlanInfo={membershipPlanInfo}
                        isLock={isLock}
                    />

                    <Divider></Divider>

                    <h1 className={commonStyles['form-title']}>礼包信息</h1>

                    <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                        {({ getFieldValue }) => {
                            // 拼出其他列已选的优惠券列表，在当前加量包不允许继续选中
                            const addPrizeList = getFieldValue('addPrizeList');
                            let otherCpnList = [];
                            for (let i = addPrizeList?.length - 1; i >= 0; i--) {
                                const task = addPrizeList[i];
                                const addPrizes = task?.addPrizes;
                                if (addPrizes?.length) {
                                    otherCpnList = [...addPrizes, ...otherCpnList];
                                }
                            }
                            return (
                                <CouponTable
                                    actId={membershipPlanInfo?.actId}
                                    dispatch={dispatch}
                                    global={global}
                                    couponModel={couponModel}
                                    form={form}
                                    needCheck
                                    actState={membershipPlanInfo?.actState}
                                    editabled={route.path.indexOf('/look') < 0}
                                    isCopy={isCopy}
                                    putCpnList={putCpnList}
                                    formItemLayout={formItemLayout}
                                    title="配置奖品"
                                    name="prizeList"
                                    onRefresh={loadData}
                                    extInParams={{ cpnOwner: '03', vipCouponFlag: '1' }}
                                    disabledIds={otherCpnList.map((ele) => ele.cpnId)}
                                    putColTitle="每人领取数量"
                                />
                            );
                        }}
                    </FormItem>

                    {/* 加量包 */}
                    <Form.List name={'addPrizeList'} initialValue={[{}, {}, {}]}>
                        {(fields, { add, remove }) => (
                            <Fragment>
                                {fields.map((field, index) => (
                                    <Fragment key={index}>
                                        <br></br>
                                        <FormItem>
                                            <Space>
                                                <div
                                                    className={styles.formTitle}
                                                    style={{ margin: '0' }}
                                                >
                                                    加量包{index + 1}
                                                </div>
                                                {index > 0 ? (
                                                    <FormItem
                                                        name={[field.name, 'useInvite']}
                                                        valuePropName={'checked'}
                                                        noStyle
                                                    >
                                                        <Switch
                                                            onChange={(value) => {
                                                                if (!value) {
                                                                    const addPrizeList =
                                                                        form.getFieldValue(
                                                                            'addPrizeList',
                                                                        );
                                                                    const list = [...addPrizeList];
                                                                    list[field.name] = {
                                                                        addPrizeSn: `${index + 1}`,
                                                                    };
                                                                    form.setFieldsValue({
                                                                        addPrizeList: list,
                                                                    });
                                                                }
                                                            }}
                                                            disabled={isLock}
                                                        ></Switch>
                                                    </FormItem>
                                                ) : null}
                                            </Space>
                                        </FormItem>
                                        <FormItem
                                            name={[field.name, 'addPrizeSn']}
                                            initialValue={`${index + 1}`}
                                            noStyle
                                        ></FormItem>
                                        <FormItem
                                            noStyle
                                            shouldUpdate={(prevValues, curValues) =>
                                                (prevValues.addPrizeList &&
                                                    prevValues.addPrizeList[field.name] &&
                                                    prevValues.addPrizeList[field.name]
                                                        .useInvite) !==
                                                (curValues.addPrizeList &&
                                                    curValues.addPrizeList[field.name] &&
                                                    curValues.addPrizeList[field.name].useInvite)
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                const addPrizeList = getFieldValue('addPrizeList');
                                                let hasUse = false;
                                                if (index == 0) {
                                                    hasUse = true;
                                                } else {
                                                    hasUse = addPrizeList[index].useInvite == true;
                                                }
                                                return hasUse ? (
                                                    <PrizeLayout
                                                        form={form}
                                                        fileKey={field.name}
                                                        {...props}
                                                        isLock={isLock}
                                                        isCopy={isCopy}
                                                        onRefresh={loadData}
                                                    />
                                                ) : null;
                                            }}
                                        </FormItem>
                                    </Fragment>
                                ))}
                            </Fragment>
                        )}
                    </Form.List>

                    <Divider></Divider>

                    {/* <ExperienceFormLayout
                        {...props}
                        form={form}
                        membershipPlanInfo={membershipPlanInfo}
                        isLock={isLock}
                    />

                    <Divider></Divider> */}

                    <h1 className={commonStyles['form-title']}>会员配置</h1>
                    <ConfigureFormLayout
                        form={form}
                        membershipPlanInfo={membershipPlanInfo}
                        isLock={isLock}
                    />

                    <Divider></Divider>

                    <h1 className={commonStyles['form-title']}>活动页面</h1>

                    <ActiveLayout {...props}></ActiveLayout>

                    <div className={styles['form-submit']}>
                        <Fragment>
                            {((isCopy ||
                                !membershipPlanInfo ||
                                membershipPlanInfo?.actState == 0) &&
                                !isLock && (
                                    <Button
                                        className={styles['form-btn-left']}
                                        type="primary"
                                        loading={submitLoading}
                                        onClick={() => {
                                            saveGiftEvent('save');
                                        }}
                                    >
                                        保存
                                    </Button>
                                )) ||
                                null}
                            {!isLock && (
                                <Button
                                    className={styles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        confirm({
                                            title: `您确认要提交吗？`,
                                            content: actId ? '' : '提交后方案不可删除',
                                            okText: '确定',
                                            cancelText: '取消',
                                            onOk() {
                                                saveGiftEvent('submit');
                                            },
                                        });
                                    }}
                                >
                                    提交
                                </Button>
                            )}

                            <Button className={styles['form-btn']} onClick={goBack}>
                                取消
                            </Button>
                        </Fragment>
                    </div>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, membershipModel, couponModel }) => ({
    global,
    membershipModel,
    couponModel,
}))(MembershipPlanEditPage);
