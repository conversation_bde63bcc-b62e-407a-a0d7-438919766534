import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Input,
    Select,
    Form,
    DatePicker,
    message,
    Divider,
    Space,
    Modal,
    Switch,
    Tooltip,
    Checkbox,
} from 'antd';

import { LeftOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    saveVipLevelPlanApi,
    saveVipPlanApi,
    checkVipPlanApi,
} from '@/services/Marketing/MarketingMembershipApi';
import commonStyles from '@/assets/styles/common.less';
import styles from '../Membership.less';
import CitysSelect from '@/components/CitysSelect/index.js';
import CityTransferModal from '@/components/CityTransferModal';

import EditActInfoForm from './components/EditActInfoForm';
import EditPrizeForm from './components/EditPrizeForm';
import EditTryVipForm from './components/EditTryVipForm';
import EditConfigureForm from './components/EditConfigureForm';
import EditOpenMethodsForm from './components/EditOpenMethodsForm';
import EditActPageForm from './components/EditActPageForm';
import ActLinkItem from '@/components/ActLinkItem/index';

import { isEmpty } from '@/utils/utils';
import { VIP_TYPES, RIGHTS_TYPES } from '../MembershipRights/RightsConfig';
import { getVipPlanEquityCityApi } from '@/services/Marketing/MarketingMembershipApi';
import {
    ACTSUBTYPES,
    ACT_LINK_TYPES,
    ACT_SUB_STATUS,
    ACT_SUB_TYPE,
    APPLET_ACT_TYPES,
} from '@/config/declare';
import { createQrcodeCommon } from '@/utils/utils';
import EditLevelForm from './components/EditLevelForm';

const { RangePicker } = DatePicker;

const { Option } = Select;
const { confirm } = Modal;

const FormItem = Form.Item;

// 跳转方式
const LINK_TYPES = {
    HOME: '01', // 首页
    CARD: '02', // 我的卡包
    OTHER: '03', // 自定义
    GIFT: '04', // 默认礼包领取页面
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const MembershipPlanEditPage = (props) => {
    const {
        dispatch,
        history,
        route,
        match,
        membershipModel: { membershipPlanInfo },
    } = props;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [actId, changeActId] = useState(match.params.id || null);
    const cityModalRef = useRef();

    useEffect(() => {
        if (props?.location?.state) {
            const type = props?.location?.state?.actSubType;
            form.setFieldsValue({ ...props?.location?.state });
            if (type == VIP_TYPES.VIP_TRY) {
                // 体验版
                form.setFieldsValue({
                    openMethods: [
                        {
                            openType: '07',
                            openAmt: 0,
                        },
                    ],
                });
            } else if (type == VIP_TYPES.VIP_LEVEL) {
                form.setFieldsValue({ actName: '等级会员', actSubType: VIP_TYPES.VIP_LEVEL });
            }
        }
    }, []);

    const isLock = useMemo(() => {
        if (route.path.indexOf('/detail') >= 0) {
            return true;
        }
        return false;
    }, [route]); // 是否可编辑

    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy-vip') >= 0) {
            return true;
        }
        return false;
    }, [route]); // 是否可编辑

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    // 初始化
    useEffect(() => {
        loadData();
        return () => {
            dispatch({
                type: 'membershipModel/updateMembershipProperty',
                params: { membershipPlanInfo: undefined },
            });
        };
    }, []);

    const loadData = () => {
        if (actId) {
            const type = props?.location?.state?.actSubType;
            if (type == VIP_TYPES.VIP_LEVEL) {
                dispatch({
                    type: 'membershipModel/getVipLevelDetail',
                    options: { actId },
                });
            } else {
                dispatch({
                    type: 'membershipModel/getVipPlanDetail',
                    options: { actId },
                });
            }
        } else {
            // initAddInfo();
        }
    };

    // 初始化详情
    useEffect(() => {
        if (membershipPlanInfo) {
            const params = {
                ...membershipPlanInfo,
            };

            if (membershipPlanInfo.upgradeTipFlag) {
                params.upgradeTipFlag = membershipPlanInfo.upgradeTipFlag === '1' ? true : false;
            }

            if (membershipPlanInfo.stopTipFlag) {
                params.stopTipFlag = membershipPlanInfo.stopTipFlag === '1' ? true : false;
            }
            if (membershipPlanInfo.showLabel) {
                params.showLabel = membershipPlanInfo.showLabel === '1' ? true : false;
            }
            if (membershipPlanInfo.showBottomDesc) {
                params.showBottomDesc = membershipPlanInfo.showBottomDesc === '1' ? true : false;
            }

            params.vipExpireSendMsgFlag = membershipPlanInfo.vipExpireSendMsgFlag === '1';

            if (membershipPlanInfo.banners) {
                params.banners = JSON.parse(membershipPlanInfo.banners);
            }
            if (membershipPlanInfo.citys) {
                params.citys = JSON.parse(membershipPlanInfo.citys);
            }
            if (membershipPlanInfo.gains) {
                params.gains = JSON.parse(membershipPlanInfo.gains);
            }
            if (membershipPlanInfo.openMethods) {
                params.openMethods = JSON.parse(membershipPlanInfo.openMethods);
            }

            if (membershipPlanInfo.effTime && membershipPlanInfo.expTime) {
                params.dateTime = [
                    moment(membershipPlanInfo.effTime, 'YYYY-MM-DD'),
                    moment(membershipPlanInfo.expTime, 'YYYY-MM-DD'),
                ];
                params.effTime = undefined;
                params.expTime = undefined;
            }

            if (membershipPlanInfo.vipDay?.indexOf(',') > -1) {
                params.vipDay = membershipPlanInfo.vipDay.split(',');
            }
            if (!membershipPlanInfo.vipDayCityList) {
                params.vipDayCityList = [];
            }

            if (membershipPlanInfo.addPrizeList) {
                const list = membershipPlanInfo.addPrizeList.map((ele) => {
                    const options = { ...ele };
                    return options;
                });
                if (!list?.length) {
                    list.push({ addPrizeSn: '1' });
                }
                const two = list.findIndex((ele) => ele.addPrizeSn == '2');
                if (two >= 0) {
                    list[two].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '2' });
                }
                const three = list.findIndex((ele) => ele.addPrizeSn == '3');
                if (three >= 0) {
                    list[three].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '3' });
                }
                params.addPrizeList = list;
            }

            if (membershipPlanInfo.vipGroupList) {
                const list = membershipPlanInfo.vipGroupList.map((ele) => {
                    const options = { ...ele, city: ele?.city?.split(',') || [] };
                    return options;
                });

                params.vipGroupList = list;
            }

            if (membershipPlanInfo.banners) {
                params.banners = JSON.parse(membershipPlanInfo.banners);
            }

            if (membershipPlanInfo.citys) {
                params.citys = JSON.parse(membershipPlanInfo.citys);
            }

            if (membershipPlanInfo.gains) {
                params.gains = JSON.parse(membershipPlanInfo.gains);
            }

            if (membershipPlanInfo.openMethods) {
                const openMethodsArr = JSON.parse(membershipPlanInfo.openMethods);
                params.openMethods = openMethodsArr?.map((ele) => {
                    ele.showLabel = ele.showLabel === '1' ? true : false;
                    ele.showBottomDesc = ele.showBottomDesc === '1' ? true : false;
                    return ele;
                });
            }

            if (membershipPlanInfo.activeCrowd?.length) {
                params.activeCrowd =
                    membershipPlanInfo.activeCrowd.split?.(',') || membershipPlanInfo.activeCrowd;
            }

            form.setFieldsValue(params);
        }
    }, [membershipPlanInfo]);

    /**
     * 保存
     * type  save/send
     */
    const saveGiftEvent = (type) => {
        if (submitLoading) {
            return;
        }
        form.validateFields()
            .then(async (values) => {
                const params = {
                    ...values,
                    effTime: values?.dateTime?.[0]?.format?.('YYYY-MM-DD'),
                    expTime: values?.dateTime?.[1]?.format?.('YYYY-MM-DD'),
                };
                delete params.dateTime;

                params.upgradeTipFlag = values.upgradeTipFlag ? '1' : '0';
                params.stopTipFlag = values.stopTipFlag ? '1' : '0';
                params.vipExpireSendMsgFlag = values.vipExpireSendMsgFlag ? '1' : '0';

                if (values?.citys?.length) {
                    params.citys = JSON.stringify(values.citys);
                }
                if (values?.openMethods?.length) {
                    const openMethods = values?.openMethods?.map((ele) => {
                        ele.showLabel = ele?.showLabel ? '1' : '0';
                        ele.showBottomDesc = ele?.showBottomDesc ? '1' : '0';
                        return ele;
                    });
                    params.openMethods = JSON.stringify(openMethods);
                }
                if (values?.banners?.length) {
                    const banners = [];
                    values.banners.forEach((ele) => {
                        banners.push({
                            bannerType: ele.bannerType,
                            bannerImg: ele.bannerImg,
                        });
                    });
                    params.banners = JSON.stringify(banners);
                }
                if (values?.gains?.length) {
                    const gains = [];
                    values.gains.forEach((ele) => {
                        gains.push({
                            equityId: ele.equityId,
                            sortId: ele.sortId,
                            gainType: ele.gainType,
                            gainName: ele.gainName,
                            gainTitle: ele.gainTitle,
                            gainIcon: ele.gainIcon,
                            gainDetail: ele.gainDetail,
                            equityImg: ele.equityImg,
                            equityLabelImg: ele.equityLabelImg,
                            equityImgNew: ele.equityImgNew,
                            equityImgNewLinkType: ele.equityImgNewLinkType,
                            equityImgNewUrl: ele.equityImgNewUrl,
                        });
                    });
                    params.gains = JSON.stringify(gains);
                }
                if (values.actSubType == VIP_TYPES.VIP_LEVEL) {
                    if (values.levelDefineBos) {
                        // params.levelDefineParams = values.levelDefineBos;
                        delete params.levelDefineBos;
                    }
                    if (values.levelEquityBos) {
                        params.levelEquityParams = values.levelEquityBos;
                        delete params.levelEquityBos;
                    }
                    if (values.levelBos) {
                        // params.levelParams = values.levelBos;
                        delete params.levelBos;
                    }
                    if (values.levelTaskBos) {
                        // params.levelTaskParams = values.levelTaskBos;
                        delete params.levelTaskBos;
                    }
                }

                if (values?.activeCrowd?.length) {
                    params.activeCrowd = values?.activeCrowd?.join?.(',') || values?.activeCrowd;
                }

                if (type == 'save') {
                    params.submitFlag = false;
                } else if (type == 'submit' || type == 'checkSubmit') {
                    params.submitFlag = true;
                }
                if (!isCopy && actId) {
                    params.actId = actId;
                }
                if (isCopy) {
                    delete params.actState;
                }
                console.log(params, values);
                // return;
                changeSubmitLoading(true);

                if (values.actSubType !== VIP_TYPES.VIP_LEVEL && type == 'checkSubmit') {
                    const {
                        data: { remark: checkError },
                    } = await checkVipPlanApi({
                        actId: params?.actId,
                        citys: params?.citys,
                    });
                    if (checkError) {
                        confirm({
                            title: '确认提交?',
                            icon: <ExclamationCircleOutlined />,
                            content: checkError,
                            onOk() {
                                changeSubmitLoading(false);

                                saveGiftEvent('submit');
                            },
                            onCancel() {
                                console.log('Cancel');
                                changeSubmitLoading(false);
                            },
                        });
                        return;
                    }
                }

                const {
                    data: { actId: id },
                } = await (values.actSubType == VIP_TYPES.VIP_LEVEL
                    ? saveVipLevelPlanApi(params)
                    : saveVipPlanApi(params));
                changeSubmitLoading(false);
                changeActId(id);

                if (type == 'save') {
                    message.success('保存成功');
                    if (isCopy || !actId) {
                        goBack();
                    }
                } else if (type == 'submit' || type == 'checkSubmit') {
                    message.success('提交成功');
                    goBack();
                }
            })
            .catch((e) => {
                console.log(e);
                changeSubmitLoading(false);
            });
    };

    const goBack = () => {
        if (props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL) {
            history.replace('/userCenter/membership/vip-level/list');
        } else {
            history.replace('/userCenter/membership/plan/list');
        }
    };

    const methodsRef = useRef();

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    scrollToFirstError
                    initialValues={{ vipExpireSendMsgFlag: true }}
                    onFieldsChange={(values, allFields) => {
                        form.getFieldsValue();
                    }}
                >
                    <FormItem name="actState" noStyle />
                    <h1 className={commonStyles['form-title']}>基本信息</h1>
                    <FormItem
                        label="方案类型"
                        name="actSubType"
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, message: '请选择方案类型' }]}
                    >
                        {props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL ? (
                            '等级会员'
                        ) : (
                            <Select
                                placeholder="请选择"
                                onChange={(value) => {
                                    const openMethods = form.getFieldValue('openMethods');

                                    if (value == VIP_TYPES.VIP_TRY) {
                                        // 体验版
                                        form.setFieldsValue({
                                            openMethods: [
                                                {
                                                    openType: '07',
                                                    openAmt: 0,
                                                },
                                            ],
                                        });
                                    } else if (value == VIP_TYPES.VIP_LEVEL) {
                                        form.setFieldsValue({ actName: '等级会员' });
                                    } else if (
                                        openMethods instanceof Array &&
                                        openMethods?.some((ele) => ele.openType == '07')
                                    ) {
                                        openMethods.splice(
                                            openMethods.indexOf(
                                                openMethods.findIndex(
                                                    (ele) => ele.openType == '07',
                                                ),
                                            ),
                                        );
                                        if (!openMethods?.length) {
                                            openMethods.push({});
                                        }
                                        form.setFieldsValue({ openMethods });
                                    }
                                }}
                                disabled={!isCopy && (membershipPlanInfo?.actState >= 2 || isLock)}
                            >
                                <Option key={VIP_TYPES.VIP_2_0} value={VIP_TYPES.VIP_2_0}>
                                    会员方案2.0
                                </Option>
                                <Option key={VIP_TYPES.VIP_TRY} value={VIP_TYPES.VIP_TRY}>
                                    体验版方案
                                </Option>
                            </Select>
                        )}
                    </FormItem>

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.actSubType !== curValues.actSubType ||
                            prevValues.actSuactNamebType !== curValues.actName ||
                            prevValues.unionVipFlag !== curValues.unionVipFlag
                        }
                    >
                        {({ getFieldValue }) => {
                            const actSubType = getFieldValue('actSubType');
                            const actName = getFieldValue('actName');
                            const unionVipFlag = getFieldValue('unionVipFlag');
                            return actSubType == VIP_TYPES.VIP_LEVEL ? (
                                <FormItem
                                    label="方案名称"
                                    name="actName"
                                    {...formItemFixedWidthLayout}
                                    rules={[{ required: true, message: '请填写活动名称' }]}
                                >
                                    {actName}
                                </FormItem>
                            ) : (
                                <Fragment>
                                    <FormItem
                                        label="方案名称"
                                        name="actName"
                                        {...formItemFixedWidthLayout}
                                        rules={[{ required: true, message: '请填写活动名称' }]}
                                    >
                                        <Input
                                            disabled={
                                                !isCopy &&
                                                (membershipPlanInfo?.actState > 2 || isLock)
                                            }
                                            placeholder="请输入"
                                            autoComplete="off"
                                            maxLength={10}
                                            showCount
                                        />
                                    </FormItem>

                                    <FormItem
                                        label="副标题"
                                        name="actResume"
                                        {...formItemFixedWidthLayout}
                                        rules={[{ required: true, message: '请填写副标题' }]}
                                    >
                                        <Input
                                            disabled={
                                                !isCopy &&
                                                (membershipPlanInfo?.actState > 2 || isLock)
                                            }
                                            placeholder="请输入"
                                            autoComplete="off"
                                            maxLength={10}
                                            showCount
                                        />
                                    </FormItem>

                                    <FormItem
                                        label="方案有效期"
                                        {...formItemFixedWidthLayout}
                                        required
                                        name="dateTime"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请选择活动有效期');
                                                    }
                                                    if (!value[0]) {
                                                        return Promise.reject('请选择活动开始日期');
                                                    }
                                                    if (!value[1]) {
                                                        return Promise.reject('请选择活动失效日期');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <RangePicker
                                            disabled={[
                                                !isCopy &&
                                                    (membershipPlanInfo?.actState >= 2 || isLock),
                                                !isCopy &&
                                                    (membershipPlanInfo?.actState > 2 || isLock),
                                            ]}
                                            format="YYYY-MM-DD"
                                        />
                                    </FormItem>
                                    <Form.Item label={'联合会员标识'}>
                                        <Space direction="vertical" style={{ width: '100%' }}>
                                            <Form.Item noStyle>
                                                <Space style={{ lineHeight: '32px' }}>
                                                    <Form.Item
                                                        name="unionVipFlag"
                                                        valuePropName="checked"
                                                        noStyle
                                                    >
                                                        <Checkbox
                                                            checked={unionVipFlag}
                                                            onChange={(e) => {
                                                                if (!e.target.checked) {
                                                                    const gains =
                                                                        form.getFieldValue('gains');
                                                                    form.setFieldValue(
                                                                        'gains',
                                                                        gains.filter(
                                                                            (v) =>
                                                                                v.gainType !==
                                                                                RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                        ),
                                                                    );
                                                                    form.setFieldValue(
                                                                        'actLbl',
                                                                        undefined,
                                                                    );
                                                                }
                                                            }}
                                                        ></Checkbox>
                                                    </Form.Item>
                                                    {unionVipFlag && (
                                                        <Form.Item
                                                            name={'actLbl'}
                                                            noStyle
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择联合会员类型',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                style={{ width: '200px' }}
                                                                allowClear
                                                                placeholder="请选择"
                                                                disabled={
                                                                    !isCopy &&
                                                                    (membershipPlanInfo?.actState >=
                                                                        2 ||
                                                                        isLock)
                                                                }
                                                                onChange={(value) => {
                                                                    const gains =
                                                                        form.getFieldValue('gains');
                                                                    if (value == '03') {
                                                                        if (
                                                                            gains &&
                                                                            !gains.find(
                                                                                (v) =>
                                                                                    v.gainType ===
                                                                                    RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                            )
                                                                        ) {
                                                                            gains.push({
                                                                                gainType:
                                                                                    RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                            });
                                                                        }
                                                                        form.setFieldValue(
                                                                            'gains',
                                                                            gains.filter(
                                                                                (v) =>
                                                                                    v.gainType !==
                                                                                        RIGHTS_TYPES.MEMBER_PROPR &&
                                                                                    !isEmpty(v),
                                                                            ),
                                                                        );
                                                                    } else {
                                                                        form.setFieldValue(
                                                                            'gains',
                                                                            gains.filter(
                                                                                (v) =>
                                                                                    v.gainType !==
                                                                                    RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                            ),
                                                                        );
                                                                    }
                                                                }}
                                                            >
                                                                <Option key={'03'} value={'03'}>
                                                                    支付宝联合会员
                                                                </Option>
                                                            </Select>
                                                        </Form.Item>
                                                    )}
                                                </Space>
                                            </Form.Item>
                                        </Space>
                                    </Form.Item>
                                </Fragment>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.citys !== curValues.citys ||
                            prevValues.actSubType !== curValues.actSubType
                        }
                    >
                        {({ getFieldValue }) => {
                            const actSubType = getFieldValue('actSubType');
                            if (actSubType == VIP_TYPES.VIP_LEVEL) {
                                return null;
                            }
                            const citys = getFieldValue('citys');
                            return (
                                <FormItem
                                    label="开通城市"
                                    name="citys"
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!citys?.length) {
                                                    return Promise.reject('请选择开通城市');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    {(citys?.length && (
                                        <Space size="small">
                                            {((isCopy ||
                                                !membershipPlanInfo ||
                                                membershipPlanInfo?.actState <= 2) &&
                                                !isLock && (
                                                    <Button
                                                        type="link"
                                                        onClick={() => {
                                                            cityModalRef?.current?.show({
                                                                defaultKeys: citys?.map(
                                                                    (ele) => ele.areaCode,
                                                                ),
                                                            });
                                                        }}
                                                    >
                                                        编辑
                                                    </Button>
                                                )) ||
                                                null}
                                            {((isCopy ||
                                                !membershipPlanInfo ||
                                                membershipPlanInfo?.actState <= 2) &&
                                                !isLock && (
                                                    <Divider
                                                        type="vertical"
                                                        style={{ margin: 0 }}
                                                    />
                                                )) ||
                                                null}
                                            <Button
                                                type="link"
                                                onClick={() =>
                                                    cityModalRef?.current?.show({
                                                        defaultKeys: citys?.map(
                                                            (ele) => ele.areaCode,
                                                        ),
                                                        disabled: true,
                                                    })
                                                }
                                            >
                                                查看
                                            </Button>
                                        </Space>
                                    )) ||
                                        ((!membershipPlanInfo ||
                                            membershipPlanInfo?.actState <= 2) && (
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    cityModalRef?.current?.show();
                                                }}
                                                disabled={isLock}
                                            >
                                                城市
                                            </Button>
                                        )) ||
                                        null}
                                </FormItem>
                            );
                        }}
                    </FormItem>
                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.actSubType !== curValues.actSubType ||
                            prevValues.openMethods !== curValues.openMethods
                        }
                    >
                        {({ getFieldValue }) => {
                            const actSubType = getFieldValue('actSubType');
                            const openMethods = getFieldValue('openMethods');

                            const paymemberQrcodePath = 'https://act.xdtev.com/paymember-pay';

                            if (actSubType == VIP_TYPES.VIP_1_0) {
                                return (
                                    <Fragment>
                                        <EditActInfoForm
                                            {...props}
                                            form={form}
                                            isLock={isLock}
                                            formItemLayout={formItemLayout}
                                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                                        />

                                        <EditPrizeForm
                                            {...props}
                                            form={form}
                                            isLock={isLock}
                                            formItemLayout={formItemLayout}
                                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                                            onRefresh={loadData}
                                        />

                                        <EditConfigureForm
                                            {...props}
                                            form={form}
                                            isLock={isLock}
                                            formItemLayout={formItemLayout}
                                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                                            equityNo={RIGHTS_TYPES.MEMBER_DAY}
                                        />

                                        <Divider />
                                        <h1 className={commonStyles['form-title']}>活动页面</h1>
                                        <ActLinkItem
                                            actId={actId}
                                            actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                            disableds={[
                                                ACT_LINK_TYPES.BAIDU,
                                                ACT_LINK_TYPES.BAIDU_MAP,
                                            ]}
                                            path="/pagesPaymember/paymember/pay"
                                            wrapperCol={{
                                                span: 24,
                                            }}
                                            readOnly={isLock}
                                        />
                                    </Fragment>
                                );
                            } else if (actSubType == VIP_TYPES.VIP_2_0) {
                                return (
                                    <Fragment>
                                        <FormItem
                                            name="openMethods"
                                            label="开通方式"
                                            wrapperCol={{
                                                span: 16,
                                            }}
                                            rules={[{ required: true, message: '请配置开通方式' }]}
                                            initialValue={[{}]}
                                        >
                                            <EditOpenMethodsForm
                                                ref={methodsRef}
                                                disabled={
                                                    !isCopy &&
                                                    (membershipPlanInfo?.actState >= 2 || isLock)
                                                }
                                                readOnly={isLock}
                                            ></EditOpenMethodsForm>
                                        </FormItem>

                                        <FormItem
                                            label={
                                                <Tooltip title="开启后在途连续包月用户展示升级提醒">
                                                    升级提醒
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            }
                                            name="upgradeTipFlag"
                                            valuePropName="checked"
                                        >
                                            <Switch
                                                disabled={
                                                    membershipPlanInfo?.actState > 2 || isLock
                                                }
                                            ></Switch>
                                        </FormItem>

                                        <FormItem
                                            labelCol={{ flex: '0 0 200px' }}
                                            label={
                                                <Tooltip title="开启时，用户拒绝升级时，用户原方案到期时终止续费">
                                                    拒绝升级时终止原方案续费
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            }
                                            name="stopTipFlag"
                                            valuePropName="checked"
                                        >
                                            {/* 草稿和新建支持编辑，未开始、进行中，如果是关闭状态支持开启 */}
                                            <Switch disabled={isLock}></Switch>
                                        </FormItem>

                                        {openMethods?.some(
                                            (ele) => ele.openType == '04' || ele.openType == '05',
                                        ) ? (
                                            <FormItem
                                                labelCol={{ flex: '0 0 200px' }}
                                                label={
                                                    <Tooltip title="仅对连续包月、连续包季&超过80天内没有充电记录的会员用户在代扣前5天下发短信代扣提醒">
                                                        自动续费短信提醒
                                                        <InfoCircleOutlined
                                                            style={{ marginLeft: '6px' }}
                                                        />
                                                    </Tooltip>
                                                }
                                                name="vipExpireSendMsgFlag"
                                                valuePropName="checked"
                                            >
                                                {/* 草稿和新建支持编辑，未开始、进行中，如果是关闭状态支持开启 */}
                                                <Switch disabled={isLock}></Switch>
                                            </FormItem>
                                        ) : null}

                                        <EditActPageForm
                                            {...props}
                                            form={form}
                                            isLock={isLock}
                                            formItemLayout={formItemLayout}
                                        />

                                        <ActLinkItem
                                            actId={actId}
                                            actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                            mergePath={paymemberQrcodePath}
                                            mergeLabel={'支付宝&微信外部投放'}
                                            disableds={[
                                                ACT_LINK_TYPES.BAIDU,
                                                ACT_LINK_TYPES.BAIDU_MAP,
                                            ]}
                                            path="/pagesPaymember/paymember/pay"
                                            wrapperCol={{
                                                span: 24,
                                            }}
                                            readOnly={isLock}
                                        />
                                    </Fragment>
                                );
                            } else if (actSubType == VIP_TYPES.VIP_TRY) {
                                return (
                                    <Fragment>
                                        <FormItem
                                            name="openMethods"
                                            label="开通方式"
                                            wrapperCol={{
                                                span: 16,
                                            }}
                                            rules={[{ required: true, message: '请配置开通方式' }]}
                                            initialValue={[
                                                {
                                                    openType: '07',
                                                    openAmt: 0,
                                                },
                                            ]}
                                        >
                                            <EditOpenMethodsForm
                                                disabled
                                                readOnly={isLock}
                                            ></EditOpenMethodsForm>
                                        </FormItem>

                                        <EditTryVipForm
                                            {...props}
                                            form={form}
                                            isLock={isLock}
                                            formItemLayout={formItemLayout}
                                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                                            showTitle
                                        />

                                        <Divider />
                                        <h1 className={commonStyles['form-title']}>活动页面</h1>
                                        <ActLinkItem
                                            actId={actId}
                                            actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                            disableds={[
                                                ACT_LINK_TYPES.BAIDU,
                                                ACT_LINK_TYPES.BAIDU_MAP,
                                            ]}
                                            path="/pagesPaymember/paymember/probationCheck"
                                            wrapperCol={{
                                                span: 24,
                                            }}
                                            readOnly={isLock}
                                        />
                                    </Fragment>
                                );
                            } else if (actSubType == VIP_TYPES.VIP_LEVEL) {
                                return <EditLevelForm isLock={isLock} form={form} />;
                            }
                            return null;
                        }}
                    </FormItem>

                    <div className={styles['form-submit']}>
                        <Fragment>
                            {((isCopy ||
                                !membershipPlanInfo ||
                                membershipPlanInfo?.actState == 0) &&
                                props?.location?.state?.actSubType != VIP_TYPES.VIP_LEVEL && (
                                    <Button
                                        className={styles['form-btn-left']}
                                        type="primary"
                                        loading={submitLoading}
                                        onClick={() => {
                                            saveGiftEvent('save');
                                        }}
                                    >
                                        保存
                                    </Button>
                                )) ||
                                null}
                            {(isCopy ||
                                !membershipPlanInfo ||
                                membershipPlanInfo?.actState <= 2 ||
                                props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL) &&
                                !isLock && (
                                    <Button
                                        className={styles['form-btn']}
                                        type="primary"
                                        loading={submitLoading}
                                        onClick={async () => {
                                            form.validateFields().then(() => {
                                                confirm({
                                                    title: `您确认要提交吗？`,
                                                    content: actId
                                                        ? form.getFieldValue('actSubType') ==
                                                          VIP_TYPES.VIP_2_0
                                                            ? '修改内容将于生效时间生效，如您新增了城市，请根据实际需要，在生效日期前完善加量包、天天领红包权益的配置！'
                                                            : ''
                                                        : '提交后方案不可删除',
                                                    okText: '确定',
                                                    cancelText: '取消',
                                                    onOk() {
                                                        saveGiftEvent('checkSubmit');
                                                    },
                                                });
                                            });
                                        }}
                                    >
                                        提交
                                    </Button>
                                )}

                            <Button className={styles['form-btn']} onClick={goBack}>
                                取消
                            </Button>
                        </Fragment>
                    </div>
                </Form>
            </Card>

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    form.setFieldsValue({ citys });
                }}
                request={async (params = {}) => {
                    try {
                        const {
                            data: { areaList },
                        } = await getVipPlanEquityCityApi(params);

                        return areaList;
                    } catch (error) {
                        return [];
                    }
                }}
                deleteEnabled={false}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, membershipModel, couponModel }) => ({
    global,
    membershipModel,
    couponModel,
}))(MembershipPlanEditPage);
