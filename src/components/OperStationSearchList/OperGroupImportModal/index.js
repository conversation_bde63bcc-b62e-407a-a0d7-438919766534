import React, { Fragment, useState, useMemo, useImperativeHandle, useEffect, useRef } from 'react';
import {
    Form,
    Modal,
    Space,
    Input,
    Button,
    Popconfirm,
    message,
    Typography,
    Row,
    Col,
    Switch,
    Popover,
    Radio,
    Select,
} from 'antd';
import PropTypes from 'prop-types';
import { PlusOutlined } from '@ant-design/icons';
import ImportModal from '@/components/ImportModal';
import { stationImportApi } from '@/services/CommonApi';
import TablePro from '@/components/TablePro';
import OperSelectItem from '@/components/OperSelectItem';
import { isEmpty, localDownloadFileToExcel, randomStringCommon } from '@/utils/utils';
import { SELECT_TYPES, MATCH_TYPES } from '@/config/declare';
import { initIcnChannelApi } from '@/services/SallerCenter/IcnApi.js';

import { useRequest } from 'ahooks';

import { connect } from 'dva';

//导入类型
const ImportTypes = {
    Name: '1',
    ID: '2',
};

const FormItem = Form.Item;

const { TextArea } = Input;
const { confirm } = Modal;
const { Title, Text } = Typography;

const limitFormLayout = {
    labelCol: {
        span: 8,
    },
    wrapperCol: {
        span: 14,
    },
};

const OperGroupImportModal = (props) => {
    const {
        dispatch,
        initRef,
        onConfirm,
        title = '批量导入',
        purchase,
        global: { operatorList = [] },
        currentUser,
        isLimit, // 是否要加入限制条件
        limitOperIds,
        limitCitys,
        exceptOperIds, // 限制哪几个运营商不可选中，打折立减的临时解决方案，只需传operId数组
        disabledStationIds,
        limitCityIds,
        hasReasonText = '场站已被添加或关联',
        maxTagCount, //服务商选择框最大数量，超过隐藏
        maxTagPlaceholder, //超过显示文案
        premiumFlag, // 溢价分成规则生效中
        isChannelRule,
        cooperationPlatform,
        buildId, // 用于 场站规则配置-批量查询-导入查询 只匹配当前运营商的场站数据
        importApi = stationImportApi,
        importExtra, //批量导入按钮扩展
        isContainAbandon,
        closeRemark,
    } = props;

    const [limitForm] = Form.useForm(); // 限制条件
    const [operForm] = Form.useForm();

    const [matchStations, updateMatchStations] = useState([]); // 存储所有已匹配到的场站
    const [unmatchStations, updateUnmatchStations] = useState([]); // 存储所有未匹配到的场站
    const [doubleMatchStations, updateDoubleMatchStations] = useState([]); // 存储所有重复匹配到的场站

    const finalDoubleMatecedStations = useMemo(() => {
        // 由于存在同样名称的场站，但是归属不同运营商，如果执行删除操作，则把唯一存在的场站reason改为已匹配
        const stations = doubleMatchStations.map((item) => {
            if (
                doubleMatchStations.some(
                    (subItem) =>
                        subItem.stationName == item.stationName &&
                        subItem.stationId != item.stationId,
                )
            ) {
                // 容器里面存在重复场站，依旧提示
                return item;
            }
            const newItem = { ...item };
            newItem.matchFlag = MATCH_TYPES.MATCHED;
            newItem.reason = '匹配';
            return newItem;
        });
        return stations;
    }, [doubleMatchStations]);
    const allStations = useMemo(() => {
        return [...unmatchStations, ...finalDoubleMatecedStations, ...matchStations];
    }, [matchStations, finalDoubleMatecedStations, unmatchStations]); // 存储所有已记录的场站

    const [preSelectStations, updatePreSelectStations] = useState([]); // 已选中的场站列表，用于添加时判断是否重复选择
    const [showModal, toggleShowModal] = useState(false); // 选择奖品弹窗
    const [searchKey, updateSearchKey] = useState(''); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
    const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const importModalRef = useRef();

    const [textForm] = Form.useForm(); // 文本输入
    const [showTextModal, toggleShowTextModal] = useState(false); // 选择奖品弹窗
    const [textUploading, updateTextUploading] = useState(false); // 文本是否正在上传

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
    };

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);
    useEffect(() => {
        if (limitCitys) {
        }
    }, [limitCitys]);

    useImperativeHandle(initRef, () => ({
        show: (stations) => {
            toggleShowModal(true);
            updatePreSelectStations(stations || []);
            changePageNum(1);
            updateSearchKey('');
            operForm.resetFields();
            limitForm.resetFields();
            updateMatchStations([]);
            updateUnmatchStations([]);
            updateDoubleMatchStations([]);
            updateSelectedRowKeys([]);

            if (isChannelRule) {
                loadChannelList();
            }

            if (limitOperIds?.length) {
                limitForm.setFieldsValue({ buildIds: limitOperIds.map((ele) => ele.operId) });
            }
        },

        close: () => {
            toggleShowModal(false);
        },
    }));

    // 用于过滤出要显示的场站，兼容查询操作
    const showStations = useMemo(() => {
        if (searchKey?.length) {
            const searchList = allStations.filter(
                (item) => item.stationName && item.stationName.indexOf(searchKey) != -1,
            );
            return searchList;
        }
        return allStations || [];
    }, [allStations, searchKey]);

    const importSuccess = (list) => {
        const matchArr = [];
        const unmatchArr = [];
        const doubleMatchArr = []; // 重复匹配

        list?.forEach((ele) => {
            ele.readId = ele.stationId || randomStringCommon();
            if (ele.reason?.indexOf('已废弃') > 0) {
                ele.matchFlag = MATCH_TYPES.ABANDON;
            }
            const doubleArr = [
                ...matchArr,
                ...unmatchArr,
                ...doubleMatchArr,
                // ...matchStations,
                // ...unmatchStations,
                // ...doubleMatchStations,
            ].filter((item) => {
                if (
                    item.matchFlag == MATCH_TYPES.MATCHED ||
                    item.matchFlag == MATCH_TYPES.ABANDON ||
                    item.matchFlag == MATCH_TYPES.DOUBLEMACHED
                ) {
                    return item.stationId == ele.stationId && item.buildId == ele.buildId;
                }
                // 如果是未匹配，不返回id，所以要用名称来匹配

                return (
                    ((ele.stationName && item.stationName == ele.stationName) ||
                        (ele.stationId && item.stationId == ele.stationId)) &&
                    item.reason == ele.reason
                );
            }); // 记录重合的数组
            if (doubleArr?.length) {
                // 如果有重合数组，先移除，再添加
                doubleArr.forEach((item) => {
                    const arr = [
                        matchStations,
                        matchArr,
                        unmatchStations,
                        unmatchArr,
                        doubleMatchStations,
                        doubleMatchArr,
                    ];
                    arr.forEach((subArr) => {
                        const index = subArr.indexOf(item);

                        if (index >= 0) {
                            subArr.splice(index, 1);
                        }
                    });
                });
            }

            if (ele?.stationName?.length || ele?.stationId || ele?.buildName?.length) {
                if (ele.matchFlag != MATCH_TYPES.UNMATCHED) {
                    if (exceptOperIds?.some((exceptEle) => exceptEle.operId == ele.buildId)) {
                        ele.matchFlag = MATCH_TYPES.UNMATCHED;
                        ele.reason = '不支持导入当前运营商';
                    } else if (purchase == SELECT_TYPES.EXCEPTBUY && ele.cooperationType == '02') {
                        ele.matchFlag = MATCH_TYPES.UNMATCHED;
                        ele.reason = '活动范围是运营商时，不支持导入购电模式场站';
                    }
                }

                // 城市分润需求，限制城市+已选场站判重
                if (
                    ele.matchFlag != MATCH_TYPES.UNMATCHED &&
                    (disabledStationIds || limitCityIds)
                ) {
                    if (disabledStationIds?.some((stationId) => stationId == ele.stationId)) {
                        ele.matchFlag = MATCH_TYPES.UNMATCHED;
                        ele.reason = hasReasonText;
                    } else if (
                        limitCityIds &&
                        limitCityIds.some((cityId) => cityId == ele.city) == false
                    ) {
                        ele.matchFlag = MATCH_TYPES.UNMATCHED;
                        ele.reason = '城市不匹配';
                    }
                }

                // 如果是批量查询，并且当前场站是已废弃的，也可支持查询
                if (
                    ele.matchFlag == MATCH_TYPES.MATCHED ||
                    (title === '批量查询' && ele.matchFlag == MATCH_TYPES.ABANDON)
                ) {
                    matchArr.push(ele);
                } else if (ele.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    doubleMatchArr.push(ele);
                } else {
                    unmatchArr.push(ele);
                }
            }
        });
        // 每次导入后都是最新的列表，覆盖之前的数据
        updateMatchStations([...matchArr]);
        updateDoubleMatchStations([...doubleMatchArr]);
        updateUnmatchStations([...unmatchArr]);
        changePageNum(1);
    };

    const stationColumns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '运营商 ',
            dataIndex: 'buildName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{record?.buildName || record?.operName}</span>;
            },
        },
        {
            title: '城市 ',
            dataIndex: 'cityName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站 ',
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站ID ',
            dataIndex: 'stationId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态 ',
            dataIndex: 'reason',
            render(text, record) {
                let color = '#cccccc';
                if (record.matchFlag == MATCH_TYPES.UNMATCHED) {
                    // 红色
                    color = '#f50000';
                } else if (record.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    // 黄色
                    color = '#ff9901';
                } else if (record.matchFlag == MATCH_TYPES.MATCHED) {
                    // 绿色
                    color = '#87d068';
                } else if (record.matchFlag == MATCH_TYPES.ABANDON) {
                    if (title === '批量查询') {
                        color = '#87d068';
                    } else {
                        color = '#f50000';
                    }
                }

                return (
                    <span style={{ color }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '操作 ',
            fixed: 'right',
            render(text, record, index) {
                return (
                    <Popconfirm
                        title={`确定要删除此场站？`}
                        onConfirm={() => {
                            updateMatchStations(deleteObjFromArray(matchStations, record));
                            updateDoubleMatchStations(
                                deleteObjFromArray(doubleMatchStations, record),
                            );

                            updateUnmatchStations(deleteObjFromArray(unmatchStations, record));
                        }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                );
            },
        },
    ];

    // 有未匹配的场站
    const hasUnmatchStations = useMemo(() => {
        return unmatchStations.length > 0;
    }, [unmatchStations]);

    // 有选中的场站
    const hasSelectedStations = useMemo(() => {
        return selectedRowKeys.length > 0;
    }, [selectedRowKeys]);

    // 有之前已导入的场站
    const hasAlreadyStations = useMemo(() => {
        const allStationList = [...finalDoubleMatecedStations, ...matchStations];
        for (let index = 0; index < allStationList.length; index++) {
            const stationItem = allStationList[index];

            // 查询是否已添加过
            const res = preSelectStations?.filter(
                (showItem) => stationItem.stationId == showItem.stationId,
            );
            if (res?.length > 0) {
                return true;
            }
        }
        return false;
    }, [finalDoubleMatecedStations, matchStations]);

    const onSelectChange = (_selectedRowKeys) => {
        updateSelectedRowKeys(_selectedRowKeys);
    };

    const deleteObjFromArray = (arr = [], obj) => {
        let list = [...arr];
        let index = list.findIndex((ele) => ele.readId == obj?.readId);
        if (index >= 0) {
            list.splice(index, 1);
        } else {
        }
        return list;
    };

    // 批量删除已勾选场站
    const deleteSelectStations = async () => {
        let total = 0;
        let unmatchStationsList = [...unmatchStations];
        let doubleMatchStationsList = [...doubleMatchStations];

        let matchStationsList = [...matchStations];

        for (let i = selectedRowKeys.length - 1; i >= 0; i--) {
            const stationIndex = selectedRowKeys[i];
            const stationObj = showStations.find((ele) => ele.readId == stationIndex);

            const newUnmatchStations = deleteObjFromArray(unmatchStationsList, stationObj);

            if (newUnmatchStations.length != unmatchStations.length) {
                total += 1;
                selectedRowKeys.splice(i, 1);
                updateSelectedRowKeys([...selectedRowKeys]);

                unmatchStationsList = newUnmatchStations;
            }

            const newDoubleMatchStations = deleteObjFromArray(doubleMatchStationsList, stationObj);

            if (newDoubleMatchStations.length != doubleMatchStations.length) {
                total += 1;
                selectedRowKeys.splice(i, 1);
                updateSelectedRowKeys([...selectedRowKeys]);
                doubleMatchStationsList = newDoubleMatchStations;
            }

            const newMatchStations = deleteObjFromArray(matchStationsList, stationObj);

            if (newMatchStations.length != matchStations.length) {
                total += 1;
                selectedRowKeys.splice(i, 1);
                updateSelectedRowKeys([...selectedRowKeys]);
                matchStationsList = newMatchStations;
            }
        }

        updateUnmatchStations([...unmatchStationsList]);
        updateDoubleMatchStations([...doubleMatchStationsList]);
        updateMatchStations([...matchStationsList]);

        message.success(`成功删除${total}条数据`);
        updateSelectedRowKeys([]);
    };

    // 批量删除已导入
    const deleteAlreadyStations = () => {
        const alreadyStations = []; // 记录已存在的场站信息
        const allStationList = [...finalDoubleMatecedStations, ...matchStations];
        allStationList.forEach((stationItem) => {
            // 查询是否已添加过
            const res = preSelectStations?.filter(
                (showItem) => stationItem.stationId == showItem.stationId,
            );
            if (res?.length > 0) {
                alreadyStations.push(stationItem.stationId);
            }
        });

        let total = 0;
        // 如果存在已导入场站，进入判断
        if (alreadyStations.length) {
            for (let i = matchStations.length - 1; i >= 0; i--) {
                let finded = alreadyStations.filter(
                    (stationId) => stationId == matchStations[i].stationId,
                );
                if (finded?.length) {
                    total += 1;
                    matchStations.splice(i, 1);
                    updateMatchStations([...matchStations]);
                }
            }
            for (let i = doubleMatchStations.length - 1; i >= 0; i--) {
                let finded = alreadyStations.filter(
                    (stationId) => stationId == doubleMatchStations[i].stationId,
                );
                if (finded?.length) {
                    total += 1;
                    doubleMatchStations.splice(i, 1);
                    updateDoubleMatchStations([...doubleMatchStations]);
                }
            }
        }
        message.success(`成功删除${total}条数据`);
    };

    const exportFormEvent = () => {
        const tableData = [];
        allStations.forEach((item, index) => {
            const col = {};
            stationColumns.forEach((colItem) => {
                const name = colItem.title;
                const key = colItem.dataIndex;
                if (key) {
                    const value = item[key];
                    col[name] = value;
                }
            });
            tableData.push(col);
        });
        localDownloadFileToExcel({ data: tableData, fileName: '场站列表' });
    };

    const formDataExt = (formData) => {
        if (isContainAbandon) {
            // 判断是否废弃
            formData.append('containAbandon', '1');
        }
        if (purchase === SELECT_TYPES.EXCEPTBUY) {
            formData.append('operFilterType', '01');
        }
        if (premiumFlag === true) {
            formData.append('premiumFlag', '1');
        }
        if (buildId) {
            formData.append('buildId', buildId);
        }
        if (limitCitys) {
            formData.append('cityStr', limitCitys);
            formData.append('city', limitCitys);
        }
        if (!isLimit) return;
        const data = limitForm.getFieldsValue();
        if (data.openFlag) {
            formData.append('openFlag', '1');
        }
        if (data.buildIds?.length) {
            formData.append('buildIds', JSON.stringify(data.buildIds));
        }
        if (data.channelId) {
            formData.append('channelId', data.channelId);
        }
        if (cooperationPlatform) {
            formData.append('cooperationPlatform', cooperationPlatform);
        }
    };

    const [showConfirmModal, toggleConfirmModal] = useState(false); // 选择奖品弹窗
    const [addEnabledStations, updateAddEnabledStations] = useState([]); // 可支持导入的场站列表
    const [addStopStations, updateAddStopStations] = useState([]); // 禁止导入的场站列表
    const onOk = () => {
        const alreadyStations = []; // 记录已存在的场站信息
        let successStations = []; // 可成功导入的场站
        const allStationList = [...finalDoubleMatecedStations, ...matchStations];
        allStationList.forEach((stationItem) => {
            // 查询是否已添加过
            const res = preSelectStations?.filter(
                (showItem) => stationItem.stationId == showItem.stationId,
            );
            if (res?.length > 0) {
                alreadyStations.push(stationItem.stationName);
            } else {
                // 未匹配到
                successStations.push(stationItem);
            }
        });

        const double = finalDoubleMatecedStations.some(
            (item) => item.matchFlag == MATCH_TYPES.DOUBLEMACHED,
        );

        updateAddEnabledStations(successStations);
        updateAddStopStations(alreadyStations);
        if (alreadyStations?.length || unmatchStations?.length || double) {
            toggleConfirmModal(true);
        } else {
            if (closeRemark) {
                const remark = operForm.getFieldValue('closeRemark');
                successStations = successStations.map((ele) => {
                    return {
                        ...ele,
                        closeRemark: remark,
                    };
                });
            }
            onConfirm && onConfirm(successStations);
            initRef?.current.close();
        }
    };

    const { run: loadChannelList, data: commonChannelList } = useRequest(
        async () => {
            try {
                let params = {
                    cooperationPlatform,
                };
                if (isChannelRule) {
                    params.dockingMode = 2;
                }
                const {
                    data: { channelList = [] },
                } = await initIcnChannelApi(params);
                return channelList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    const channelId = Form.useWatch('channelId', limitForm);

    return (
        <Fragment>
            <Modal
                title={title}
                visible={showModal}
                onOk={onOk}
                width={800}
                onCancel={() => initRef?.current.close()}
                maskClosable={false}
            >
                {isLimit && (
                    <Form form={limitForm} name="limitForm" {...limitFormLayout}>
                        <Row>
                            {isChannelRule ? (
                                <Col span={8}>
                                    <Form.Item
                                        label="渠道简称"
                                        name="channelId"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请先选择企业',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="请选择"
                                            allowClear
                                            showSearch
                                            filterOption={(input, option) =>
                                                (option?.children ?? '')
                                                    .toLowerCase()
                                                    .includes(input.toLowerCase())
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    operForm.setFieldsValue({ rangeType: 'all' });
                                                }
                                            }}
                                        >
                                            {commonChannelList?.map((item) => {
                                                return (
                                                    <Select.Option
                                                        value={item.channelId}
                                                        key={item.channelId}
                                                    >
                                                        {item.channelName}
                                                    </Select.Option>
                                                );
                                            })}
                                        </Select>
                                    </Form.Item>
                                </Col>
                            ) : (
                                <Fragment>
                                    <Col span={8}>
                                        <OperSelectItem
                                            placeholder="不限"
                                            label="运营商"
                                            name="buildIds"
                                            validateTrigger={['onChange', 'onBlur']}
                                            operatorList={operatorList}
                                            mode="multiple"
                                            initialValue={
                                                (currentUser?.operId?.length &&
                                                    currentUser.operId) ||
                                                undefined
                                            }
                                            limitOperIds={limitOperIds}
                                            maxTagCount={maxTagCount}
                                            maxTagPlaceholder={maxTagPlaceholder}
                                            cooperationPlatform={cooperationPlatform}
                                        />
                                    </Col>
                                    <Col span={8}>
                                        <FormItem
                                            label="是否开放"
                                            name="openFlag"
                                            valuePropName="checked"
                                        >
                                            <Switch checkedChildren="是" unCheckedChildren="否" />
                                        </FormItem>
                                    </Col>
                                </Fragment>
                            )}
                        </Row>
                    </Form>
                )}
                <Form form={operForm} name="operForm">
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.stationList !== allStations
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            return (
                                <Fragment>
                                    <Space>
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                if (isChannelRule && isEmpty(channelId)) {
                                                    message.error('请先选择企业');
                                                    return;
                                                }
                                                importModalRef.current.show();
                                            }}
                                        >
                                            <PlusOutlined />
                                            表格导入
                                        </Button>
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                if (isChannelRule && isEmpty(channelId)) {
                                                    message.error('请先选择企业');
                                                    return;
                                                }
                                                toggleShowTextModal(true);
                                                textForm.resetFields();
                                            }}
                                        >
                                            <PlusOutlined />
                                            文本导入
                                        </Button>
                                    </Space>
                                    {(allStations?.length && (
                                        <div style={{ display: 'flex', margin: '10px 0' }}>
                                            <Space>
                                                <FormItem noStyle name="searchKey">
                                                    <Input
                                                        placeholder="请输入场站进行查询"
                                                        allowClear
                                                    />
                                                </FormItem>
                                                <Button
                                                    type="primary"
                                                    onClick={() => {
                                                        const key =
                                                            operForm.getFieldValue('searchKey');
                                                        updateSearchKey(key);
                                                        changePageNum(1);
                                                        updateSelectedRowKeys([]);
                                                    }}
                                                >
                                                    搜索
                                                </Button>
                                            </Space>
                                            <Button
                                                type="primary"
                                                style={{ marginRight: 0, marginLeft: 'auto' }}
                                                onClick={exportFormEvent}
                                            >
                                                导出
                                            </Button>
                                        </div>
                                    )) ||
                                        null}

                                    {(allStations?.length && (
                                        <Fragment>
                                            <p>
                                                <Popover
                                                    content={
                                                        <div>
                                                            <Space>
                                                                <Button
                                                                    type="primary"
                                                                    onClick={deleteSelectStations}
                                                                    disabled={!hasSelectedStations}
                                                                >
                                                                    删除选中项
                                                                </Button>
                                                                <Button
                                                                    danger
                                                                    onClick={() => {
                                                                        message.success(
                                                                            `成功删除${
                                                                                unmatchStations?.length ||
                                                                                0
                                                                            }条数据`,
                                                                        );
                                                                        updateUnmatchStations([]);
                                                                    }}
                                                                    disabled={!hasUnmatchStations}
                                                                >
                                                                    删除未匹配场站
                                                                </Button>
                                                                <Button
                                                                    type="primary"
                                                                    onClick={deleteAlreadyStations}
                                                                    disabled={!hasAlreadyStations}
                                                                >
                                                                    删除已导入场站
                                                                </Button>
                                                            </Space>
                                                        </div>
                                                    }
                                                    title="批量操作"
                                                    trigger="click"
                                                >
                                                    <Button>批量操作</Button>
                                                </Popover>
                                            </p>
                                            <TablePro
                                                scroll={{ x: 'max-content' }}
                                                rowKey={'readId'}
                                                dataSource={showStations}
                                                onChange={changePageInfo}
                                                pagination={{
                                                    current: pageNum,
                                                    total: showStations?.length,
                                                    pageSize,
                                                    showSizeChanger: true,
                                                    showQuickJumper: true,
                                                    showTotal: (total) => `共 ${total} 条`,
                                                }}
                                                expandable={{ defaultExpandAllRows: true }}
                                                columns={stationColumns}
                                                rowSelection={{
                                                    selectedRowKeys,
                                                    onChange: onSelectChange,
                                                }}
                                            />
                                        </Fragment>
                                    )) ||
                                        null}
                                </Fragment>
                            );
                        }}
                    </FormItem>
                    {(closeRemark && (
                        <FormItem label="备注" name="closeRemark" style={{ marginTop: '20px' }}>
                            <TextArea placeholder="请输入" rows={4} maxLength={200} showCount />
                        </FormItem>
                    )) ||
                        null}
                </Form>

                <ImportModal
                    title="批量导入场站"
                    downLoadPath="/aliMini/xdt/static/excel/场站导入模板.xls"
                    onUpload={async (formData, callback) => {
                        formDataExt(formData);

                        const { ret, data: matchstationList } = await importApi(formData);

                        callback &&
                            callback({
                                ret: ret == 200 ? 'suc' : 'false',
                                msg: ret == 200 ? '导入成功' : '导入失败',
                            });
                        importSuccess(matchstationList);
                    }}
                    ref={importModalRef}
                    surfix={['.xls']}
                    modelExtra={importExtra}
                />
            </Modal>

            <Modal
                title="文本导入"
                width={400}
                visible={showTextModal}
                onCancel={() => toggleShowTextModal(false)}
                onOk={() => {
                    updateTextUploading(true);
                    textForm
                        .validateFields()
                        .then(async (params) => {
                            const formData = new FormData();
                            for (const key in params) {
                                formData.append(key, params[key]);
                            }
                            formDataExt(formData);

                            const { data: matchstationList } = await importApi(formData);
                            importSuccess(matchstationList);
                            updateTextUploading(false);
                            toggleShowTextModal(false);
                            message.success('导入成功');
                        })
                        .catch((e) => {
                            updateTextUploading(false);
                        });
                }}
                maskClosable={false}
            >
                <Form form={textForm} name="textForm">
                    <FormItem label="导入方式" name="importType" initialValue={ImportTypes.Name}>
                        <Radio.Group
                            onChange={() => {
                                textForm.setFieldsValue({
                                    stationNames: '',
                                    stationIds: '',
                                });
                            }}
                        >
                            <Radio value={ImportTypes.Name}>场站名称</Radio>
                            <Radio value={ImportTypes.ID}>场站Id</Radio>
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        noStyle
                        shouldUpdate={(pre, after) => pre.importType != after.importType}
                    >
                        {({ getFieldValue }) => {
                            const importType = getFieldValue('importType');
                            let placeholder = '输入场站名称，回车间隔';
                            let keyName = 'stationNames';
                            if (importType === ImportTypes.ID) {
                                keyName = 'stationIds';
                                placeholder = '输入场站ID，回车间隔';
                            }
                            return (
                                <FormItem
                                    label="输入文本"
                                    name={keyName}
                                    rules={[{ required: true, message: placeholder }]}
                                >
                                    <TextArea placeholder={placeholder} rows={4} />
                                </FormItem>
                            );
                        }}
                    </FormItem>
                </Form>
            </Modal>

            <Modal
                title={`本次${title}将添加${addEnabledStations.length || 0}个场站`}
                width={650}
                visible={showConfirmModal}
                onCancel={() => toggleConfirmModal(false)}
                onOk={() => {
                    toggleConfirmModal(false);
                    onConfirm && onConfirm(addEnabledStations);
                    initRef?.current.close();
                }}
                maskClosable={false}
            >
                {(finalDoubleMatecedStations.some(
                    (item) => item.matchFlag == MATCH_TYPES.DOUBLEMACHED,
                ) && (
                    <>
                        <Title level={5}>
                            以下
                            <span style={{ color: '#ff9901' }}>重名场站</span>
                            经确认后可继续添加：
                        </Title>
                        <Text style={{ whiteSpace: 'pre' }}>
                            {finalDoubleMatecedStations
                                .filter((item) => item.matchFlag == MATCH_TYPES.DOUBLEMACHED)
                                .map((item) => `${item.stationName}（运营商：${item.buildName}）`)
                                .join('\n')}
                        </Text>
                    </>
                )) ||
                    null}

                {(addStopStations?.length && (
                    <>
                        <Title level={5}>
                            以下场站此前已添加过，将
                            <span style={{ color: '#ff9901' }}>不再添加</span>：
                        </Title>
                        <Text>{addStopStations?.join('、')}</Text>
                    </>
                )) ||
                    null}

                {(unmatchStations?.length && (
                    <Title level={5}>
                        {`另有${unmatchStations.length}个未匹配场站，将`}
                        <span style={{ color: '#ff9901' }}>自动忽略</span>。
                    </Title>
                )) ||
                    null}
            </Modal>
        </Fragment>
    );
};

OperGroupImportModal.propTypes = {
    dispatch: PropTypes.func.isRequired,
    // global: PropTypes.object.isRequired,
};

export default connect(({ global }) => ({
    global,
}))(OperGroupImportModal);
