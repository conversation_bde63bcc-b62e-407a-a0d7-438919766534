import { SketchPicker } from 'react-color';
import { Form, Input, Popover } from 'antd';
import { BgColorsOutlined } from '@ant-design/icons';
const ColorItem = (props) => {
    const { value, onChange, placeholder, disabled, ...extendProps } = props;
    const closeAfter = <BgColorsOutlined />;
    const handleChangeComplete = (colorValue) => {
        const value = colorValue.hex || colorValue.rgb;
        onChange && onChange(value);
    };
    const changeColorEvent = (value) => {
        onChange && onChange(value);
    };
    return (
        <Popover
            overlayClassName="selectColorPopover"
            placement="bottomLeft"
            content={<SketchPicker color={value} onChangeComplete={handleChangeComplete} />}
            trigger="click"
        >
            <Input
                value={value}
                placeholder={placeholder}
                autoComplete="off"
                onChange={(e) => changeColorEvent(e.target.value)}
                addonAfter={closeAfter}
                // onFocus={() => {
                //     togglePicker(true);
                // }}
                disabled={disabled}
                {...extendProps}
            />
        </Popover>
    );
};
export default ColorItem;
