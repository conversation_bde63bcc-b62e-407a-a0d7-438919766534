import {
    Fragment,
    useEffect,
    useState,
    useImperativeHandle,
    useMemo,
    forwardRef,
    useRef,
    useCallback,
} from 'react';
import { Upload, message, Button, Space, Tooltip } from 'antd';

import { PlusOutlined, LoadingOutlined, UploadOutlined } from '@ant-design/icons';

import { uploadMngFile } from '@/services/CommonApi';
import ThumbnailVideo from '@/components/ShowPicture/ThumbnailVideo';
import styles from './index.less';

import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { copyObjectCommon } from '@/utils/utils';
const type = 'DragableUploadList';

const DragableUploadListItem = ({ originNode, moveRow, file, fileList, disabled }) => {
    const ref = useRef(null);
    const index = fileList.indexOf(file);
    const [{ isOver, dropClassName }, drop] = useDrop({
        accept: type,
        collect: (monitor) => {
            const { index: dragIndex } = monitor.getItem() || {};
            if (dragIndex === index) {
                return {};
            }
            return {
                isOver: monitor.isOver(),
                dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
            };
        },
        drop: (item) => {
            moveRow(item.index, index);
        },
    });
    const [, drag] = useDrag({
        type,
        item: {
            index,
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });
    drop(drag(ref));
    const errorNode = <Tooltip title="Upload Error">{originNode.props.children}</Tooltip>;
    if (disabled) {
        return originNode;
    }
    return (
        <div
            ref={ref}
            className={`ant-upload-draggable-list-item ${isOver ? dropClassName : ''}`}
            style={{
                cursor: 'move',
            }}
        >
            {file.status === 'error' ? errorNode : originNode}
        </div>
    );
};

//新建组件优化老index组件在formlist开发成本高的问题

// 上传图片按钮
const UploadButton = ({ uploading, showUploderText }) => (
    <div>
        {uploading ? <LoadingOutlined /> : <PlusOutlined />}
        {(showUploderText && <div className="ant-upload-text">上传视频</div>) || null}
    </div>
);

const UpLoadImgItem = (props, ref) => {
    const {
        value,
        onChange,
        filePath, // 如果是嵌套from，由此路径赋值
        disabled,
        uploadData = {
            contentType: '03',
            contRemrk: 'eplainVedio',
            relaTable: 'mg_oper_mkt_instruction',
        },
        relaId,
        sizeInfo = {
            size: 1024 * 50,
        },
        showPlaceholder = true, // 控制底部提示语显隐
        showUploderText = true, // 控制添加按钮上传文字显隐
        placeholder,
        multiple = false,
        maxCount = 5,
        isBar,
        ...otherParams
    } = props;

    const [imageUrl, changeImageUrl] = useState(); // 上传文件
    const [imageList, updateImageList] = useState([]);
    const [uploading, changeUploading] = useState(false); // 上传加载

    useEffect(() => {
        if (multiple && value instanceof Array) {
            updateImageList(value.map((ele) => ({ url: ele, uid: ele, status: 'done' })));
        } else {
            changeImageUrl(value);
        }
    }, [value]);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {},
        init: (url) => {
            if (multiple) {
                for (const item of url) {
                    handleUploadChange(item);
                    updateImageList([...imageList, { url: item, uid: item, status: 'done' }]);
                }
            } else {
                changeImageUrl(url);
                updateImageList([...imageList, { url: url, uid: url, status: 'done' }]);
            }
        },
    }));

    const tipName = '视频';

    const handleUploadChange = (imgUrl) => {
        changeImageUrl(imgUrl);

        if (multiple) {
            const newImages = [...imageList, { url: imgUrl, uid: imgUrl, status: 'done' }];
            onChange && onChange(newImages.map((ele) => ele.url));
        } else {
            onChange && onChange(imgUrl);
        }
    };

    const handleRemoveEvent = (file) => {
        let list = imageList.filter((ele) => ele.uid !== file.uid);
        updateImageList(list);

        onChange && onChange(list.map((ele) => ele.url));
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isVedio = [
                'video/mp4',
                // , 'video/x-msvideo', 'video/x-ms-wmv'
            ].includes(file.type);
            if (!isVedio) {
                message.error(`${tipName}格式错误!`);
                // rej();
                return false;
            }
            const isLt2M = file.size / 1024 < sizeInfo.size;
            if (!isLt2M) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}mb`;
                } else {
                    sizeNum = `${sizeInfo.size}kb`;
                }
                message.error(`${tipName}不大于 ${sizeNum}!`);
                // rej();
                return false;
            }

            res();
        });

    const uploadProps = {
        name: 'avatar',
        className: styles['avatar-uploader'],
        showUploadList: multiple ? true : false,
        data: uploadData,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const params = {
                    ...data,
                };

                if (relaId) {
                    params.relaId = relaId;
                }
                const formData = new FormData();
                if (params) {
                    Object.keys(params).forEach((key) => {
                        formData.append(key, params[key]);
                    });
                }
                formData.append(data.contRemrk, file);

                changeUploading(true);
                const {
                    data: { filePath: linePath, fileId },
                } = await uploadMngFile(formData);
                onSuccess(linePath);
            } catch (error) {
            } finally {
                changeUploading(false);
            }

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType: isBar ? undefined : 'picture-card',
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };

    const placeholderMessage = useMemo(() => {
        if (placeholder) {
            return placeholder;
        }
        let str = '';

        let sizeNum = '';
        const sizeRadio = Math.floor(sizeInfo.size / 1024);
        if (sizeRadio >= 1) {
            sizeNum = `${sizeRadio}mb`;
        } else {
            sizeNum = `${sizeInfo.size}kb`;
        }
        str += `格式支持MP4,大小不得超过${sizeNum}，MP4格式为H.264编码`;
        return str;
    }, [placeholder, sizeInfo]);

    const uploadRender = useMemo(() => {
        if (multiple) {
            if (imageList?.length >= maxCount || disabled) {
                return null;
            }
            return <UploadButton uploading={uploading} showUploderText={showUploderText} />;
        } else {
            if (imageUrl) {
                return (
                    <ThumbnailVideo
                        url={imageUrl}
                        disabled={disabled}
                        delEvent={() => {
                            onChange && onChange(null);
                            changeImageUrl(undefined);
                        }}
                    />
                );
            } else if (isBar && !disabled) {
                return <Button icon={<UploadOutlined />}>上传视频</Button>;
            } else if (!disabled) {
                return <UploadButton uploading={uploading} showUploderText={showUploderText} />;
            }
        }
        return null;
    }, [multiple, imageUrl, imageList, disabled, uploading]);

    const moveRow = useCallback(
        (dragIndex, hoverIndex) => {
            const newList = copyObjectCommon(imageList);
            const temp = newList[dragIndex];
            newList[dragIndex] = newList[hoverIndex];
            newList[hoverIndex] = temp;
            updateImageList(newList);
            onChange && onChange(newList.map((ele) => ele.url));
        },
        [imageList],
    );

    return (
        <Space direction="vertical">
            <Upload
                {...uploadProps}
                disabled={disabled}
                fileList={(multiple && imageList) || null}
                onRemove={handleRemoveEvent}
                {...otherParams}
                itemRender={(originNode, file, fileList, { download, preview, remove }) => {
                    return (
                        <ThumbnailVideo
                            url={file.url}
                            disabled={disabled}
                            delEvent={() => {
                                remove(file);
                            }}
                        />
                    );
                }}
            >
                {uploadRender}
            </Upload>
            {(showPlaceholder && !disabled && (
                <span className={styles['mark-text']}>{placeholderMessage}</span>
            )) ||
                null}
        </Space>
    );
};

export default forwardRef(UpLoadImgItem);
