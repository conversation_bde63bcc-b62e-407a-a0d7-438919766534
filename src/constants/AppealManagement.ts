/**
 * 申诉管理相关常量
 */

/**
 * 申诉状态枚举
 */
export enum AppealStatusEnum {
    PENDING = '01',      // 待处理
    PROCESSING = '02',   // 处理中
    PROCESSED = '03',    // 已处理
    REJECTED = '04',     // 已拒绝
    CLOSED = '05',       // 已关闭
}

/**
 * 申诉状态标题映射
 */
export const AppealStatusTitle = {
    [AppealStatusEnum.PENDING]: '待处理',
    [AppealStatusEnum.PROCESSING]: '处理中',
    [AppealStatusEnum.PROCESSED]: '已处理',
    [AppealStatusEnum.REJECTED]: '已拒绝',
    [AppealStatusEnum.CLOSED]: '已关闭',
};

/**
 * 申诉状态选项
 */
export const AppealStatusOptions = [
    {
        label: '全部',
        value: '',
    },
    {
        label: AppealStatusTitle[AppealStatusEnum.PENDING],
        value: AppealStatusEnum.PENDING,
    },
    {
        label: AppealStatusTitle[AppealStatusEnum.PROCESSING],
        value: AppealStatusEnum.PROCESSING,
    },
    {
        label: AppealStatusTitle[AppealStatusEnum.PROCESSED],
        value: AppealStatusEnum.PROCESSED,
    },
    {
        label: AppealStatusTitle[AppealStatusEnum.REJECTED],
        value: AppealStatusEnum.REJECTED,
    },
    {
        label: AppealStatusTitle[AppealStatusEnum.CLOSED],
        value: AppealStatusEnum.CLOSED,
    },
];

/**
 * 申诉类型枚举
 */
export enum AppealTypeEnum {
    SERVICE_QUALITY = '01',    // 服务质量
    BILLING_DISPUTE = '02',    // 计费争议
    EQUIPMENT_FAULT = '03',    // 设备故障
    REFUND_REQUEST = '04',     // 退款申请
    OTHER = '99',              // 其他
}

/**
 * 申诉类型标题映射
 */
export const AppealTypeTitle = {
    [AppealTypeEnum.SERVICE_QUALITY]: '服务质量',
    [AppealTypeEnum.BILLING_DISPUTE]: '计费争议',
    [AppealTypeEnum.EQUIPMENT_FAULT]: '设备故障',
    [AppealTypeEnum.REFUND_REQUEST]: '退款申请',
    [AppealTypeEnum.OTHER]: '其他',
};

/**
 * 申诉类型选项
 */
export const AppealTypeOptions = [
    {
        label: '全部',
        value: '',
    },
    {
        label: AppealTypeTitle[AppealTypeEnum.SERVICE_QUALITY],
        value: AppealTypeEnum.SERVICE_QUALITY,
    },
    {
        label: AppealTypeTitle[AppealTypeEnum.BILLING_DISPUTE],
        value: AppealTypeEnum.BILLING_DISPUTE,
    },
    {
        label: AppealTypeTitle[AppealTypeEnum.EQUIPMENT_FAULT],
        value: AppealTypeEnum.EQUIPMENT_FAULT,
    },
    {
        label: AppealTypeTitle[AppealTypeEnum.REFUND_REQUEST],
        value: AppealTypeEnum.REFUND_REQUEST,
    },
    {
        label: AppealTypeTitle[AppealTypeEnum.OTHER],
        value: AppealTypeEnum.OTHER,
    },
];

/**
 * 申诉优先级枚举
 */
export enum AppealPriorityEnum {
    LOW = '01',      // 低
    MEDIUM = '02',   // 中
    HIGH = '03',     // 高
    URGENT = '04',   // 紧急
}

/**
 * 申诉优先级标题映射
 */
export const AppealPriorityTitle = {
    [AppealPriorityEnum.LOW]: '低',
    [AppealPriorityEnum.MEDIUM]: '中',
    [AppealPriorityEnum.HIGH]: '高',
    [AppealPriorityEnum.URGENT]: '紧急',
};

/**
 * 申诉优先级选项
 */
export const AppealPriorityOptions = [
    {
        label: '全部',
        value: '',
    },
    {
        label: AppealPriorityTitle[AppealPriorityEnum.LOW],
        value: AppealPriorityEnum.LOW,
    },
    {
        label: AppealPriorityTitle[AppealPriorityEnum.MEDIUM],
        value: AppealPriorityEnum.MEDIUM,
    },
    {
        label: AppealPriorityTitle[AppealPriorityEnum.HIGH],
        value: AppealPriorityEnum.HIGH,
    },
    {
        label: AppealPriorityTitle[AppealPriorityEnum.URGENT],
        value: AppealPriorityEnum.URGENT,
    },
];
